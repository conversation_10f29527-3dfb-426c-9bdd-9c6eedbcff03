# Design to Code - Agent Guidelines

## Resumo do Projeto

Design to Code é um sistema inteligente de geração de código que converte designs do Figma em código Angular/React usando Design Systems. O projeto possui arquitetura modular com separação clara de responsabilidades entre scraping, extração e geração de código.

## Arquitetura do Sistema

### Estrutura Principal

```
src/
├── design_system/          # Sistema de Design System
│   ├── scrapers/          # Scrapers e extratores
│   │   ├── storybook_scraper.py      # Leitura de URLs e menus
│   │   ├── storybook_extractor.py    # Extração de conteúdo
│   │   └── storybook_version_detector.py
│   ├── parsers/           # Parsers especializados
│   │   ├── base_parser.py
│   │   ├── classes_parser.py
│   │   ├── code_processor.py
│   │   ├── content_processor.py
│   │   ├── image_parser.py
│   │   └── table_processor.py
│   ├── parsers_json/      # Parsers JSON (alternativos)
│   │   ├── components_parser.py
│   │   ├── templates_parser.py
│   │   ├── classes_parser.py
│   │   ├── images_parser.py
│   │   ├── main_docs_parser.py
│   │   ├── markdown_generator.py
│   │   └── storybook_parser.py
│   └── utils/             # Utilitários
├── figma/                 # Integração com Figma
│   ├── figma_api_client.py
│   ├── figma_discovery.py
│   ├── figma_normalized_extractor.py
│   ├── figma_type_mapper.py
│   └── models/
│       └── component_schema.py
├── generators/            # Geradores de código
│   ├── component_generator.py
│   ├── design_system_mapper.py
│   ├── figma_reader.py
│   ├── wrapper_generator.py
│   ├── prompts.yaml
│   └── utils/
│       ├── angular_structure_organizer.py
│       ├── figma_data_processor.py
│       ├── element_order_validator.py
│       ├── validation_engine.py
│       └── ai_analysis_helper.py
├── scripts/              # Scripts principais
│   ├── figma_extract.py
│   ├── storybook_extract.py
│   └── code_generator.py
└── utils/                # Utilitários gerais
    ├── config/
    │   └── config_loader.py
    ├── logging/
    │   └── logging_config.py
    ├── patterns/
    │   ├── pattern_detector.py
    │   └── smart_pattern_detector.py
    ├── file/
    │   └── file_utils.py
    ├── angular_utils.py
    ├── color_mapper.py
    ├── component_visibility_resolver.py
    ├── discover_pages.py
    ├── figma_utils.py
    ├── interactive_utils.py
    ├── structural_analyzer.py
    └── table_parser.py
```

### Fluxo de Dados

1. **Extração do Figma**: `figma_extract.py` → `figma_normalized_extractor.py` → JSONs estruturados
2. **Extração do Design System**: `storybook_scraper.py` → `storybook_extractor.py` → Markdown/JSON
3. **Geração de Código**: JSONs + Markdown → `code_generator.py` → Código Angular/React
4. **Geração de Wrappers**: Metadados → `wrapper_generator.py` → Componentes wrapper

### Fluxo de Geração Atualizado

1. **Leitura do Figma** - Carrega JSON principal + componentes filhos
2. **Mapeamento Design System** - BM25 + IA para correspondências
3. **Geração Angular Sequencial**
   - **Validação de Ordem**: Aplica ordem original do Figma
   - **HTML**: Gera usando templates do Design System
   - **TypeScript**: Gera com contexto do HTML
   - **SCSS**: Gera com contexto completo HTML+TS
   - **Validação Cruzada**: Verifica consistência entre arquivos
   - **Correções Automáticas**: Fix de imports, métodos, sintaxe
4. **Wrapper Generation** - Detecta e conecta componentes filhos
5. **Reorganização Angular** - Usa copytree para evitar corrupção

## Padrões e Convenções

### 1. Estrutura de Arquivos

- **Scripts**: Sempre em `src/scripts/` com nomes descritivos
- **Parsers**: Organizados por tipo em `src/design_system/parsers/`
- **Configuração**: YAML em `project_config.yaml` e `.env`
- **Dados**: Estruturados em `data/` com subdiretórios específicos

### 2. Nomenclatura

- **Classes**: PascalCase (ex: `StorybookScraper`, `FigmaExtractor`)
- **Funções**: snake_case (ex: `extract_components`, `save_markdown`)
- **Variáveis**: snake_case (ex: `component_urls`, `extraction_time`)
- **Constantes**: UPPER_SNAKE_CASE (ex: `MAX_RETRIES`, `DEFAULT_TIMEOUT`)

### 3. Imports

- **Sempre usar caminhos completos**: `from src.design_system.scrapers import StorybookScraper`
- **Imports relativos**: Evitar `from . import` quando possível
- **Organização**: Standard library → Third party → Local imports

### 4. Configuração

```python
# Sempre usar ConfigLoader para configurações
from src.utils.config import ConfigLoader
config_loader = ConfigLoader(config_file)
ds_config = config_loader.get_design_system_config()
```

### 5. Logging

```python
# Sempre configurar logging apropriado
from src.utils.logging import setup_script_logging
logger = setup_script_logging("script_name", verbose=True)
```

## Componentes Principais

### 1. StorybookScraper

**Responsabilidade**: Apenas leitura de URLs e abertura de menus
**Localização**: `src/design_system/scrapers/storybook_scraper.py`
**Funcionalidades**:

- Navegação para Storybook
- Expansão automática de menus
- Extração de URLs dos componentes
- Filtros para componentes não desejados
- Priorização por categoria

### 2. StorybookExtractor

**Responsabilidade**: Extração de conteúdo usando parsers especializados
**Localização**: `src/design_system/scrapers/storybook_extractor.py`
**Funcionalidades**:

- Processamento de URLs extraídas
- Parsers específicos por tipo (templates, classes, images)
- Geração de Markdown ou JSON estruturado
- Cache inteligente com verificação de versão
- Detecção automática de mudanças no Storybook

### 3. Parsers Especializados

**Localização**: `src/design_system/parsers/` e `src/design_system/parsers_json/`
**Tipos**:

- `base_parser.py`: Parser base para todos os tipos
- `classes_parser.py`: Classes/utilities
- `code_processor.py`: Processamento de código
- `content_processor.py`: Processamento de conteúdo
- `image_parser.py`: Processamento de imagens
- `table_processor.py`: Processamento de tabelas
- `components_parser.py`: Componentes principais
- `templates_parser.py`: Templates
- `images_parser.py`: Images (icons, logos, etc.)
- `main_docs_parser.py`: Documentação principal
- `markdown_generator.py`: Geração de markdown
- `storybook_parser.py`: Parser específico do Storybook

### 4. FigmaNormalizedExtractor

**Localização**: `src/figma/figma_normalized_extractor.py`
**Funcionalidades**:

- Extração normalizada de componentes
- Filtro de visibilidade automático
- Detecção de ícones
- Estrutura hierárquica organizada
- Processamento de webcomponents
- Download automático de imagem do Figma

### 5. FigmaReader

**Localização**: `src/generators/figma_reader.py`
**Responsabilidade**: Leitura e processamento de dados do Figma
**Funcionalidades**:

- Leitura de extrações do Figma
- Processamento de dados estruturados
- Preparação para mapeamento com Design System
- Detecção de componentes wrapper
- Geração de metadados para wrappers

### 6. DesignSystemMapper

**Localização**: `src/generators/design_system_mapper.py`
**Responsabilidade**: Mapeamento usando BM25 + IA
**Funcionalidades**:

- Busca BM25 para casos simples e rápidos
- IA como fallback para casos complexos
- Mapeamento automático de propriedades
- Tratamento de erros detalhado
- Índice BM25 construído automaticamente
- Sistema de busca em múltiplas camadas

### 7. ComponentGenerator

**Localização**: `src/generators/component_generator.py`
**Responsabilidade**: Geração final de código Angular
**Funcionalidades**:

- Geração de HTML, TypeScript e SCSS
- Aplicação de boas práticas Angular
- Uso de templateUrl e styleUrls
- Outputs sem prefixo "on"
- Integração com Flow API
- Mapeamento inteligente de propriedades
- Detecção de componentes wrapper
- Geração de metadados para wrappers

### 8. WrapperGenerator

**Localização**: `src/generators/wrapper_generator.py`
**Responsabilidade**: Geração de componentes wrapper via IA
**Funcionalidades**:

- Geração de wrappers a partir de metadados
- Análise de componentes filhos
- Geração de HTML, TypeScript e SCSS para wrappers
- Integração com Flow API
- Fallbacks para casos de erro

## ⭐ Melhorias Recentes - Sistema de Geração Sequencial

### 1. Novos Utilitários de Validação

#### Component Generator - Fluxo Sequencial Consolidado (`src/generators/component_generator.py`)

- **Geração sequencial consolidada**: HTML → TypeScript → SCSS
- Cada etapa recebe contexto da anterior
- Garante consistência entre arquivos
- Integra validações de ordem

#### Element Order Validator (`src/generators/utils/element_order_validator.py`)

- Valida ordem dos elementos baseada no Figma original
- Previne duplicação entre wrapper e componentes filhos
- Extrai ordem correta de botões dos dados do Figma

#### Figma Data Processor (`src/generators/utils/figma_data_processor.py`)

- **MELHORADO**: Extração de textos reais vs placeholders
- Sistema de prioridade para conteúdo real
- Preserva textos exatos do Figma

#### Validation Engine (`src/generators/utils/validation_engine.py`)

- Correções automáticas de imports, métodos, sintaxe
- Validação cruzada entre HTML e TypeScript
- Detecção e correção de inconsistências

### 2. Validações Implementadas

#### Validação de Textos

- **Extrai textos exatos** dos dados do Figma
- **Prioriza conteúdo real** sobre placeholders
- **Nunca inventa** títulos ou labels

#### Validação de Ordem

- **Mantém ordem original** dos elementos conforme Figma
- **Extrai ordem correta** dos botões automaticamente
- **Previne duplicação** entre wrapper e filhos

#### Validação de Classes CSS

- **Usa apenas classes válidas** do Design System
- **Proíbe classes inexistentes** (brad-col-\*, brad-row)
- **Força uso de brad-flex** para layouts

#### Validação Cross-File

- **Métodos HTML existem no TypeScript**
- **Imports necessários estão presentes**
- **Consistência entre HTML, TS e SCSS**

### 3. Análise Visual com Imagens

- **Download durante extração**: Imagens PNG baixadas automaticamente via API do Figma
- **Armazenamento local**: Salvas como `componente.png` junto com dados JSON
- **Análise contextual**: IA usa imagem para validar dados e identificar elementos visuais
- **Instruções específicas**: Prompts incluem orientações detalhadas sobre uso da imagem

## Configurações Importantes

### 1. project_config.yaml

```yaml
figma:
  file_key: "seu_file_key"
  token: "seu_token"
  max_exploration_depth: 7

design_system:
  name: "Liquid Bradesco"
  slug: "liquid_bradesco"
  storybook:
    base_url: "https://banco.bradesco/cdn/design-system/dist"
    version: "latest"
    auto_detect_version: true
    components_path: "/?path=/docs/designsystem-components-"
    docs_path: "/?path=/docs/design-system-"
  cache:
    update_interval_hours: 48
    force_update: false
    cache_dir: "data/design_system"

generation:
  framework: "angular"
  generate_angular_components: true
  include_typescript: true
  include_scss: true
  generate_mapping_reports: true

output:
  logs_path: "data/logs"
  figma_extraction_path: "data/figma_extraction"
  design_system_path: "data/design_system"
  generate_path: "data/output_generated"
```

### 2. .env

```bash
# Configurações de ambiente
FLOW_API_URL=
FLOW_API_TOKEN=
```

## Padrões de Código

### 1. Context Managers

```python
# Sempre usar context managers para recursos
with StorybookScraper(config) as scraper:
    urls = scraper.extract_component_urls()

with StorybookExtractor(config) as extractor:
    components = extractor.extract_components(urls)
```

### 2. Error Handling

```python
try:
    # Operação principal
    result = perform_operation()
except SpecificException as e:
    logger.error(f"Erro específico: {e}")
    # Fallback ou retry
except Exception as e:
    logger.error(f"Erro inesperado: {e}", exc_info=True)
    raise
```

### 3. Type Hints

```python
from typing import Dict, List, Any, Optional

def extract_components(urls: Dict[str, Any], limit: Optional[int] = None) -> Dict[str, Dict[str, Any]]:
    """Extrai componentes usando parsers específicos."""
    pass
```

### 4. Dataclasses

```python
from dataclasses import dataclass

@dataclass
class StorybookConfig:
    base_url: str
    output_dir: Path
    headless: bool = True
    timeout: int = 30
```

## Comandos e Scripts

### 1. run.sh (Interface Principal)

```bash
./run.sh setup           # Configuração inicial
./run.sh storybook       # Extração do Design System
./run.sh figma          # Extração do Figma
./run.sh generate       # Geração de código
./run.sh all            # Fluxo completo
./run.sh clean          # Limpeza de dados
```

### 2. Scripts Python Diretos

```bash
# Extração do Figma
python -m src.scripts.figma_extract

# Extração do Storybook
python -m src.scripts.storybook_extract --limit 10 --category components

# Geração de código
python -m src.scripts.code_generator --extraction data/figma_extraction/projeto/componente
```

## Estrutura de Dados

### 1. Componentes do Figma

```json
{
  "id": "component_id",
  "name": "Component Name",
  "type": "COMPONENT",
  "visible": true,
  "children": [...],
  "css": {
    "width": 100,
    "height": 50
  }
}
```

### 2. Componentes do Design System

```json
{
  "id": "component_id",
  "name": "Component Name",
  "description": "Component description",
  "properties": {...},
  "examples": [...],
  "component_type": "component",
  "extracted_at": "2024-01-01T00:00:00"
}
```

### 3. Mapeamento Inteligente

```json
{
  "component_name": "modal-vinculo",
  "figma_id": "figma_id",
  "mappings": [
    {
      "figma_webcomponent": {...},
      "design_system_component": {...},
      "confidence": 0.95,
      "mapping_reason": "busca BM25 (score: 0.850)",
      "properties_mapping": {...},
      "search_method": "bm25"
    }
  ],
  "metadata": {
    "total_webcomponents": 5,
    "mapped_components": 5,
    "failed_components": 0,
    "average_confidence": 0.92,
    "mapping_method": "bm25_plus_ai",
    "search_methods": {
      "bm25": 4,
      "ai": 1,
      "exact": 0,
      "pattern": 0,
      "error": 0
    }
  }
}
```

## Sistema de Mapeamento Inteligente

### 1. BM25Index

**Localização**: `src/generators/design_system_mapper.py`
**Funcionalidades**:

- Construção automática de índice BM25
- Tokenização inteligente de documentos
- Busca rápida com scores normalizados
- Fallback para busca simples se BM25 não disponível

### 2. Fluxo de Busca

```python
# 1. Correspondência exata (mais rápida)
ds_component, confidence, reason, method = self._find_exact_match(webcomponent_name, component_id)

# 2. Busca BM25 (rápida e eficiente)
if not ds_component:
    ds_component, confidence, reason, method = self._find_bm25_match(webcomponent_name)

# 3. Correspondência por padrões
if not ds_component:
    ds_component, confidence, reason, method = self._find_pattern_match(webcomponent_name)

# 4. IA como fallback (mais inteligente)
if not ds_component:
    ds_component, confidence, reason, method = self._find_ai_match(webcomponent_name, webcomponent)

# 5. ERRO - componente não encontrado (sem fallback genérico)
if not ds_component:
    raise ComponentNotFoundError(...)
```

### 3. Mapeamento de Propriedades

```python
# Mapeamento expandido de propriedades Figma → Design System
prop_mapping = {
    # Texto e conteúdo
    'Label': 'label',
    'Content': 'content',
    'Hint Text': 'placeholder',

    # Estados e controle
    'Is Disabled': 'disabled',
    'Is Focused': 'focused',
    'Is Hovered': 'hovered',

    # Validação
    'Validation': 'validation',
    'Required': 'required',
    'Error': 'error',

    # Ícones e imagens
    'Leading Icon': 'leadingIcon',
    'Trailing Icon': 'trailingIcon',

    # Layout e estilo
    'Size': 'size',
    'Type': 'type',
    'Variant': 'variant',

    # Acessibilidade
    'Aria Label': 'ariaLabel',
    'Role': 'role',
}
```

## Boas Práticas Angular

### 1. Convenções de Nomeação

#### Arquivos e Pastas Angular

- **Kebab-case** para arquivos e pastas de componentes
- **Snake_case** para dados internos do sistema
- **PascalCase** para classes TypeScript

```typescript
// ✅ Correto - kebab-case para arquivos
user-profile.component.ts
user-profile.component.html
user-profile.component.scss
user-profile/

// ✅ Correto - PascalCase para classes
export class UserProfileComponent { }

// ✅ Correto - snake_case para dados internos
component_name = "modal_cadastro"
normalized_name = "modal_cadastro_novo_contador_erro_generico"
```

#### Conversão Automática

O sistema converte automaticamente:

- **snake_case** (dados internos) → **kebab-case** (arquivos Angular)
- Exemplo: `modal_cadastro` → `modal-cadastro.component.ts`

### 2. Template Externo

```typescript
@Component({
  selector: 'app-modal-vinculo',
  templateUrl: './modal-vinculo.component.html',
  styleUrls: ['./modal-vinculo.component.scss']
})
```

### 3. Outputs Limpos

```typescript
// ✅ Correto - sem prefixo "on"
@Output() save = new EventEmitter<any>();
@Output() cancel = new EventEmitter<void>();

// ❌ Incorreto - com prefixo "on"
@Output() onSave = new EventEmitter<any>();
@Output() onCancel = new EventEmitter<void>();
```

### 4. Interfaces Tipadas

```typescript
interface ModalVinculoData {
  title: string;
  content: string;
  actions: ActionButton[];
}

interface ActionButton {
  label: string;
  type: "primary" | "secondary";
  disabled?: boolean;
}
```

### 5. Componentes Reutilizáveis

```typescript
export class ModalVinculoComponent {
  @Input() data: ModalVinculoData;
  @Input() loading = false;
  @Output() save = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<void>();

  constructor() {}

  onSave(): void {
    this.save.emit(this.data);
  }

  onCancel(): void {
    this.cancel.emit();
  }
}
```

## Sistema de Prompts

### 1. prompts.yaml

**Localização**: `src/generators/prompts.yaml`
**Funcionalidades**:

- Prompts centralizados para IA
- Prompts específicos por tipo (análise, mapeamento, geração)
- Configurações de sistema e diretrizes
- Prompts para HTML, TypeScript e SCSS

## Debugging e Troubleshooting

### 1. Logs

- **Logs de aplicação**: `data/logs/`
- **Configuração**: `setup_script_logging("script_name", verbose=True)`
- **Níveis**: DEBUG, INFO, WARNING, ERROR

### 2. Debug Flags

```bash
# Debug de URLs do Storybook
./run.sh storybook --debug-urls

# Teste rápido
./run.sh storybook-test

# Verbose mode
python -m src.scripts.storybook_extract --verbose

# Debug de mapeamento
python -m src.scripts.code_generator --extraction data/figma_extraction/projeto/componente --verbose
```

### 3. Cache e Limpeza

```bash
# Limpar cache
./run.sh clean

# Verificar versões
python -m src.scripts.storybook_extract --force-update
```

## Considerações de Performance

### 1. Selenium

- **Headless mode**: Sempre habilitado em produção
- **Timeouts**: Configuráveis via `project_config.yaml`
- **Retries**: Implementados para falhas temporárias

### 2. Cache

- **Versão do Storybook**: Detectada automaticamente
- **Evita re-extração**: Se versão não mudou
- **Cache local**: `data/cache/`

### 3. Filtros

- **Visibilidade**: Remove nodes invisíveis
- **Categorias**: Filtra por tipo de componente
- **Limites**: Controla quantidade de componentes

### 4. Busca Inteligente

- **BM25**: Para casos simples (80% dos casos)
- **IA Seletiva**: Apenas quando necessário (20% dos casos)
- **Fallbacks Inteligentes**: Sem componentes genéricos

## Sistema de Prompts

### 1. prompts.yaml

**Localização**: `src/generators/prompts.yaml`
**Funcionalidades**:

- Prompts centralizados para IA
- Prompts específicos por tipo (análise, mapeamento, geração)
- Configurações de sistema e diretrizes
- Prompts para HTML, TypeScript e SCSS

### 2. Tipos de Prompts

```yaml
system_prompts:
  component_analysis:
    role: "Você é um analista especialista em estruturas de design e componentes."
    mission: "Sua missão é analisar dados do Figma e identificar padrões, estrutura e requisitos para geração de código."

  design_system_mapping:
    role: "Você é um especialista em Design Systems e mapeamento de componentes."
    mission: "Sua missão é mapear componentes do Figma para componentes do Design System disponíveis."

  html_generation:
    role: "Você é um desenvolvedor Angular especialista em gerar componentes HTML."
    mission: "Sua missão é gerar HTML para um componente Angular baseado em dados do Figma pré-processados pela IA."

  typescript_generation:
    role: "Você é um desenvolvedor Angular especialista em gerar componentes TypeScript."
    mission: "Sua missão é gerar código TypeScript para um componente Angular baseado em dados do Figma pré-processados pela IA."

  scss_generation:
    role: "Você é um desenvolvedor Angular especialista em gerar estilos SCSS."
    mission: "Sua missão é gerar SCSS para um componente Angular baseado em dados do Figma pré-processados pela IA."

  wrapper_html_generation:
    role: "Você é um desenvolvedor Angular especialista em gerar componentes wrapper HTML."
    mission: "Sua missão é gerar HTML de componente wrapper Angular que referencia componentes filhos."

  wrapper_typescript_generation:
    role: "Você é um desenvolvedor Angular especialista em gerar componentes wrapper TypeScript."
    mission: "Sua missão é gerar TypeScript de componente wrapper Angular que referencia componentes filhos."

  wrapper_scss_generation:
    role: "Você é um desenvolvedor Angular especialista em gerar estilos para componentes wrapper."
    mission: "Sua missão é gerar SCSS de componente wrapper Angular baseado nos dados do Figma."
```

## Extensibilidade

### 1. Novos Parsers

```python
# Criar novo parser em src/design_system/parsers/
class NewParser(BaseParser):
    def parse_component(self, html: str, component_id: str) -> Dict[str, Any]:
        # Implementação do parser
        pass
```

### 2. Novos Scrapers

```python
# Criar novo scraper seguindo o padrão
class NewScraper:
    def __init__(self, config: Config):
        self.config = config

    def __enter__(self):
        # Setup
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Cleanup
        pass
```

### 3. Novos Scripts

```python
# Criar novo script em src/scripts/
def main():
    parser = argparse.ArgumentParser()
    # Argumentos
    args = parser.parse_args()

    # Lógica principal
    pass

if __name__ == "__main__":
    main()
```

## Regras Importantes

1. **Sempre usar context managers** para recursos (Selenium, arquivos)
2. **Configuração via ConfigLoader** nunca hardcoded
3. **Logging apropriado** em todas as operações
4. **Type hints** em todas as funções públicas
5. **Error handling** específico para cada tipo de erro
6. **Caminhos completos** com src. nos imports
7. **Quebra de funcionalidades** em arquivos separados para evitar classes gigantes
8. **Mapeamento de propriedades** entre Figma e Design System, NUNCA criar ou inventar
9. **Documentação** em português brasileiro
10. **Testes** para funcionalidades críticas
11. **Filtros apropriados** para reduzir dados desnecessários
12. **Mapeamento inteligente** com BM25 + IA como fallback
13. **Fallbacks inteligentes** para NUNCA criar dados ou componentes genéricos
14. **Boas práticas Angular** (templateUrl, styleUrls, Outputs limpos)
15. **Tratamento de erros detalhado** com informações de debug
16. **Convenções de nomeação** (kebab-case para arquivos, snake_case para dados internos, PascalCase para classes)
17. **Pasta utils** para funções auxiliares gerais e de cada módulo
18. **Prompts centralizados** em prompts.yaml para consistência
19. **Geração de wrappers** via WrapperGenerator para componentes complexos
20. **Flow API** para geração de código com IA
