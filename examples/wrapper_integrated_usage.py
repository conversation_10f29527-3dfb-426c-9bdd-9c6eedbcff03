#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Exemplo prático de uso da nova funcionalidade de geração integrada de wrappers.

Este exemplo mostra como integrar a nova funcionalidade no fluxo principal
de geração de componentes.
"""

import sys
from pathlib import Path

# Adicionar o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.generators.wrapper_generator import WrapperGenerator
from src.utils.logging import get_logger

logger = get_logger(__name__)

def example_integrated_wrapper_generation():
    """
    Exemplo de uso da geração integrada de wrappers.
    """
    logger.info("🚀 EXEMPLO: Geração Integrada de Wrappers")
    logger.info("=" * 50)
    
    # 1. Configurar o gerador
    config_path = "project_config.yaml"
    
    # Nota: Em uso real, configure seu flow_client aqui
    # flow_client = configure_your_flow_client()
    flow_client = None  # Para exemplo sem IA
    
    wrapper_generator = WrapperGenerator(config_path, flow_client)
    
    # 2. Definir diretório onde estão os metadados de wrapper
    output_dir = "data/output_generated/sat_contabilidade/modal_vinculo/angular/modal-vinculo"
    
    logger.info(f"📁 Procurando wrappers em: {output_dir}")
    
    # 3. Usar a nova funcionalidade integrada
    try:
        logger.info("🔄 Iniciando geração integrada...")
        
        # NOVA FUNCIONALIDADE: Geração integrada
        wrapper_generator.generate_wrappers_integrated_from_metadata(output_dir)
        
        logger.info("✅ Geração integrada concluída!")
        
    except Exception as e:
        logger.error(f"❌ Erro na geração integrada: {e}")

def example_comparison_old_vs_new():
    """
    Exemplo comparando a abordagem antiga vs nova.
    """
    logger.info("📊 COMPARAÇÃO: Antiga vs Nova Abordagem")
    logger.info("=" * 50)
    
    config_path = "project_config.yaml"
    flow_client = None  # Configure conforme necessário
    
    wrapper_generator = WrapperGenerator(config_path, flow_client)
    output_dir = "data/output_generated/exemplo"
    
    logger.info("🔄 ABORDAGEM ANTIGA:")
    logger.info("   - 3 chamadas separadas à IA")
    logger.info("   - HTML -> TypeScript -> SCSS")
    logger.info("   - Sem validação específica")
    
    try:
        # Abordagem antiga (mantida para compatibilidade)
        # wrapper_generator.generate_wrappers_from_metadata(output_dir)
        logger.info("   ✅ Funcionalidade antiga disponível")
    except Exception as e:
        logger.info(f"   ⚠️ Erro na abordagem antiga: {e}")
    
    logger.info("\n🆕 ABORDAGEM NOVA:")
    logger.info("   - 1 chamada integrada à IA")
    logger.info("   - HTML + TypeScript + SCSS juntos")
    logger.info("   - Validação específica para wrappers")
    
    try:
        # Abordagem nova (recomendada)
        # wrapper_generator.generate_wrappers_integrated_from_metadata(output_dir)
        logger.info("   ✅ Funcionalidade nova implementada")
    except Exception as e:
        logger.info(f"   ⚠️ Erro na abordagem nova: {e}")

def example_integration_in_main_flow():
    """
    Exemplo de como integrar no fluxo principal de geração.
    """
    logger.info("🔗 INTEGRAÇÃO NO FLUXO PRINCIPAL")
    logger.info("=" * 50)
    
    logger.info("📝 Para integrar no fluxo principal, modifique:")
    logger.info("")
    logger.info("1️⃣ No arquivo principal de geração:")
    logger.info("```python")
    logger.info("# Após gerar componentes individuais")
    logger.info("component_generator.generate_component_code(...)")
    logger.info("")
    logger.info("# Gerar wrappers com nova funcionalidade")
    logger.info("wrapper_generator = WrapperGenerator(config_path, flow_client)")
    logger.info("wrapper_generator.generate_wrappers_integrated_from_metadata(output_dir)")
    logger.info("```")
    logger.info("")
    logger.info("2️⃣ Ou criar um switch para escolher a abordagem:")
    logger.info("```python")
    logger.info("use_integrated_generation = True  # Flag de configuração")
    logger.info("")
    logger.info("if use_integrated_generation:")
    logger.info("    wrapper_generator.generate_wrappers_integrated_from_metadata(output_dir)")
    logger.info("else:")
    logger.info("    wrapper_generator.generate_wrappers_from_metadata(output_dir)")
    logger.info("```")

def example_prompt_structure():
    """
    Exemplo da estrutura dos novos prompts.
    """
    logger.info("📋 ESTRUTURA DOS NOVOS PROMPTS")
    logger.info("=" * 50)
    
    logger.info("1️⃣ PROMPT DE GERAÇÃO INTEGRADA:")
    logger.info("   📍 system_prompts.wrapper_integrated_generation")
    logger.info("   📍 user_prompts.wrapper_integrated_generation")
    logger.info("")
    logger.info("   Formato de resposta esperado:")
    logger.info("   ```html")
    logger.info("   <!-- HTML do wrapper -->")
    logger.info("   ```")
    logger.info("   ```typescript")
    logger.info("   // TypeScript do wrapper")
    logger.info("   ```")
    logger.info("   ```scss")
    logger.info("   /* SCSS do wrapper */")
    logger.info("   ```")
    logger.info("")
    logger.info("2️⃣ PROMPT DE VALIDAÇÃO:")
    logger.info("   📍 validation.wrapper_validation")
    logger.info("")
    logger.info("   Foca em:")
    logger.info("   - Conexão entre wrapper e filhos")
    logger.info("   - @Input()/@Output() corretos")
    logger.info("   - Classes do Design System")
    logger.info("   - Layout baseado no Figma")

def main():
    """
    Função principal com todos os exemplos.
    """
    logger.info("🧪 EXEMPLOS DE USO - GERAÇÃO INTEGRADA DE WRAPPERS")
    logger.info("=" * 60)
    
    # Exemplo básico
    example_integrated_wrapper_generation()
    
    print("\n")
    
    # Comparação de abordagens
    example_comparison_old_vs_new()
    
    print("\n")
    
    # Integração no fluxo principal
    example_integration_in_main_flow()
    
    print("\n")
    
    # Estrutura dos prompts
    example_prompt_structure()
    
    logger.info("=" * 60)
    logger.info("✅ Exemplos concluídos!")
    logger.info("")
    logger.info("📚 Para mais informações, consulte:")
    logger.info("   - docs/wrapper_integrated_generation.md")
    logger.info("   - src/generators/test_wrapper_integrated.py")

if __name__ == "__main__":
    main()
