#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comparação entre os fluxos de validação de wrapper:
1. Abordagem anterior: Quebrar primeiro, validar depois
2. Abordagem nova: Validar primeiro, quebrar depois
"""

import sys
from pathlib import Path

# Adicionar o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from src.utils.logging import get_logger

logger = get_logger(__name__)

def show_old_approach():
    """
    Mostra a abordagem anterior (quebrar primeiro, validar depois).
    """
    logger.info("🔄 ABORDAGEM ANTERIOR (Quebrar → Validar)")
    logger.info("=" * 50)
    
    logger.info("1️⃣ Gerar resposta integrada da IA:")
    logger.info("   integrated_response = ai.generate_wrapper(...)")
    logger.info("   # Resposta completa com HTML + TS + SCSS")
    logger.info("")
    
    logger.info("2️⃣ Quebrar IMEDIATAMENTE em arquivos:")
    logger.info("   html = extract_html(integrated_response)")
    logger.info("   ts = extract_typescript(integrated_response)")
    logger.info("   scss = extract_scss(integrated_response)")
    logger.info("   # ❌ Perde contexto da resposta completa")
    logger.info("")
    
    logger.info("3️⃣ Validar arquivos separados:")
    logger.info("   validated_html, validated_ts, validated_scss = ai.validate(html, ts, scss)")
    logger.info("   # ❌ IA não vê a resposta original completa")
    logger.info("")
    
    logger.info("❌ PROBLEMAS:")
    logger.info("   - IA de validação não vê o contexto completo")
    logger.info("   - Perde informações da resposta original")
    logger.info("   - Validação menos eficaz")

def show_new_approach():
    """
    Mostra a nova abordagem (validar primeiro, quebrar depois).
    """
    logger.info("🆕 NOVA ABORDAGEM (Validar → Quebrar)")
    logger.info("=" * 50)
    
    logger.info("1️⃣ Gerar resposta integrada da IA:")
    logger.info("   integrated_response = ai.generate_wrapper(...)")
    logger.info("   # Resposta completa com HTML + TS + SCSS")
    logger.info("")
    
    logger.info("2️⃣ Validar resposta COMPLETA:")
    logger.info("   validated_response = ai.validate_raw(integrated_response, context)")
    logger.info("   # ✅ IA vê toda a resposta original")
    logger.info("")
    
    logger.info("3️⃣ Quebrar APÓS validação:")
    logger.info("   html = extract_html(validated_response)")
    logger.info("   ts = extract_typescript(validated_response)")
    logger.info("   scss = extract_scss(validated_response)")
    logger.info("   # ✅ Arquivos já validados e corrigidos")
    logger.info("")
    
    logger.info("✅ VANTAGENS:")
    logger.info("   - IA de validação vê o contexto completo")
    logger.info("   - Mantém informações da resposta original")
    logger.info("   - Validação mais eficaz e precisa")

def show_code_comparison():
    """
    Mostra a comparação de código entre as abordagens.
    """
    logger.info("💻 COMPARAÇÃO DE CÓDIGO")
    logger.info("=" * 50)
    
    logger.info("🔄 CÓDIGO ANTERIOR:")
    logger.info("```python")
    logger.info("# Gerar e quebrar imediatamente")
    logger.info("html, ts, scss = self._generate_wrapper_integrated_ai(context)")
    logger.info("")
    logger.info("# Validar arquivos separados")
    logger.info("html, ts, scss = ai_helper.validate_wrapper_with_ai(html, ts, scss, context)")
    logger.info("")
    logger.info("# Salvar arquivos")
    logger.info("generate_angular_component_files(name, html, ts, scss, output_dir)")
    logger.info("```")
    logger.info("")
    
    logger.info("🆕 CÓDIGO NOVO:")
    logger.info("```python")
    logger.info("# Gerar resposta completa")
    logger.info("integrated_response = self._generate_wrapper_integrated_ai_raw(context)")
    logger.info("")
    logger.info("# Validar resposta completa")
    logger.info("validated_response = ai_helper.validate_wrapper_with_ai_raw(integrated_response, context)")
    logger.info("")
    logger.info("# Quebrar APÓS validação")
    logger.info("html = self._extract_html_from_integrated_response(validated_response)")
    logger.info("ts = self._extract_typescript_from_integrated_response(validated_response)")
    logger.info("scss = self._extract_scss_from_integrated_response(validated_response)")
    logger.info("")
    logger.info("# Salvar arquivos")
    logger.info("generate_angular_component_files(name, html, ts, scss, output_dir)")
    logger.info("```")

def show_validation_context_difference():
    """
    Mostra a diferença no contexto de validação.
    """
    logger.info("🔍 DIFERENÇA NO CONTEXTO DE VALIDAÇÃO")
    logger.info("=" * 50)
    
    logger.info("🔄 CONTEXTO ANTERIOR (arquivos separados):")
    logger.info("```python")
    logger.info("validation_context = {")
    logger.info("    'wrapper_name': 'modal-vinculo',")
    logger.info("    'generated_html': '<div>...</div>',  # Só HTML")
    logger.info("    'generated_typescript': 'export class...',  # Só TS")
    logger.info("    'generated_scss': '.wrapper {...}',  # Só SCSS")
    logger.info("    # ❌ IA não vê como os arquivos se relacionam")
    logger.info("}")
    logger.info("```")
    logger.info("")
    
    logger.info("🆕 CONTEXTO NOVO (resposta completa):")
    logger.info("```python")
    logger.info("# IA recebe a resposta original completa:")
    logger.info("integrated_response = '''")
    logger.info("```html")
    logger.info("<div>...</div>")
    logger.info("```")
    logger.info("")
    logger.info("```typescript")
    logger.info("export class...")
    logger.info("```")
    logger.info("")
    logger.info("```scss")
    logger.info(".wrapper {...}")
    logger.info("```")
    logger.info("'''")
    logger.info("# ✅ IA vê toda a estrutura e relacionamentos")
    logger.info("```")

def show_benefits():
    """
    Mostra os benefícios da nova abordagem.
    """
    logger.info("🎯 BENEFÍCIOS DA NOVA ABORDAGEM")
    logger.info("=" * 50)
    
    logger.info("✅ MELHOR VALIDAÇÃO:")
    logger.info("   - IA vê o contexto completo da geração")
    logger.info("   - Pode verificar consistência entre arquivos")
    logger.info("   - Entende melhor as relações HTML ↔ TS ↔ SCSS")
    logger.info("")
    
    logger.info("✅ MENOS PERDA DE INFORMAÇÃO:")
    logger.info("   - Mantém formatação original da IA")
    logger.info("   - Preserva comentários e estrutura")
    logger.info("   - Não perde contexto na extração prematura")
    logger.info("")
    
    logger.info("✅ VALIDAÇÃO MAIS INTELIGENTE:")
    logger.info("   - IA pode corrigir inconsistências entre arquivos")
    logger.info("   - Pode ajustar um arquivo baseado em outro")
    logger.info("   - Validação holística do componente")

def main():
    """
    Função principal mostrando todas as comparações.
    """
    logger.info("🔄 COMPARAÇÃO: FLUXOS DE VALIDAÇÃO DE WRAPPER")
    logger.info("=" * 60)
    
    # Abordagem anterior
    show_old_approach()
    
    print("\n")
    
    # Nova abordagem
    show_new_approach()
    
    print("\n")
    
    # Comparação de código
    show_code_comparison()
    
    print("\n")
    
    # Diferença no contexto
    show_validation_context_difference()
    
    print("\n")
    
    # Benefícios
    show_benefits()
    
    logger.info("=" * 60)
    logger.info("✅ A nova abordagem é mais eficaz!")
    logger.info("   Validar primeiro, quebrar depois = melhor resultado")

if __name__ == "__main__":
    main()
