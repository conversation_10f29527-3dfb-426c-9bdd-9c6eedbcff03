# Exemplo de configuração do projeto Design to Code

# Configurações da API do Figma
figma:
  # ID do arquivo Figma que contém os componentes e estilos
  # Pode ser encontrado na URL do arquivo: https://www.figma.com/FILE_KEY/
  file_key: "ID_DO_ARQUIVO_FIGMA"

  # Substitua pelo seu token de acesso pessoal do Figma
  # Instruções: https://www.figma.com/developers/api#access-tokens
  token: "SEU_TOKEN_AQUI"
  
  # Profundidade máxima para exploração interativa (padrão: 5)
  # Use 0 para exploração ilimitada
  max_exploration_depth: 5
    
# Configurações do Design System
design_system:
  name: "Nome do Design System"
  slug: "nome_design_system"
  storybook:
    # URL principal do Storybook onde a documentação dos componentes está localizada
    base_url: "https://seu-storybook-url.com"

    # Versão do Storybook, default: latest
    version: "latest"
    auto_detect_version: true 

    # Camin<PERSON> das URLs
    components_path: "/?path=/docs/designsystem-components-"  
    docs_path: "/?path=/docs/design-system-" 

  # Configurações de cache
  cache:
    update_interval_hours: 48  # Intervalo de atualização em horas
    force_update: false  # Forçar atualização
    cache_dir: "data/design_system"  # Diretório de cache


# Configurações de geração
generation:
  framework: "angular"  # angular, react
  
  # Gerar componentes Angular completos (padrão: true)
  generate_angular_components: true
  
  # Incluir TypeScript com tipagem completa (padrão: true)
  include_typescript: true
  
  # Incluir SCSS com estilos (padrão: true)
  include_scss: true
  
  # Gerar relatórios de mapeamento (padrão: true)
  generate_mapping_reports: true
    

# Configurações de saída
output:
  # Diretório para salvar os dados extraídos do Figma (normalizados)
  figma_extraction_path: "data/figma_extraction"

  # Diretório para salvar os dados extraídos do Storybook
  design_system_path: "data/design_system"
  
  # Diretório para logs
  logs_path: "data/logs"
  
  # Diretório para componentes gerados
  generate_path: "data/output_generated/"
