#!/bin/bash

# Script unificado para o projeto Design to Code
# Facilita a execução do fluxo de trabalho com comandos simples.

set -e # Parar em caso de erro

# --- Cores para o output ---
BLUE='\033[0;34m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# --- Funções de Verificação ---

# Verifica se o Python 3 está disponível
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python 3 não encontrado. Instale a versão 3.10+ para continuar.${NC}"
        exit 1
    fi
}

# Verifica se o arquivo de configuração existe
check_config() {
    if [ ! -f "project_config.yaml" ]; then
        echo -e "${YELLOW}⚠️  Arquivo project_config.yaml não encontrado.${NC}"
        echo "Execute './run.sh setup' primeiro."
        return 1
    fi
    return 0
}

# --- Comandos ---

# Comando: setup
cmd_setup() {
    echo -e "${BLUE}🔧 Configurando o ambiente do projeto...${NC}"
    
    # Cria o arquivo de configuração a partir do exemplo, se não existir
    if [ ! -f "project_config.yaml" ]; then
        cp project_config.example.yaml project_config.yaml
        echo -e "${GREEN}✅ Arquivo 'project_config.yaml' criado.${NC}"
        echo -e "${YELLOW}   -> Edite-o com suas chaves de API e URLs.${NC}"
    else
        echo -e "${GREEN}✅ Arquivo 'project_config.yaml' já existe.${NC}"
    fi

    # Cria o arquivo .env se não existir
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            echo -e "${GREEN}✅ Arquivo '.env' criado.${NC}"
            echo -e "${YELLOW}   -> Edite-o com seus dados de ambiente.${NC}"
        else
            echo -e "${YELLOW}⚠️  Arquivo '.env.example' não encontrado.${NC}"
        fi
    else
        echo -e "${GREEN}✅ Arquivo '.env' já existe.${NC}"
    fi

    # Cria os diretórios de dados necessários
    mkdir -p data/logs data/figma_extraction data/design_system data/output data/cache
    echo -e "${GREEN}✅ Diretórios de dados verificados/criados em 'data/'.${NC}"
    
    echo -e "\n${GREEN}🎉 Setup concluído!${NC}"
}

# Comando: storybook
cmd_storybook() {
    echo -e "${BLUE}📚 Extraindo documentação do Storybook...${NC}"
    check_config
    
    # Verificar se há argumentos adicionais
    if [ $# -gt 1 ]; then
        echo -e "${YELLOW}Executando com argumentos: ${@:2}${NC}"
        python3 -m src.scripts.storybook_extract "${@:2}"
    else
        python3 -m src.scripts.storybook_extract
    fi
    
    echo -e "${GREEN}🎉 Extração do Storybook concluída!${NC}"
}

# Comando: figma
cmd_figma() {
    echo -e "${BLUE}🎨 Extraindo componentes do Figma (modo interativo)...${NC}"
    check_config
    python3 -m src.scripts.figma_extract
    echo -e "${GREEN}🎉 Extração do Figma concluída!${NC}"
}

# Comando: generate
cmd_generate() {
    echo -e "${BLUE}⚙️  Gerando código a partir das extrações...${NC}"
    check_config
    python3 -m src.scripts.code_generator
}

# Comando: all
cmd_all() {
    echo -e "${BLUE}🚀 Executando fluxo completo...${NC}"
    check_config
    
    echo -e "${YELLOW}1. Extraindo Storybook...${NC}"
    cmd_storybook
    
    echo -e "${YELLOW}2. Extraindo Figma...${NC}"
    cmd_figma
    
    echo -e "${YELLOW}3. Gerando código...${NC}"
    cmd_generate
    
    echo -e "${GREEN}🎉 Fluxo completo concluído!${NC}"
}

cmd_clean() {
    echo -e "${BLUE}🧹 Limpando dados gerados${NC}"
    echo "========================"
    
    # Confirmar limpeza
    echo -e "${YELLOW}⚠️  Esta ação irá remover:${NC}"
    echo "   - data/output/ (dados estruturados)"
    echo "   - data/logs/ (arquivos de log)"
    echo "   - data/figma_extraction/ (componentes extraídos)"
    echo "   - data/design_system/ (dados do design system)"
    echo "   - data/cache/ (cache do storybook)"
    echo ""
    read -p "Continuar? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf data/output data/logs data/figma_extraction data/design_system data/cache
        mkdir -p data/logs data/figma_extraction data/design_system data/output data/cache
        echo -e "${GREEN}✅ Limpeza concluída${NC}"
    else
        echo -e "${BLUE}ℹ️  Limpeza cancelada${NC}"
    fi
}

# --- Ajuda e Menu Principal ---

show_help() {
    echo -e "${BLUE}Design to Code - Runner${NC}"
    echo "------------------------"
    echo "Uso: ./run.sh [comando] [opções]"
    echo
    echo "Comandos disponíveis:"
    echo -e "  ${GREEN}setup${NC}           - Configura o ambiente inicial (config, diretórios)."
    echo -e "  ${GREEN}storybook${NC}       - Extrai a documentação do Storybook."
    echo -e "  ${GREEN}storybook-test${NC}  - Testa extração do Storybook (modo debug)."
    echo -e "  ${GREEN}figma${NC}           - Inicia a extração interativa de componentes do Figma."
    echo -e "  ${GREEN}generate${NC}        - Gera o código a partir dos dados extraídos."
    echo -e "  ${GREEN}all${NC}             - Executa o fluxo completo (storybook -> figma -> generate)."
    echo -e "  ${GREEN}clean${NC}           - Remove todos os dados gerados."
    echo -e "  ${GREEN}help${NC}            - Mostra esta mensagem de ajuda."
    echo
    echo "Fluxo recomendado: setup -> storybook -> figma -> generate"
    echo "ou use 'all' para executar tudo de uma vez."
    echo
    echo "Exemplos de uso do storybook:"
    echo "  ./run.sh storybook                    # Extração completa"
    echo "  ./run.sh storybook --limit 10         # Limitar a 10 componentes"
    echo "  ./run.sh storybook --category components  # Apenas componentes"
    echo "  ./run.sh storybook --save-json        # Salvar em JSON"
    echo "  ./run.sh storybook --save-ai-context  # Salvar para IA"
    echo "  ./run.sh storybook --debug-urls       # Debug de URLs"
    echo
    echo "Funcionalidades atuais:"
    echo "  - Extração de componentes do Figma com acesso simplificado"
    echo "  - Sistema de Design System com parsers especializados"
    echo "  - Cache inteligente com verificação de versão"
    echo "  - Filtro de visibilidade automático"
    echo "  - Interface interativa para navegação"
    echo "  - Geração de Markdown otimizado para IA"
}

# Função principal que direciona para o comando correto
main() {
    check_python
    case "${1:-help}" in
        setup)           cmd_setup ;;
        storybook)       cmd_storybook "$@" ;;
        figma)          cmd_figma ;;
        generate)       cmd_generate ;;
        clean)          cmd_clean ;;
        help|*)         show_help ;;
    esac
}

main "$@"