"""
Parsers para extração de dados do Design System - Versão Modular

Esta versão utiliza o BaseParser como parser padrão,
dividido em módulos menores para melhor organização e manutenção.
"""

from .base_parser import BaseParser
from .content_processor import ContentProcessor
from .code_processor import CodeProcessor
from .table_processor import TableProcessor
from .image_parser import ImageParser
from .classes_parser import ClassesParser

__all__ = [
    'BaseParser',
    'ContentProcessor',
    'CodeProcessor',
    'TableProcessor',
    'ImageParser',
    'ClassesParser'
] 