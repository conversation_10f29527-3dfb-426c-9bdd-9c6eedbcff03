"""
Parser base para extração de conteúdo do Storybook.

Este é o parser principal que coordena a extração de conteúdo do Storybook,
utilizando processadores especializados para diferentes tipos de conteúdo.
"""

import logging
import re
from typing import List, Optional
from bs4 import BeautifulSoup, Tag
from datetime import datetime

from .content_processor import ContentProcessor
from .code_processor import CodeProcessor
from .table_processor import TableProcessor
from .image_parser import ImageParser

logger = logging.getLogger(__name__)

class BaseParser:
    """
    Parser principal que extrai conteúdo do Storybook de forma fiel e estruturada.
    Coordena diferentes processadores para extrair todo o conteúdo relevante.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Inicializar processadores especializados
        self.content_processor = ContentProcessor()
        self.code_processor = CodeProcessor()
        self.table_processor = TableProcessor()
        self.image_parser = ImageParser()
    
    def parse_to_markdown(self, html_content: str, component_name: str = "") -> str:
        """
        Extrai conteúdo do Storybook e converte para Markdown estruturado.
        
        Args:
            html_content: Conteúdo HTML do Storybook
            component_name: Nome do componente
            
        Returns:
            Conteúdo em formato Markdown
        """
        self.logger.info(f"Extraindo conteúdo para Markdown: {component_name}")
        
        # Verificar se é um componente de imagem
        if self._is_image_component(component_name):
            self.logger.info(f"Detectado componente de imagem: {component_name}")
            return self._parse_image_component_to_markdown(html_content, component_name)
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Limpar elementos UI desnecessários
        self._clean_storybook_ui_elements(soup)
        
        # Encontrar o container principal do conteúdo
        main_content = self._find_main_content_container(soup)
        if not main_content:
            self.logger.warning("Container principal de conteúdo não encontrado")
            return f"# {component_name}\n\n*Conteúdo não encontrado*"
        
        # Extrair estrutura completa
        markdown_sections = []
        
        # 1. Título principal
        title = self.content_processor.extract_main_title(main_content) or component_name
        markdown_sections.append(f"# {title}")
        
        # 2. Descrição inicial
        description = self.content_processor.extract_initial_description(main_content)
        if description:
            markdown_sections.append(description)
        
        # 3. Índice/navegação (se existir)
        navigation = self.content_processor.extract_navigation_links(main_content)
        if navigation:
            markdown_sections.append("## Índice")
            markdown_sections.append(navigation)
        
        # 4. Seções estruturadas
        sections = self._extract_structured_sections(main_content)
        markdown_sections.extend(sections)
        
        # 5. Metadados finais
        markdown_sections.append("")
        markdown_sections.append("---")
        markdown_sections.append(f"*Extraído automaticamente do Storybook em {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        
        return "\n\n".join(markdown_sections)
    
    def _clean_storybook_ui_elements(self, soup: BeautifulSoup) -> None:
        """Remove elementos UI desnecessários do Storybook."""
        try:
            # Remover botões de cópia - procurar por diferentes padrões
            copy_selectors = [
                'button[class*="copy"]',
                'button[class*="Copy"]',
                '.css-otxova',  # Classe específica do botão Copy
                '.css-111a2cx'  # Container do botão Copy
            ]
            
            for selector in copy_selectors:
                try:
                    elements = soup.select(selector)
                    for element in elements:
                        if element and element.get_text().strip() == "Copy":
                            element.decompose()
                except:
                    pass
            
            # Buscar botões "Copy" por texto (alternativa ao :contains)
            copy_buttons = soup.find_all('button')
            for button in copy_buttons:
                if button and button.get_text().strip() == "Copy":
                    button.decompose()
            
            # Remover elementos de scrollbar e outros elementos UI
            ui_selectors = [
                'div[class*="scrollbar"]',
                'div[class*="os-"]',
                'div[class*="observer"]',
                'div[class*="observed"]'
            ]
            
            for selector in ui_selectors:
                elements = soup.select(selector)
                for element in elements:
                    if element:
                        element.decompose()
            
            # Remover elementos CSS de formatação (preservar conteúdo)
            css_elements = soup.select('div[class^="css-"], span[class^="css-"]')
            for element in css_elements:
                if element:
                    element.unwrap()
                    
        except Exception as e:
            self.logger.warning(f"Erro ao limpar elementos UI: {e}")
            # Continuar mesmo se houver erro na limpeza
    
    def _find_main_content_container(self, soup: BeautifulSoup) -> Optional[Tag]:
        """Encontra o container principal do conteúdo do Storybook."""
        # Tentar diferentes seletores para encontrar o conteúdo principal
        selectors = [
            "div.sbdocs-content",
            "div.sbdocs.sbdocs-content",
            ".sbdocs-wrapper .sbdocs-content",
            "[class*='sbdocs-content']"
        ]
        
        for selector in selectors:
            content = soup.select_one(selector)
            if content:
                self.logger.debug(f"Conteúdo encontrado com seletor: {selector}")
                return content
        
        # Fallback: procurar por qualquer div que contenha conteúdo relevante
        divs = soup.find_all('div')
        for div in divs:
            if div.find('h1') and (div.find('p') or div.find('pre') or div.find('table')):
                self.logger.debug("Conteúdo encontrado via fallback")
                return div
        
        return None
    
    def _extract_structured_sections(self, content: Tag) -> List[str]:
        """Extrai todas as seções estruturadas do conteúdo."""
        sections = []
        processed_elements = set()
        
        # Encontrar todos os headings para estruturar as seções
        headings = content.find_all(['h2', 'h3', 'h4', 'h5', 'h6'])
        
        for heading in headings:
            if id(heading) in processed_elements:
                continue
            
            section_content = self._extract_section_content(heading, processed_elements)
            if section_content:
                sections.append(section_content)
        
        return sections
    
    def _extract_section_content(self, heading: Tag, processed_elements: set) -> Optional[str]:
        """Extrai conteúdo completo de uma seção."""
        heading_text = self._clean_text(heading.get_text())
        if not heading_text:
            return None
        
        processed_elements.add(id(heading))
        
        # Determinar nível do heading para markdown
        heading_level = int(heading.name[1])  # h2 = 2, h3 = 3, etc.
        markdown_heading = '#' * heading_level + ' ' + heading_text
        
        section_parts = [markdown_heading]
        
        # Extrair conteúdo até o próximo heading do mesmo nível ou superior
        current = heading.find_next_sibling()
        
        while current:
            if hasattr(current, 'name') and current.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                current_level = int(current.name[1])
                if current_level <= heading_level:
                    break
            
            if id(current) in processed_elements:
                current = current.find_next_sibling()
                continue
            
            content_part = self._process_content_element(current)
            if content_part:
                section_parts.append(content_part)
                processed_elements.add(id(current))
            
            current = current.find_next_sibling()
        
        return '\n\n'.join(section_parts) if len(section_parts) > 1 else None
    
    def _process_content_element(self, element: Tag) -> Optional[str]:
        """Processa um elemento de conteúdo e retorna sua representação em Markdown."""
        if not hasattr(element, 'name'):
            return None
        
        element_name = element.name
        
        if element_name == 'p':
            return self.content_processor.process_paragraph(element)
        elif element_name in ['ul', 'ol']:
            return self.content_processor.process_list(element)
        elif element_name == 'table':
            return self.table_processor.process_table(element)
        elif element_name == 'pre':
            return self.code_processor.process_code_block(element)
        elif element_name == 'div':
            return self.code_processor.process_div_container(element)
        elif element_name == 'blockquote':
            return self.content_processor.process_blockquote(element)
        elif element_name in ['h3', 'h4', 'h5', 'h6']:
            # Sub-headings dentro da seção
            heading_text = self._clean_text(element.get_text())
            if heading_text:
                level = int(element.name[1])
                return '#' * level + ' ' + heading_text
        
        return None
    
    def _clean_text(self, text: str) -> str:
        """Limpa e normaliza texto."""
        if not text:
            return ""
        
        # Remover espaços extras e quebras de linha desnecessárias
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Remover caracteres de controle
        cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)
        
        return cleaned
    
    def _is_image_component(self, component_name: str) -> bool:
        """Verifica se o componente é um componente de imagem."""
        if not component_name:
            return False
        
        name_lower = component_name.lower()
        image_keywords = ['icon', 'logo', 'flag', 'animation', 'illustration', 'images']
        
        return any(keyword in name_lower for keyword in image_keywords)
    
    def _parse_image_component_to_markdown(self, html_content: str, component_name: str) -> str:
        """Processa componente de imagem usando ImageParser."""
        try:
            # Usar ImageParser para extrair dados estruturados
            image_data = self.image_parser.parse_image_component(html_content, component_name)
            
            # Converter para Markdown
            markdown_sections = []
            
            # 1. Título
            title = image_data.get("component_name", component_name)
            markdown_sections.append(f"# {title}")
            
            # 2. Descrição
            description = image_data.get("description", "")
            if description:
                markdown_sections.append(description)
            
            # 3. Instruções de uso
            usage_instructions = image_data.get("usage_instructions", [])
            if usage_instructions:
                markdown_sections.append("## Instruções de Uso")
                for instruction in usage_instructions:
                    markdown_sections.append(instruction)
            
            # 4. Opções de tamanho
            size_options = image_data.get("size_options", {})
            if size_options:
                markdown_sections.append("## Tamanhos")
                markdown_sections.append("| Classes | Width |")
                markdown_sections.append("| --- | --- |")
                for class_name, size_value in size_options.items():
                    markdown_sections.append(f"| {class_name} | {size_value} |")
            
            # 5. Exemplo de código
            code_example = image_data.get("code_example", "")
            if code_example:
                markdown_sections.append("## Exemplo de Código")
                markdown_sections.append("```html")
                markdown_sections.append(code_example)
                markdown_sections.append("```")
            
            # 6. Lista de itens
            items = image_data.get("items", [])
            total_items = image_data.get("total_items", 0)
            
            if items:
                markdown_sections.append(f"## Itens ({total_items} total)")
                
                # Criar tabela com todos os itens
                markdown_sections.append("| Nome | Exemplo de Uso |")
                markdown_sections.append("| --- | --- |")
                
                for item in items:
                    name = item.get("name", "")
                    html_code = item.get("html_code", "")
                    # Escapar pipes no código HTML para não quebrar a tabela
                    html_code_escaped = html_code.replace("|", "\\|")
                    markdown_sections.append(f"| {name} | `{html_code_escaped}` |")
            
            # 7. Metadados finais
            markdown_sections.append("")
            markdown_sections.append("---")
            markdown_sections.append(f"*Extraído automaticamente do Storybook em {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
            
            return "\n\n".join(markdown_sections)
            
        except Exception as e:
            self.logger.error(f"Erro ao processar componente de imagem {component_name}: {e}")
            # Fallback para processamento normal
            return self._fallback_parse_to_markdown(html_content, component_name)
    
    def _fallback_parse_to_markdown(self, html_content: str, component_name: str) -> str:
        """Fallback para processamento normal quando ImageParser falha."""
        soup = BeautifulSoup(html_content, 'html.parser')
        self._clean_storybook_ui_elements(soup)
        
        main_content = self._find_main_content_container(soup)
        if not main_content:
            return f"# {component_name}\n\n*Conteúdo não encontrado*"
        
        markdown_sections = []
        title = self.content_processor.extract_main_title(main_content) or component_name
        markdown_sections.append(f"# {title}")
        
        description = self.content_processor.extract_initial_description(main_content)
        if description:
            markdown_sections.append(description)
        
        sections = self._extract_structured_sections(main_content)
        markdown_sections.extend(sections)
        
        markdown_sections.append("")
        markdown_sections.append("---")
        markdown_sections.append(f"*Extraído automaticamente do Storybook em {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        
        return "\n\n".join(markdown_sections) 