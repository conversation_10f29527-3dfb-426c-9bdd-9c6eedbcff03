#!/usr/bin/env python3
"""
Parser para classes utilitárias do design system
"""

import re
from typing import Dict, List, Any
from bs4 import BeautifulSoup


class ClassesParser:
    """Parser para classes utilitárias"""
    
    def __init__(self):
        # Importar processadores especializados
        from src.design_system.parsers.table_processor import TableProcessor
        from src.design_system.parsers.content_processor import ContentProcessor
        
        self.table_processor = TableProcessor()
        self.content_processor = ContentProcessor()
    
    def parse_classes_component(self, html_content: str, component_name: str = "") -> Dict[str, Any]:
        """Parse um componente de classes"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Extrair informações básicas
        title = self._extract_title(soup)
        description = self._extract_description(soup)
        
        # Determinar tipo de classe baseado no nome
        class_type = self._determine_class_type(component_name)
        
        # Extrair seções com títulos (h2-h5) e suas tabelas
        sections = self._extract_sections_with_titles(soup)
        
        # Extrair exemplos baseado no tipo
        examples = self._extract_examples(soup, class_type)
        
        # Extrair código de exemplo
        code_examples = self._extract_code_examples(soup)
        
        # Extrair tabela de propriedades se existir
        properties_table = self._extract_properties_table(soup)
        
        return {
            "component_name": title,
            "description": description,
            "class_type": class_type,
            "sections": sections,
            "examples": examples,
            "code_examples": code_examples,
            "properties_table": properties_table,
            "total_examples": len(examples)
        }
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extrai o título do componente"""
        title_elem = soup.find('h1', class_='sbdocs-title')
        if title_elem:
            return title_elem.get_text(strip=True)
        return ""
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extrai a descrição do componente"""
        # Buscar por parágrafo após o título
        title_elem = soup.find('h1', class_='sbdocs-title')
        if title_elem:
            desc_elem = title_elem.find_next_sibling('p')
            if desc_elem:
                return desc_elem.get_text(strip=True)
        return ""
    
    def _extract_sections_with_titles(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai seções com títulos (h2-h5) e seus conteúdos incluindo tabelas"""
        sections = []
        
        # Buscar por todos os títulos h2-h5
        headings = soup.find_all(['h2', 'h3', 'h4', 'h5'])
        
        for heading in headings:
            section = {
                "title": heading.get_text(strip=True),
                "level": heading.name,
                "content": "",
                "tables": []
            }
            
            # Extrair conteúdo até o próximo título
            next_elem = heading.find_next_sibling()
            content_text = []
            
            while next_elem and next_elem.name not in ['h1', 'h2', 'h3', 'h4', 'h5']:
                if next_elem.name == 'table':
                    # Processar tabela usando TableProcessor
                    table_markdown = self.table_processor.process_table(next_elem)
                    if table_markdown:
                        section["tables"].append(table_markdown)
                elif next_elem.name in ['p', 'div', 'span']:
                    text = next_elem.get_text(strip=True)
                    if text:
                        content_text.append(text)
                
                next_elem = next_elem.find_next_sibling()
            
            section["content"] = " ".join(content_text)
            sections.append(section)
        
        return sections
    
    def _determine_class_type(self, component_name: str) -> str:
        """Determina o tipo de classe baseado no nome"""
        if "flex" in component_name.lower():
            return "flex"
        elif "color" in component_name.lower():
            return "colors"
        elif "glassmorphism" in component_name.lower():
            return "glassmorphism"
        elif "gradient" in component_name.lower():
            return "gradients"
        elif "skeleton" in component_name.lower():
            return "skeleton"
        elif "spacing" in component_name.lower():
            return "spacing"
        elif "waves" in component_name.lower():
            return "waves"
        elif "zindex" in component_name.lower():
            return "zindex"
        elif "typography" in component_name.lower():
            return "typography"
        else:
            return "generic"
    
    def _extract_examples(self, soup: BeautifulSoup, class_type: str) -> List[Dict[str, str]]:
        """Extrai exemplos baseado no tipo de classe"""
        if class_type == "flex":
            return self._extract_flex_examples(soup)
        elif class_type == "colors":
            return self._extract_colors_examples(soup)
        elif class_type == "typography":
            return self._extract_typography_examples(soup)
        elif class_type in ["glassmorphism", "gradients", "skeleton", "spacing", "waves", "zindex"]:
            return self._extract_code_box_examples(soup)
        else:
            return self._extract_generic_examples(soup)
    
    def _extract_typography_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de classes de tipografia"""
        examples = []
        
        # Buscar por elementos que contenham classes de tipografia
        typography_elements = soup.find_all(['div', 'span', 'p'], class_=re.compile(r'brad-font-'))
        
        for elem in typography_elements:
            classes = elem.get('class', [])
            typography_classes = [cls for cls in classes if cls.startswith('brad-font-')]
            
            if typography_classes:
                text = elem.get_text(strip=True)
                examples.append({
                    "name": text or typography_classes[0],
                    "class": typography_classes[0],
                    "html_code": f'<{elem.name} class="{typography_classes[0]}">{text}</{elem.name}>'
                })
        
        return examples
    
    def _extract_flex_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de classes flex"""
        examples = []
        
        # Buscar por elementos que contenham classes flex
        flex_elements = soup.find_all(['div', 'span'], class_=re.compile(r'brad-flex'))
        
        for elem in flex_elements:
            classes = elem.get('class', [])
            flex_classes = [cls for cls in classes if cls.startswith('brad-flex')]
            
            if flex_classes:
                # Extrair texto do elemento
                text = elem.get_text(strip=True)
                if text:
                    examples.append({
                        "name": text,
                        "class": ' '.join(flex_classes),
                        "html_code": f'<{elem.name} class="{" ".join(flex_classes)}">{text}</{elem.name}>'
                    })
        
        return examples
    
    def _extract_colors_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de classes de cores"""
        examples = []
        
        # Buscar por elementos que contenham informações de cores
        color_elements = soup.find_all(['div', 'span'], class_=re.compile(r'brad-(bg|text|border)-color'))
        
        for elem in color_elements:
            classes = elem.get('class', [])
            color_classes = [cls for cls in classes if 'color' in cls]
            
            if color_classes:
                # Extrair informações de cor
                text = elem.get_text(strip=True)
                
                # Buscar por informações HEX/RGB
                hex_info = elem.find(text=re.compile(r'#[0-9a-fA-F]{6}'))
                rgb_info = elem.find(text=re.compile(r'rgb\([^)]+\)'))
                
                color_info = {
                    "name": text or color_classes[0],
                    "class": color_classes[0],
                    "html_code": f'<{elem.name} class="{color_classes[0]}">{text}</{elem.name}>'
                }
                
                if hex_info:
                    color_info["hex"] = hex_info.strip()
                if rgb_info:
                    color_info["rgb"] = rgb_info.strip()
                
                examples.append(color_info)
        
        return examples
    
    def _extract_code_box_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de caixas de código (glassmorphism, gradients, etc.)"""
        examples = []
        
        # Buscar por caixas de código
        code_boxes = soup.find_all('div', class_=re.compile(r'docblock-source|css-'))
        
        for box in code_boxes:
            # Buscar por código dentro da caixa
            code_elem = box.find(['pre', 'code'])
            if code_elem:
                code_text = code_elem.get_text(strip=True)
                if code_text:
                    # Extrair classes do código
                    class_matches = re.findall(r'brad-[a-zA-Z-]+', code_text)
                    if class_matches:
                        examples.append({
                            "name": class_matches[0],
                            "class": class_matches[0],
                            "html_code": code_text
                        })
        
        return examples
    
    def _extract_generic_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos genéricos"""
        examples = []
        
        # Buscar por elementos que contenham classes brad-
        brad_elements = soup.find_all(['div', 'span'], class_=re.compile(r'brad-'))
        
        for elem in brad_elements:
            classes = elem.get('class', [])
            brad_classes = [cls for cls in classes if cls.startswith('brad-')]
            
            if brad_classes:
                text = elem.get_text(strip=True)
                examples.append({
                    "name": text or brad_classes[0],
                    "class": brad_classes[0],
                    "html_code": f'<{elem.name} class="{brad_classes[0]}">{text}</{elem.name}>'
                })
        
        return examples
    
    def _extract_code_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de código HTML"""
        code_examples = []
        
        # Buscar por elementos pre com código
        pre_elements = soup.find_all('pre', class_='prismjs')
        
        for pre in pre_elements:
            code_elem = pre.find('div', class_='language-html')
            if code_elem:
                code_text = code_elem.get_text(strip=True)
                if code_text:
                    code_examples.append({
                        "title": "Exemplo de código",
                        "code": code_text,
                        "language": "html"
                    })
        
        return code_examples
    
    def _extract_properties_table(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extrai tabela de propriedades se existir"""
        table = soup.find('table', class_='docblock-argstable')
        if not table:
            return {}
        
        properties = {}
        
        # Extrair headers
        headers = []
        header_row = table.find('thead')
        if header_row:
            header_cells = header_row.find_all('th')
            headers = [cell.get_text(strip=True) for cell in header_cells]
        
        # Extrair dados
        rows = table.find('tbody').find_all('tr') if table.find('tbody') else []
        
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 2:
                prop_name = cells[0].get_text(strip=True)
                prop_desc = cells[1].get_text(strip=True) if len(cells) > 1 else ""
                prop_default = cells[2].get_text(strip=True) if len(cells) > 2 else ""
                
                properties[prop_name] = {
                    "description": prop_desc,
                    "default": prop_default
                }
        
        return properties 