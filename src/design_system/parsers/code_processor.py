"""
Processador de código para extração de blocos de código do Storybook.

Este módulo foca em extrair e processar blocos de código,
detectando linguagens e aplicando limpeza específica.
"""

import logging
import re
from typing import List, Optional
from bs4 import Tag

logger = logging.getLogger(__name__)

class CodeProcessor:
    """
    Processador especializado em extrair e processar blocos de código do Storybook.
    Detecta linguagens e aplica limpeza específica para cada tipo.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def process_code_block(self, pre_element: Tag) -> Optional[str]:
        """
        Processa blocos de código.
        
        Args:
            pre_element: Elemento pre com código
            
        Returns:
            Bloco de código formatado em Markdown ou None
        """
        # Procurar por elemento code dentro do pre
        code_element = pre_element.find('code')
        if code_element:
            code_text = self._extract_clean_code(code_element)
            
            # Tentar detectar linguagem
            language = self._detect_code_language(code_element, code_text)
            
            # Aplicar limpeza específica baseada na linguagem
            if language == 'html':
                code_text = self._clean_html_code(code_text)
            elif language == 'javascript':
                code_text = self._clean_javascript_code(code_text)
            
            return f"```{language}\n{code_text}\n```"
        else:
            # Se não há elemento code, usar o texto do pre diretamente
            pre_text = pre_element.get_text()
            if pre_text.strip():
                return f"```\n{pre_text}\n```"
        
        return None
    
    def extract_storybook_code_blocks(self, container: Tag) -> List[str]:
        """
        Extrai blocos de código dos containers do Storybook.
        
        Args:
            container: Container que pode conter código
            
        Returns:
            Lista de blocos de código formatados
        """
        code_blocks = []
        
        # Procurar por elementos pre dentro do container
        pre_elements = container.find_all('pre')
        
        for pre in pre_elements:
            # Verificar se o pre contém código real (não apenas "Copy")
            code_text = pre.get_text().strip()
            if code_text and code_text != "Copy":
                code_block = self.process_code_block(pre)
                if code_block:
                    code_blocks.append(code_block)
        
        return code_blocks
    
    def process_div_container(self, div_element: Tag) -> Optional[str]:
        """
        Processa containers div que podem conter código.
        
        Args:
            div_element: Elemento div container
            
        Returns:
            Conteúdo processado ou None
        """
        # Verificar se é um container de código do Storybook
        classes = div_element.get('class', [])
        
        # Remover botões de cópia e outros elementos UI
        copy_buttons = div_element.find_all('button', class_=re.compile(r'copy|Copy'))
        for button in copy_buttons:
            button.decompose()
        
        # Remover elementos de scrollbar e outros elementos UI
        ui_elements = div_element.find_all(['div'], class_=re.compile(r'scrollbar|os-'))
        for element in ui_elements:
            if any(cls in str(element.get('class', [])) for cls in ['scrollbar', 'os-']):
                element.decompose()
        
        # Verificar se é um container de código do Storybook (docblock-source)
        if any('docblock-source' in cls for cls in classes):
            code_blocks = self.extract_storybook_code_blocks(div_element)
            return '\n\n'.join(code_blocks) if code_blocks else None
        
        # Verificar se é um container de código com os-content
        elif any('os-content' in cls for cls in classes):
            code_blocks = self.extract_storybook_code_blocks(div_element)
            return '\n\n'.join(code_blocks) if code_blocks else None
        
        # Verificar se contém elementos pre diretamente
        elif div_element.find('pre'):
            code_blocks = self.extract_storybook_code_blocks(div_element)
            return '\n\n'.join(code_blocks) if code_blocks else None
        
        else:
            # Container genérico - extrair texto se relevante
            text_content = self._clean_text(div_element.get_text())
            if text_content and len(text_content) > 20:  # Apenas se tiver conteúdo substancial
                return text_content
        
        return None
    
    def _extract_clean_code(self, code_element: Tag) -> str:
        """
        Extrai código limpo removendo formatação excessiva do Storybook.
        
        Args:
            code_element: Elemento code
            
        Returns:
            Código limpo
        """
        # Se o código tem elementos span com classes de syntax highlighting
        spans = code_element.find_all('span')
        if spans:
            # Extrair apenas o texto dos spans, preservando a estrutura
            code_parts = []
            for span in spans:
                text = span.get_text()
                if text:
                    code_parts.append(text)
            return ''.join(code_parts)
        else:
            # Se não há spans, usar o texto direto
            return code_element.get_text()
    
    def _clean_html_code(self, html_code: str) -> str:
        """
        Limpa código HTML removendo formatação excessiva.
        
        Args:
            html_code: Código HTML para limpar
            
        Returns:
            Código HTML limpo
        """
        # Remover quebras de linha desnecessárias
        cleaned = re.sub(r'\n\s*\n', '\n', html_code)
        
        # Remover espaços em branco excessivos no início das linhas
        cleaned = re.sub(r'^\s+', '', cleaned, flags=re.MULTILINE)
        
        # Remover linhas vazias no início e fim
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _clean_javascript_code(self, js_code: str) -> str:
        """
        Limpa código JavaScript removendo formatação excessiva.
        
        Args:
            js_code: Código JavaScript para limpar
            
        Returns:
            Código JavaScript limpo
        """
        # Remover quebras de linha desnecessárias
        cleaned = re.sub(r'\n\s*\n', '\n', js_code)
        
        # Remover espaços em branco excessivos no início das linhas
        cleaned = re.sub(r'^\s+', '', cleaned, flags=re.MULTILINE)
        
        # Remover linhas vazias no início e fim
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _detect_code_language(self, code_element: Tag, code_text: str) -> str:
        """
        Detecta a linguagem do código.
        
        Args:
            code_element: Elemento code
            code_text: Texto do código
            
        Returns:
            Linguagem detectada
        """
        # Verificar classes do elemento
        classes = code_element.get('class', [])
        for cls in classes:
            if cls.startswith('language-'):
                return cls.replace('language-', '')
            elif cls == 'language-html':
                return 'html'
            elif cls == 'language-js' or cls == 'language-javascript':
                return 'javascript'
            elif cls == 'language-css':
                return 'css'
            elif cls == 'language-ts' or cls == 'language-typescript':
                return 'typescript'
        
        # Detectar pela análise do conteúdo
        code_lower = code_text.lower()
        
        # HTML detection
        if any(keyword in code_lower for keyword in ['<div', '<span', '<button', 'class=', 'id=']):
            return 'html'
        
        # JavaScript detection
        if any(keyword in code_lower for keyword in ['const ', 'let ', 'var ', 'function', 'getInstance', 'LiquidCorp', 'new Date', 'console.log']):
            return 'javascript'
        
        # CSS detection
        if any(keyword in code_lower for keyword in ['.css', 'color:', 'margin:', 'padding:', 'background:', 'font-size:']):
            return 'css'
        
        # TypeScript detection
        if any(keyword in code_lower for keyword in ['import ', 'export ', 'interface ', 'type ', '=>']):
            return 'typescript'
        
        # Fallback para HTML se contém tags
        if '<' in code_text and '>' in code_text:
            return 'html'
        
        return 'text'
    
    def _clean_text(self, text: str) -> str:
        """
        Limpa e normaliza texto.
        
        Args:
            text: Texto para limpar
            
        Returns:
            Texto limpo
        """
        if not text:
            return ""
        
        # Remover espaços extras e quebras de linha desnecessárias
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Remover caracteres de controle
        cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)
        
        return cleaned 