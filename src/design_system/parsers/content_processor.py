"""
Processador de conteúdo para extração de elementos específicos do Storybook.

Este módulo foca em extrair e processar diferentes tipos de conteúdo
como parágrafos, listas, citações e navegação.
"""

import logging
import re
from typing import Optional
from bs4 import Tag, NavigableString

logger = logging.getLogger(__name__)

class ContentProcessor:
    """
    Processador especializado em extrair e processar conteúdo textual do Storybook.
    Foca em parágrafos, listas, citações e navegação.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def process_paragraph(self, p_element: Tag) -> Optional[str]:
        """
        Processa um parágrafo, preservando formatação básica.
        
        Args:
            p_element: Elemento parágrafo
            
        Returns:
            Texto formatado em Markdown ou None
        """
        text_parts = []
        
        for child in p_element.children:
            if isinstance(child, NavigableString):
                text = str(child).strip()
                if text:
                    text_parts.append(text)
            elif hasattr(child, 'name'):
                if child.name == 'strong' or child.name == 'b':
                    strong_text = self._clean_text(child.get_text())
                    if strong_text:
                        text_parts.append(f"**{strong_text}**")
                elif child.name == 'em' or child.name == 'i':
                    em_text = self._clean_text(child.get_text())
                    if em_text:
                        text_parts.append(f"*{em_text}*")
                elif child.name == 'code':
                    code_text = self._clean_text(child.get_text())
                    if code_text:
                        text_parts.append(f"`{code_text}`")
                elif child.name == 'a':
                    link_text = self._clean_text(child.get_text())
                    href = child.get('href', '')
                    if link_text:
                        if href:
                            text_parts.append(f"[{link_text}]({href})")
                        else:
                            text_parts.append(link_text)
                else:
                    # Para outros elementos, extrair apenas o texto
                    other_text = self._clean_text(child.get_text())
                    if other_text:
                        text_parts.append(other_text)
        
        paragraph_text = ' '.join(text_parts).strip()
        return paragraph_text if paragraph_text else None
    
    def process_list(self, list_element: Tag) -> Optional[str]:
        """
        Processa listas (ul/ol).
        
        Args:
            list_element: Elemento de lista
            
        Returns:
            Lista formatada em Markdown ou None
        """
        list_items = []
        is_ordered = list_element.name == 'ol'
        
        for i, li in enumerate(list_element.find_all('li', recursive=False), 1):
            item_text = self._clean_text(li.get_text())
            if item_text:
                if is_ordered:
                    list_items.append(f"{i}. {item_text}")
                else:
                    list_items.append(f"- {item_text}")
        
        return '\n'.join(list_items) if list_items else None
    
    def process_blockquote(self, blockquote_element: Tag) -> Optional[str]:
        """
        Processa citações.
        
        Args:
            blockquote_element: Elemento de citação
            
        Returns:
            Citação formatada em Markdown ou None
        """
        quote_text = self._clean_text(blockquote_element.get_text())
        if quote_text:
            # Dividir em linhas e adicionar > no início de cada uma
            lines = quote_text.split('\n')
            quoted_lines = [f"> {line}" if line.strip() else ">" for line in lines]
            return '\n'.join(quoted_lines)
        
        return None
    
    def extract_navigation_links(self, content: Tag) -> Optional[str]:
        """
        Extrai links de navegação/índice se existirem.
        
        Args:
            content: Container de conteúdo
            
        Returns:
            Links formatados em Markdown ou None
        """
        nav_lists = []
        
        # Procurar por ul/ol que contenham links
        lists = content.find_all(['ul', 'ol'])
        for list_elem in lists:
            links = list_elem.find_all('a')
            if len(links) >= 2:  # Se tem pelo menos 2 links, provavelmente é navegação
                nav_items = []
                for link in links:
                    link_text = self._clean_text(link.get_text())
                    href = link.get('href', '')
                    if link_text:
                        if href.startswith('#'):
                            nav_items.append(f"- [{link_text}]({href})")
                        else:
                            nav_items.append(f"- {link_text}")
                
                if nav_items:
                    nav_lists.append('\n'.join(nav_items))
        
        return '\n\n'.join(nav_lists) if nav_lists else None
    
    def extract_main_title(self, content: Tag) -> Optional[str]:
        """
        Extrai o título principal da página.
        
        Args:
            content: Container de conteúdo
            
        Returns:
            Título extraído ou None
        """
        # Procurar por h1 primeiro
        h1 = content.find('h1')
        if h1:
            title_text = self._clean_text(h1.get_text())
            if title_text:
                return title_text
        
        # Fallback: procurar por outros headings
        for heading_tag in ['h2', 'h3']:
            heading = content.find(heading_tag)
            if heading:
                title_text = self._clean_text(heading.get_text())
                if title_text:
                    return title_text
        
        return None
    
    def extract_initial_description(self, content: Tag) -> Optional[str]:
        """
        Extrai a descrição inicial do componente.
        
        Args:
            content: Container de conteúdo
            
        Returns:
            Descrição extraída ou None
        """
        # Procurar pelo primeiro parágrafo após o título
        h1 = content.find('h1')
        if h1:
            # Procurar próximo elemento que seja um parágrafo
            next_element = h1.find_next_sibling()
            while next_element:
                if next_element.name == 'p':
                    desc_text = self._clean_text(next_element.get_text())
                    if desc_text:
                        return desc_text
                elif next_element.name in ['h2', 'h3', 'h4']:
                    # Se encontrou outro heading, parar
                    break
                next_element = next_element.find_next_sibling()
        
        # Fallback: primeiro parágrafo da página
        first_p = content.find('p')
        if first_p:
            desc_text = self._clean_text(first_p.get_text())
            if desc_text:
                return desc_text
        
        return None
    
    def _clean_text(self, text: str) -> str:
        """
        Limpa e normaliza texto.
        
        Args:
            text: Texto para limpar
            
        Returns:
            Texto limpo
        """
        if not text:
            return ""
        
        # Remover espaços extras e quebras de linha desnecessárias
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Remover caracteres de controle
        cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)
        
        return cleaned 