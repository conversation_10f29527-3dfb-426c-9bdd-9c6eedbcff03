"""
Image Parser

Parser específico para extrair informações de componentes de imagem do Storybook.
Inclui: Animation, Flags, Icons, Illustration e Logos.
"""

import logging
import re
from typing import Dict, List, Any
from bs4 import BeautifulSoup, Tag

logger = logging.getLogger(__name__)

class ImageParser:
    """Parser específico para componentes de imagem do Storybook"""
    
    def __init__(self):
        self.logger = logger
    
    def parse_image_component(self, html_content: str, component_name: str = "") -> Dict[str, Any]:
        """
        Extrai informações estruturadas de um componente de imagem do Storybook
        
        Args:
            html_content: Conteúdo HTML do Storybook
            component_name: Nome do componente
            
        Returns:
            Dicionário com informações estruturadas do componente de imagem
        """
        logger.info(f"Parsing image component: {component_name}")
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Encontrar área principal
        content = self._find_main_content(soup)
        
        # Determinar tipo de imagem baseado no nome
        image_type = self._determine_image_type(component_name)
        
        # Extrair informações básicas
        result = {
            "component_name": self._extract_name(content) or component_name,
            "description": self._extract_description(content),
            "image_type": image_type,
            "usage_instructions": self._extract_usage_instructions(content),
            "size_options": self._extract_size_options(content),
            "code_example": self._extract_code_example(content),
            "items": self._extract_items(content, image_type),
            "total_items": 0
        }
        
        # Contar total de itens
        if result["items"]:
            result["total_items"] = len(result["items"])
        
        return result
    
    def _find_main_content(self, soup: BeautifulSoup) -> Tag:
        """Encontra o conteúdo principal do componente"""
        # Buscar por div com classe sbdocs-content
        content = soup.find('div', class_='sbdocs-content')
        if content:
            return content
        
        # Fallback: buscar por div principal
        content = soup.find('div', class_=re.compile(r'sbdocs'))
        if content:
            return content
        
        # Último fallback: usar o body
        return soup.find('body') or soup
    
    def _extract_name(self, content: Tag) -> str:
        """Extrai o nome do componente"""
        # Buscar por título principal
        title = content.find('h1', class_='sbdocs-title')
        if title:
            return title.get_text(strip=True)
        
        # Buscar por qualquer h1
        title = content.find('h1')
        if title:
            return title.get_text(strip=True)
        
        return ""
    
    def _extract_description(self, content: Tag) -> str:
        """Extrai a descrição do componente"""
        # Buscar por parágrafo após o título
        paragraphs = content.find_all('p')
        if paragraphs:
            # Pegar o primeiro parágrafo que não seja muito curto
            for p in paragraphs:
                text = p.get_text(strip=True)
                if len(text) > 20:  # Descrições geralmente são mais longas
                    return text
        
        return ""
    
    def _determine_image_type(self, component_name: str) -> str:
        """Determina o tipo de imagem baseado no nome"""
        name_lower = component_name.lower()
        
        if 'icon' in name_lower:
            return 'icons'
        elif 'logo' in name_lower:
            return 'logos'
        elif 'flag' in name_lower:
            return 'flags'
        elif 'animation' in name_lower:
            return 'animation'
        elif 'illustration' in name_lower:
            return 'illustration'
        else:
            return 'images'
    
    def _extract_usage_instructions(self, content: Tag) -> List[str]:
        """Extrai instruções de uso"""
        instructions = []
        
        # Buscar por seções de instruções
        instruction_sections = content.find_all(['h3', 'h4'], string=re.compile(r'instrução|uso|como usar', re.IGNORECASE))
        
        for section in instruction_sections:
            # Pegar o texto da seção e parágrafos seguintes
            section_text = section.get_text(strip=True)
            if section_text:
                instructions.append(section_text)
            
            # Buscar parágrafos seguintes até encontrar outro título
            next_elem = section.find_next_sibling()
            while next_elem and next_elem.name not in ['h1', 'h2', 'h3', 'h4']:
                if next_elem.name == 'p':
                    text = next_elem.get_text(strip=True)
                    if text:
                        instructions.append(text)
                next_elem = next_elem.find_next_sibling()
        
        return instructions
    
    def _extract_size_options(self, content: Tag) -> Dict[str, str]:
        """Extrai opções de tamanho"""
        size_options = {}
        
        # Buscar por tabela de tamanhos
        tables = content.find_all('table')
        for table in tables:
            # Verificar se a tabela contém informações de tamanho
            headers = table.find_all('th')
            if headers:
                header_texts = [h.get_text(strip=True).lower() for h in headers]
                if any('width' in text or 'size' in text for text in header_texts):
                    rows = table.find_all('tr')[1:]  # Pular cabeçalho
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            class_name = cells[0].get_text(strip=True)
                            size_value = cells[1].get_text(strip=True)
                            size_options[class_name] = size_value
        
        return size_options
    
    def _extract_code_example(self, content: Tag) -> str:
        """Extrai exemplo de código"""
        # Buscar por elementos pre com código
        pre_elements = content.find_all('pre')
        
        for pre in pre_elements:
            # Buscar por código HTML dentro do pre
            code_elements = pre.find_all(['div', 'span'], class_=re.compile(r'language-html'))
            if code_elements:
                # Extrair o texto do código
                code_text = ""
                for code_elem in code_elements:
                    code_text += code_elem.get_text()
                
                if code_text.strip():
                    return code_text.strip()
        
        # Fallback: buscar por qualquer texto em pre
        for pre in pre_elements:
            text = pre.get_text(strip=True)
            if text and ('<' in text or 'class=' in text):
                return text
        
        return ""
    
    def _extract_items(self, content: Tag, image_type: str) -> List[Dict[str, str]]:
        """Extrai lista de itens de imagem"""
        items = []
        
        # Buscar por containers de itens baseado no tipo
        if image_type == "logos":
            items = self._extract_logos_items(content)
        elif image_type == "icons":
            items = self._extract_icons_items(content)
        elif image_type == "flags":
            items = self._extract_flags_items(content)
        elif image_type == "animation":
            items = self._extract_animation_items(content)
        elif image_type == "illustration":
            items = self._extract_illustration_items(content)
        else:
            items = self._extract_generic_items(content)
        
        return items
    
    def _extract_logos_items(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai items de logos"""
        items = []
        
        # Buscar por divs que contenham logos
        logo_containers = content.find_all('div', class_=re.compile(r'brad-logo__card'))
        
        for container in logo_containers:
            # Extrair nome do logo
            name_elem = container.find('p', class_=re.compile(r'brad-font-subtitle'))
            if name_elem:
                logo_name = name_elem.get_text(strip=True)
                
                # Extrair elemento img que contém a classe do logo
                img_elem = container.find('img')
                if img_elem:
                    logo_classes = img_elem.get('class', [])
                    # Filtrar apenas a classe do logo (que começa com brad-logo__)
                    logo_class = [cls for cls in logo_classes if cls.startswith('brad-logo__')]
                    if logo_class:
                        logo_class = logo_class[0]
                        
                        # Gerar código HTML
                        html_code = f'<img class="brad-logo brad-size-sm {logo_class}">'
                        
                        items.append({
                            "name": logo_name,
                            "class": logo_class,
                            "html_code": html_code
                        })
        
        return items
    
    def _extract_icons_items(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai items de ícones"""
        items = []
        
        # Buscar por divs que contenham ícones
        icon_containers = content.find_all('div', class_=re.compile(r'brad-card'))
        
        for container in icon_containers:
            # Extrair nome do ícone
            name_elem = container.find('p', class_=re.compile(r'brad-font-subtitle'))
            if name_elem:
                icon_name = name_elem.get_text(strip=True)
                
                # Extrair elemento em que contém a classe do ícone
                em_elem = container.find('em')
                if em_elem:
                    icon_classes = em_elem.get('class', [])
                    # Filtrar apenas a classe do ícone (que começa com icon-)
                    icon_class = [cls for cls in icon_classes if cls.startswith('icon-')]
                    if icon_class:
                        icon_class = icon_class[0]
                        
                        # Gerar código HTML
                        html_code = f'<em class="{icon_class} brad-icon-size-xxs"></em>'
                        
                        items.append({
                            "name": icon_name,
                            "class": icon_class,
                            "html_code": html_code
                        })
        
        return items
    
    def _extract_flags_items(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai items de flags"""
        items = []
        
        # Buscar por divs que contenham flags - usar dois hífens (brad-flag--card)
        flag_containers = content.find_all('div', class_=re.compile(r'brad-flag--card'))
        
        for container in flag_containers:
            # Extrair nome da flag
            name_elem = container.find('p', class_=re.compile(r'brad-font-subtitle'))
            if name_elem:
                flag_name = name_elem.get_text(strip=True)
                
                # Extrair elemento img que contém a classe da flag
                img_elem = container.find('img')
                if img_elem:
                    flag_classes = img_elem.get('class', [])
                    # Filtrar apenas a classe da flag (que começa com brad-flag-)
                    flag_class = [cls for cls in flag_classes if cls.startswith('brad-flag-') and cls != 'brad-flag']
                    if flag_class:
                        flag_class = flag_class[0]
                        
                        # Gerar código HTML
                        html_code = f'<img class="brad-flag {flag_class}">'
                        
                        items.append({
                            "name": flag_name,
                            "class": flag_class,
                            "html_code": html_code
                        })
        
        return items
    
    def _extract_animation_items(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai items de animações"""
        items = []
        
        # Buscar por divs que contenham animações - procurar por divs com id que começa com brad-animation
        animation_containers = content.find_all('div', id=re.compile(r'brad-animation'))
        
        for container in animation_containers:
            # Extrair nome da animação do id
            animation_id = container.get('id', '')
            if animation_id.startswith('brad-animation-'):
                animation_name = animation_id.replace('brad-animation-', '')
                
                # Gerar código HTML usando o id
                html_code = f'<div id="{animation_id}"></div>'
                
                items.append({
                    "name": animation_name,
                    "class": animation_id,
                    "html_code": html_code
                })
        
        return items
    
    def _extract_illustration_items(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai items de ilustrações"""
        items = []
        
        # Buscar por divs que contenham ilustrações - usar brad-ilustration--card (com dois hífens)
        illustration_containers = content.find_all('div', class_=re.compile(r'brad-ilustration--card'))
        
        for container in illustration_containers:
            # Extrair nome da ilustração
            name_elem = container.find('p', class_=re.compile(r'brad-font-subtitle'))
            if name_elem:
                illustration_name = name_elem.get_text(strip=True)
                
                # Extrair elemento img que contém a classe da ilustração
                img_elem = container.find('img', class_=re.compile(r'brad-illustration__content'))
                if img_elem:
                    ill_classes = img_elem.get('class', [])
                    # Filtrar apenas a classe da ilustração
                    ill_class = [cls for cls in ill_classes if cls.startswith('brad-illustration__content--')]
                    if ill_class:
                        ill_class = ill_class[0]
                        
                        # Gerar código HTML
                        html_code = f'<img class="brad-illustration__content {ill_class}">'
                        
                        items.append({
                            "name": illustration_name,
                            "class": ill_class,
                            "html_code": html_code
                        })
        
        return items
    
    def _extract_generic_items(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai items genéricos para categorias não específicas"""
        items = []
        
        # Buscar por elementos que possam conter items
        containers = content.find_all('div', class_=re.compile(r'card|item'))
        
        for container in containers:
            # Tentar extrair nome e classe
            name_elem = container.find(['p', 'span', 'div'])
            if name_elem:
                name = name_elem.get_text(strip=True)
                
                # Buscar por elemento que contenha a classe
                class_elem = container.find(['em', 'img', 'div'])
                if class_elem:
                    classes = class_elem.get('class', [])
                    if classes:
                        main_class = classes[0]
                        html_code = f'<div class="{main_class}"></div>'
                        
                        items.append({
                            "name": name,
                            "class": main_class,
                            "html_code": html_code
                        })
        
        return items 