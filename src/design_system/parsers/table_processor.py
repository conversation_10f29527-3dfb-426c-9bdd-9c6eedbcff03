"""
Processador de tabelas para conversão de tabelas HTML do Storybook para Markdown.

Este módulo foca em extrair e converter tabelas HTML para formato Markdown,
incluindo processamento de controles do Storybook.
"""

import logging
import re
from typing import List, Optional
from bs4 import Tag

logger = logging.getLogger(__name__)

class TableProcessor:
    """
    Processador especializado em extrair e converter tabelas HTML para Markdown.
    Inclui processamento de controles do Storybook.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def process_table(self, table_element: Tag) -> Optional[str]:
        """
        Processa tabelas convertendo para formato Markdown.
        
        Args:
            table_element: Elemento table
            
        Returns:
            Tabela formatada em Markdown ou None
        """
        try:
            # Extrair cabeçalho
            headers = self._extract_headers(table_element)
            
            # Extrair linhas do corpo
            rows = self._extract_rows(table_element)
            
            # Processar controles nas células
            processed_rows = self._process_control_cells(rows)
            
            # Gerar markdown
            return self._generate_markdown_table(headers, processed_rows)
            
        except Exception as e:
            self.logger.warning(f"Erro ao processar tabela: {e}")
            # Fallback: extrair texto simples
            table_text = self._clean_text(table_element.get_text())
            return f"```\n{table_text}\n```" if table_text else None
    
    def _extract_headers(self, table: Tag) -> List[str]:
        """
        Extrai cabeçalhos da tabela.
        
        Args:
            table: Elemento table
            
        Returns:
            Lista de cabeçalhos
        """
        headers = []
        thead = table.find('thead')
        if thead:
            header_row = thead.find('tr')
            if header_row:
                for th in header_row.find_all('th'):
                    headers.append(th.get_text(strip=True))
        
        return headers
    
    def _extract_rows(self, table: Tag) -> List[List[str]]:
        """
        Extrai linhas de dados da tabela.
        
        Args:
            table: Elemento table
            
        Returns:
            Lista de linhas de dados
        """
        rows = []
        tbody = table.find('tbody') or table
        for tr in tbody.find_all('tr'):
            if tr.parent.name == 'thead':  # Pular se for header
                continue
            
            row_cells = []
            for td in tr.find_all(['td', 'th']):
                cell_text = self._clean_text(td.get_text())
                row_cells.append(cell_text or '')
            
            if row_cells:
                rows.append(row_cells)
        
        return rows
    
    def _process_control_cells(self, rows: List[List[str]]) -> List[List[str]]:
        """
        Processa células que contêm controles do Storybook.
        Converte controles em listas de opções legíveis.
        
        Args:
            rows: Linhas de dados da tabela
            
        Returns:
            Linhas processadas
        """
        processed_rows = []
        
        for row in rows:
            processed_row = []
            for cell in row:
                processed_cell = self._process_control_cell(cell)
                processed_row.append(processed_cell)
            processed_rows.append(processed_row)
        
        return processed_rows
    
    def _process_control_cell(self, cell_text: str) -> str:
        """
        Processa uma célula que pode conter controles do Storybook.
        
        Args:
            cell_text: Texto da célula
            
        Returns:
            Texto processado com opções expandidas
        """
        if not cell_text:
            return cell_text
        
        # Detectar e processar diferentes tipos de controles
        cell_text = self._process_select_control(cell_text)
        cell_text = self._process_checkbox_control(cell_text)
        cell_text = self._process_raw_control(cell_text)
        
        return cell_text
    
    def _process_select_control(self, cell_text: str) -> str:
        """
        Processa controles do tipo select.
        Converte "Choose option...daymonthyear" em "day | month | year"
        
        Args:
            cell_text: Texto da célula
            
        Returns:
            Texto processado
        """
        # Padrão para detectar selects com opções concatenadas
        select_pattern = r'Choose option\.\.\.([a-zA-Z]+)'
        match = re.search(select_pattern, cell_text)
        
        if match:
            options = match.group(1)
            # Separar opções (assumindo que são concatenadas sem espaços)
            separated_options = self._separate_camel_case(options)
            return ' | '.join(separated_options)
        
        return cell_text
    
    def _process_checkbox_control(self, cell_text: str) -> str:
        """
        Processa controles do tipo checkbox.
        Converte "FalseTrue" em "False | True"
        
        Args:
            cell_text: Texto da célula
            
        Returns:
            Texto processado
        """
        if cell_text in ['FalseTrue', 'TrueFalse']:
            return 'False | True'
        
        return cell_text
    
    def _process_raw_control(self, cell_text: str) -> str:
        """
        Processa controles RAW do Storybook.
        Tenta extrair informações úteis de objetos JSON.
        
        Args:
            cell_text: Texto da célula
            
        Returns:
            Texto processado
        """
        if cell_text.startswith('RAW'):
            # Tentar extrair informações úteis do RAW
            raw_content = cell_text.replace('RAW', '').strip()
            
            # Se contém estrutura de objeto, tentar extrair valores
            if ':' in raw_content and '{' in raw_content:
                # Extrair valores simples
                values = re.findall(r':\s*([^,\s}]+)', raw_content)
                if values:
                    return ' | '.join(values)
            
            return raw_content
        
        return cell_text
    
    def _separate_camel_case(self, text: str) -> List[str]:
        """
        Separa texto em camelCase em palavras individuais.
        Ex: "daymonthyear" -> ["day", "month", "year"]
        
        Args:
            text: Texto em camelCase
            
        Returns:
            Lista de palavras separadas
        """
        # Lista de palavras comuns para ajudar na separação
        common_words = [
            'day', 'month', 'year', 'week', 'hour', 'minute', 'second',
            'true', 'false', 'yes', 'no', 'on', 'off', 'up', 'down',
            'left', 'right', 'top', 'bottom', 'center', 'middle',
            'small', 'medium', 'large', 'tiny', 'huge', 'big', 'little'
        ]
        
        # Tentar separar por palavras conhecidas
        result = []
        remaining = text.lower()
        
        for word in common_words:
            if word in remaining:
                result.append(word)
                remaining = remaining.replace(word, '')
        
        # Se ainda há texto restante, adicionar como está
        if remaining:
            result.append(remaining)
        
        return result if result else [text]
    
    def _generate_markdown_table(self, headers: List[str], rows: List[List[str]]) -> str:
        """
        Gera tabela em formato Markdown.
        
        Args:
            headers: Lista de cabeçalhos
            rows: Lista de linhas de dados
            
        Returns:
            Tabela em formato Markdown
        """
        if not headers:
            return ""
        
        markdown_lines = []
        
        # Linha de cabeçalho
        markdown_lines.append('| ' + ' | '.join(headers) + ' |')
        
        # Linha separadora
        separator = '| ' + ' | '.join(['---'] * len(headers)) + ' |'
        markdown_lines.append(separator)
        
        # Linhas de dados
        for row in rows:
            # Ajustar número de colunas se necessário
            while len(row) < len(headers):
                row.append('')
            row = row[:len(headers)]  # Truncar se tiver mais colunas
            
            markdown_lines.append('| ' + ' | '.join(row) + ' |')
        
        return '\n'.join(markdown_lines)
    
    def _clean_text(self, text: str) -> str:
        """
        Limpa e normaliza texto.
        
        Args:
            text: Texto para limpar
            
        Returns:
            Texto limpo
        """
        if not text:
            return ""
        
        # Remover espaços extras e quebras de linha desnecessárias
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Remover caracteres de controle
        cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned)
        
        return cleaned 