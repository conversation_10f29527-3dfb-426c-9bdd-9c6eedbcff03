#!/usr/bin/env python3
"""
Parser JSON para classes utilitárias do design system
"""

import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup, Tag


class ClassesJsonParser:
    """Parser JSON para classes utilitárias"""
    
    def __init__(self):
        pass
    
    def parse_classes_component(self, html_content: str, component_name: str = "") -> Dict[str, Any]:
        """Parse um componente de classes para formato JSON"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Encontrar área principal
        content = self._find_main_content(soup)
        
        # Extrair informações básicas
        title = self._extract_title(content)
        description = self._extract_description(content)
        
        # Extrair seções com títulos (h2-h5)
        sections = self._extract_sections_with_titles(content)
        
        # Determinar tipo de classe baseado no nome
        class_type = self._determine_class_type(component_name)
        
        # Extrair exemplos baseado no tipo
        examples = self._extract_examples(content, class_type)
        
        # Extrair código de exemplo
        code_examples = self._extract_code_examples(content)
        
        # Extrair tabela de propriedades se existir
        properties_table = self._extract_properties_table(content)
        
        return {
            "component_name": title,
            "description": description,
            "sections": sections,
            "class_type": class_type,
            "examples": examples,
            "code_examples": code_examples,
            "properties_table": properties_table,
            "total_examples": len(examples)
        }
    
    def parse_classes(self, html_content: str, component_name: str = "") -> Dict[str, Any]:
        """Método de compatibilidade para o storybook_extractor"""
        return self.parse_classes_component(html_content, component_name)
    
    def _find_main_content(self, soup: BeautifulSoup) -> Tag:
        """Encontra o conteúdo principal do componente"""
        # Buscar por div com classe sbdocs-content
        content = soup.find('div', class_='sbdocs-content')
        if content:
            return content
        
        # Fallback: buscar por div principal
        content = soup.find('div', class_=re.compile(r'sbdocs'))
        if content:
            return content
        
        # Último fallback: usar o body
        return soup.find('body') or soup
    
    def _extract_title(self, content: Tag) -> str:
        """Extrai o título do componente"""
        # Buscar por h1 com classe sbdocs-title
        title_elem = content.find('h1', class_='sbdocs-title')
        if title_elem:
            return title_elem.get_text(strip=True)
        
        # Buscar por qualquer h1
        title_elem = content.find('h1')
        if title_elem:
            return title_elem.get_text(strip=True)
        
        return ""
    
    def _extract_description(self, content: Tag) -> str:
        """Extrai a descrição do componente"""
        # Buscar por parágrafo após o título
        paragraphs = content.find_all('p')
        if paragraphs:
            # Pegar o primeiro parágrafo que não seja muito curto
            for p in paragraphs:
                text = p.get_text(strip=True)
                if len(text) > 20:  # Descrições geralmente são mais longas
                    return text
        
        return ""
    
    def _extract_sections_with_titles(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai seções com títulos (h2-h5) e seus textos"""
        sections = []
        
        # Buscar por todos os títulos h2-h5
        headings = content.find_all(['h2', 'h3', 'h4', 'h5'])
        
        for heading in headings:
            section = {
                "title": heading.get_text(strip=True),
                "level": heading.name,
                "content": ""
            }
            
            # Extrair texto até o próximo título
            next_elem = heading.find_next_sibling()
            content_text = []
            
            while next_elem and next_elem.name not in ['h1', 'h2', 'h3', 'h4', 'h5']:
                if next_elem.name in ['p', 'div', 'span']:
                    text = next_elem.get_text(strip=True)
                    if text:
                        content_text.append(text)
                next_elem = next_elem.find_next_sibling()
            
            section["content"] = " ".join(content_text)
            sections.append(section)
        
        return sections
    
    def _determine_class_type(self, component_name: str) -> str:
        """Determina o tipo de classe baseado no nome"""
        if "flex" in component_name.lower():
            return "flex"
        elif "color" in component_name.lower():
            return "colors"
        elif "glassmorphism" in component_name.lower():
            return "glassmorphism"
        elif "gradient" in component_name.lower():
            return "gradients"
        elif "skeleton" in component_name.lower():
            return "skeleton"
        elif "spacing" in component_name.lower():
            return "spacing"
        elif "waves" in component_name.lower():
            return "waves"
        elif "zindex" in component_name.lower():
            return "zindex"
        else:
            return "generic"
    
    def _extract_examples(self, soup: BeautifulSoup, class_type: str) -> Dict[str, List[Dict[str, str]]]:
        """Extrai exemplos organizados por seção usando títulos existentes"""
        examples_by_section = {}
        
        if class_type == "colors":
            # Para cores, buscar por todos os cards de cores primeiro
            all_color_cards = soup.find_all('div', class_=re.compile(r'brad-color-doc__card'))
            
            if all_color_cards:
                # Organizar por seções baseado nos títulos próximos
                current_section = "Colors"  # Seção padrão
                
                for card in all_color_cards:
                    # Buscar o título mais próximo antes do card
                    prev_elem = card.find_previous(['h2', 'h3', 'h4', 'h5'])
                    if prev_elem:
                        section_title = prev_elem.get_text(strip=True)
                        # Filtrar títulos que são seções de cores
                        if any(keyword in section_title.lower() for keyword in ['colors', 'extended', 'neutral']):
                            current_section = section_title
                    
                    # Extrair informações do card
                    color_info = self._extract_color_card_info(card)
                    if color_info:
                        if current_section not in examples_by_section:
                            examples_by_section[current_section] = []
                        examples_by_section[current_section].append(color_info)
        else:
            # Para outros tipos, buscar por títulos h2-h5 que podem conter exemplos
            headings = soup.find_all(['h2', 'h3', 'h4', 'h5'])
            
            for heading in headings:
                section_title = heading.get_text(strip=True)
                
                # Pular títulos que não são seções de exemplos
                if any(skip_word in section_title.lower() for skip_word in ['aplicação', 'variantes', 'obervações', 'como copiar', 'exemplo']):
                    continue
                
                # Buscar exemplos na seção atual
                section_examples = []
                
                # Buscar por elementos de exemplo após este título até o próximo título
                current = heading.find_next_sibling()
                while current and current.name not in ['h1', 'h2', 'h3', 'h4', 'h5']:
                    if current.name in ['div', 'span']:
                        classes = current.get('class', [])
                        brad_classes = [cls for cls in classes if cls.startswith('brad-')]
                        if brad_classes:
                            text = current.get_text(strip=True)
                            section_examples.append({
                                "name": text or brad_classes[0],
                                "class": brad_classes[0],
                                "html_code": f'<{current.name} class="{brad_classes[0]}">{text}</{current.name}>'
                            })
                    
                    current = current.find_next_sibling()
                
                if section_examples:
                    examples_by_section[section_title] = section_examples
        
        return examples_by_section
    
    def _extract_colors_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de classes de cores em formato de tabela"""
        examples = []
        
        # Buscar por elementos que contenham classes brad-cc- (color combinations)
        color_elements = soup.find_all(['div', 'span', 'p'], class_=re.compile(r'brad-cc-'))
        
        for elem in color_elements:
            classes = elem.get('class', [])
            color_classes = [cls for cls in classes if cls.startswith('brad-cc-')]
            
            if color_classes:
                # Extrair informações de cor
                text = elem.get_text(strip=True)
                
                # Buscar por informações HEX/RGB
                hex_info = elem.find(text=re.compile(r'#[0-9a-fA-F]{6}'))
                rgb_info = elem.find(text=re.compile(r'rgb\([^)]+\)'))
                
                # Determinar tema associado
                theme = self._find_associated_theme(elem, [])
                
                color_info = {
                    "name": text or color_classes[0],
                    "class": color_classes[0],
                    "html_code": f'<{elem.name} class="{color_classes[0]}">{text}</{elem.name}>',
                    "theme": theme
                }
                
                if hex_info:
                    color_info["hex"] = hex_info.strip()
                if rgb_info:
                    color_info["rgb"] = rgb_info.strip()
                
                examples.append(color_info)
        
        # Se não encontrou classes brad-cc-, buscar por outras classes de cor
        if not examples:
            # Buscar por cards de cores (brad-color-doc__card)
            color_cards = soup.find_all('div', class_=re.compile(r'brad-color-doc__card'))
            
            for card in color_cards:
                color_info = self._extract_color_card_info(card)
                if color_info:
                    examples.append(color_info)
        
        return examples
    
    def _extract_color_card_info(self, card: Tag) -> Optional[Dict[str, str]]:
        """Extrai informações de um card de cor"""
        try:
            # Extrair ID do card (nome da cor)
            card_id = card.get('id', '')
            if not card_id:
                return None
            
            # Extrair informações dos parágrafos dentro do card
            info_paragraphs = card.find_all('p', class_='brad-color-doc__card__info')
            
            color_data = {
                "name": card_id,
                "background": "",
                "text": "",
                "border": "",
                "hex": "",
                "rgb": ""
            }
            
            for p in info_paragraphs:
                text = p.get_text(strip=True)
                span = p.find('span')
                if span:
                    value = span.get_text(strip=True)
                    
                    if "Background:" in text:
                        color_data["background"] = value
                    elif "Texto:" in text:
                        color_data["text"] = value
                    elif "Border:" in text:
                        color_data["border"] = value
                    elif "HEX:" in text:
                        color_data["hex"] = value
                    elif "RGB:" in text:
                        color_data["rgb"] = value
            
            # Gerar HTML do card
            color_data["html_code"] = f'<div class="brad-color-doc__card" id="{card_id}">...</div>'
            
            return color_data
            
        except Exception as e:
            print(f"Erro ao extrair informações do card de cor: {e}")
            return None
    
    def _extract_code_box_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de caixas de código (glassmorphism, gradients, etc.)"""
        examples = []
        
        # Buscar por caixas de código
        code_boxes = soup.find_all('div', class_=re.compile(r'docblock-source|css-'))
        
        for box in code_boxes:
            # Buscar por código dentro da caixa
            code_elem = box.find(['pre', 'code'])
            if code_elem:
                code_text = code_elem.get_text(strip=True)
                if code_text:
                    # Extrair classes do código
                    class_matches = re.findall(r'brad-[a-zA-Z-]+', code_text)
                    if class_matches:
                        examples.append({
                            "name": class_matches[0],
                            "class": class_matches[0],
                            "html_code": code_text
                        })
        
        return examples
    
    def _extract_generic_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos genéricos"""
        examples = []
        
        # Buscar por elementos que contenham classes brad-
        brad_elements = soup.find_all(['div', 'span'], class_=re.compile(r'brad-'))
        
        for elem in brad_elements:
            classes = elem.get('class', [])
            brad_classes = [cls for cls in classes if cls.startswith('brad-')]
            
            if brad_classes:
                text = elem.get_text(strip=True)
                examples.append({
                    "name": text or brad_classes[0],
                    "class": brad_classes[0],
                    "html_code": f'<{elem.name} class="{brad_classes[0]}">{text}</{elem.name}>'
                })
        
        return examples
    
    def _extract_code_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de código HTML"""
        code_examples = []
        
        # Buscar por elementos pre com código
        pre_elements = soup.find_all('pre', class_='prismjs')
        
        for pre in pre_elements:
            code_elem = pre.find('div', class_='language-html')
            if code_elem:
                code_text = code_elem.get_text(strip=True)
                if code_text:
                    code_examples.append({
                        "title": "Exemplo de código",
                        "code": code_text,
                        "language": "html"
                    })
        
        return code_examples
    
    def _extract_properties_table(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extrai tabela de propriedades se existir"""
        table = soup.find('table', class_='docblock-argstable')
        if not table:
            return {}
        
        properties = {}
        
        # Extrair headers
        headers = []
        header_row = table.find('thead')
        if header_row:
            header_cells = header_row.find_all('th')
            headers = [cell.get_text(strip=True) for cell in header_cells]
        
        # Extrair dados
        rows = table.find('tbody').find_all('tr') if table.find('tbody') else []
        
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 2:
                prop_name = cells[0].get_text(strip=True)
                prop_desc = cells[1].get_text(strip=True) if len(cells) > 1 else ""
                prop_default = cells[2].get_text(strip=True) if len(cells) > 2 else ""
                
                properties[prop_name] = {
                    "description": prop_desc,
                    "default": prop_default
                }
        
        return properties
    
    def _extract_themes(self, soup: BeautifulSoup) -> List[str]:
        """Extrai lista de temas disponíveis"""
        themes = []
        
        # Buscar por h3 que contenham nomes de temas
        theme_headers = soup.find_all('h3', class_=re.compile(r'brad-font-subtitle-sm'))
        
        for header in theme_headers:
            theme_text = header.get_text(strip=True)
            if theme_text.startswith('brad-theme-'):
                # Extrair nome do tema
                theme_name = theme_text.replace('brad-theme-', '')
                themes.append(theme_name)
        
        return themes
    
    def _find_associated_theme(self, element, themes: List[str]) -> str:
        """Encontra o tema associado ao elemento"""
        # Buscar pelo elemento pai que contenha classe de tema
        current = element
        while current:
            classes = current.get('class', [])
            for cls in classes:
                if cls.startswith('brad-theme-'):
                    theme_name = cls.replace('brad-theme-', '')
                    return theme_name
            current = current.parent
        
        return "" 