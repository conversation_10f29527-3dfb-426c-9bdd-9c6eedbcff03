# -*- coding: utf-8 -*-
"""
Parser específico para componentes do Storybook.
"""

import logging
import re
from typing import Dict, List, Any
from bs4 import BeautifulSoup, Tag

logger = logging.getLogger(__name__)

class ComponentsParser:
    """Parser específico para componentes do Storybook"""
    
    def __init__(self):
        self.logger = logger
    
    def parse_component(self, html_content: str, component_name: str = "") -> Dict[str, Any]:
        """
        Extrai informações estruturadas de um componente do Storybook
        
        Args:
            html_content: Conteúdo HTML do Storybook
            component_name: Nome do componente
            
        Returns:
            Dicionário com informações estruturadas do componente
        """
        logger.info(f"Parsing component: {component_name}")
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Encontrar área principal
        content = self._find_main_content(soup)
        
        # Extrair informações básicas
        result = {
            "component_name": self._extract_name(content) or component_name,
            "description": self._extract_description(content),
            "purpose": "",
            "html_usage": self._extract_html_code(content),
            "javascript_usage": self._extract_js_code(content),
            "css_classes": self._extract_css_classes(content),
            "properties": self._extract_properties_table(content),
            "methods": self._extract_methods_table(content),
            "accessibility": self._extract_accessibility_text(content),
            "examples": self._extract_examples(content)
        }
        
        # Extrair propósito da descrição
        result["purpose"] = self._extract_purpose(result["description"])
        
        return result
    
    def _find_main_content(self, soup: BeautifulSoup) -> Tag:
        """Encontra área principal de conteúdo"""
        content_selectors = [
            'div.sbdocs-content',
            'div.sbdocs-wrapper', 
            '#storybook-docs',
            'main',
            'body'
        ]
        
        for selector in content_selectors:
            content = soup.select_one(selector)
            if content:
                return content
        
        return soup
    
    def _extract_name(self, content: Tag) -> str:
        """Extrai nome do componente"""
        # Buscar h1
        h1 = content.find('h1')
        if h1:
            name = h1.get_text(strip=True)
            if name and name != "Storybook":
                return name
        
        # Buscar por ID que contenha nome do componente
        component_keywords = [
            'button', 'accordion', 'dropdown', 'card', 'modal', 'tooltip',
            'bottomsheet', 'tabs', 'input', 'select', 'checkbox', 'radio',
            'slider', 'spinner', 'alert', 'badge', 'breadcrumb', 'carousel',
            'collapse', 'datepicker', 'form', 'grid', 'list', 'menu',
            'navigation', 'pagination', 'progress', 'table', 'toast', 'calendar'
        ]
        
        for elem in content.find_all(attrs={'id': True}):
            id_val = elem.get('id', '').lower()
            for keyword in component_keywords:
                if keyword in id_val:
                    text = elem.get_text(strip=True)
                    if text and len(text) < 100:
                        return text
        
        return "Unknown Component"
    
    def _extract_description(self, content: Tag) -> str:
        """Extrai descrição do componente"""
        h1 = content.find('h1')
        if not h1:
            return ""
        
        descriptions = []
        current = h1.next_sibling
        
        # Buscar próximos elementos após h1
        while current and len(descriptions) < 3:
            if hasattr(current, 'name') and current.name == 'p':
                text = current.get_text(strip=True)
                if text and len(text) > 20:
                    descriptions.append(text)
            elif hasattr(current, 'name') and current.name in ['h2', 'h3']:
                break
            
            try:
                current = current.next_sibling
            except:
                break
        
        return " ".join(descriptions)
    
    def _extract_purpose(self, description: str) -> str:
        """Extrai propósito do componente da descrição"""
        if not description:
            return ""
        
        # Padrões para identificar propósito
        patterns = [
            r'é utilizado para ([^.!]+)',
            r'serve para ([^.!]+)',
            r'usado para ([^.!]+)',
            r'utilizado quando ([^.!]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _extract_html_code(self, content: Tag) -> str:
        """Extrai código HTML do componente (apenas o primeiro exemplo válido)"""
        # Buscar especificamente em divs com classe "os-content" (formato do Storybook)
        os_content_divs = content.find_all('div', class_='os-content')
        
        for div in os_content_divs:
            # Buscar dentro do os-content por pre com prismjs
            pre_blocks = div.find_all('pre', class_=lambda x: x and 'prismjs' in x)
            for pre in pre_blocks:
                code = pre.get_text()
                if '<' in code and '>' in code:
                    # Verificar se contém classes do design system
                    if any(keyword in code for keyword in ['brad-', '<div', '<button', '<span', '<p', '<h']):
                        cleaned_code = self._clean_code(code)
                        if cleaned_code:
                            return cleaned_code
        
        # Fallback: buscar em outras estruturas
        code_blocks = content.find_all(['pre', 'code'])
        for block in code_blocks:
            code = block.get_text()
            if '<' in code and '>' in code:
                if any(keyword in code for keyword in ['brad-', '<div', '<button', '<span', '<p', '<h']):
                    cleaned_code = self._clean_code(code)
                    if cleaned_code:
                        return cleaned_code
        
        return ""
    
    def _extract_js_code(self, content: Tag) -> str:
        """Extrai código JavaScript do componente (apenas o primeiro exemplo válido)"""
        # Buscar especificamente em divs com classe "os-content" (formato do Storybook)
        os_content_divs = content.find_all('div', class_='os-content')
        
        for div in os_content_divs:
            # Buscar dentro do os-content por pre com prismjs
            pre_blocks = div.find_all('pre', class_=lambda x: x and 'prismjs' in x)
            for pre in pre_blocks:
                code = pre.get_text()
                # Filtrar apenas código JS (que contém palavras-chave JS)
                if any(keyword in code for keyword in ['const', 'function', 'getInstance', 'LiquidCorp', 'var', 'let', 'document', 'querySelector', 'addEventListener', 'onclick']):
                    # Verificar se não contém "Copy" (texto do botão)
                    if 'Copy' not in code:
                        cleaned_code = self._clean_code(code)
                        if cleaned_code:
                            return cleaned_code
        
        # Fallback: buscar em outras estruturas
        code_blocks = content.find_all(['pre', 'code'])
        for block in code_blocks:
            code = block.get_text()
            if any(keyword in code for keyword in ['const', 'function', 'getInstance', 'LiquidCorp', 'var', 'let', 'document', 'querySelector', 'addEventListener', 'onclick']):
                if 'Copy' not in code:
                    cleaned_code = self._clean_code(code)
                    if cleaned_code:
                        return cleaned_code
        
        return ""
    
    def _extract_css_classes(self, content: Tag) -> List[str]:
        """Extrai classes CSS do design system"""
        classes = set()
        
        # Buscar em todo o conteúdo HTML
        text_content = content.get_text()
        found_classes = re.findall(r'brad-[\w-]+', text_content)
        
        # Limpar e filtrar classes
        for class_name in found_classes:
            clean_class = self._clean_css_class(class_name)
            if clean_class:
                classes.add(clean_class)
        
        return sorted(list(classes))
    
    def _clean_css_class(self, class_name: str) -> str:
        """Limpa uma classe CSS removendo texto adicional concatenado"""
        if not class_name.startswith('brad-'):
            return ""
        
        # Padrões para identificar onde a classe CSS termina
        patterns = [
            r'(brad-[\w-]+?)(?:Componente|Sim|Não|string|boolean|number|Valor|Se|Tipo|Default|Descrição)',
            r'(brad-[\w-]+?)(?=[A-Z][a-z])',
            r'(brad-[\w-]+?)(?=\d)',
            r'(brad-[\w-]+?)(?=[^\w-])',
        ]
        
        for pattern in patterns:
            match = re.match(pattern, class_name)
            if match:
                return match.group(1)
        
        # Se não encontrou padrão, verificar se é uma classe válida
        if re.match(r'^brad-[\w-]+$', class_name):
            return class_name
        
        # Fallback: extrair apenas a parte que parece uma classe válida
        match = re.match(r'(brad-[\w-]+)', class_name)
        if match:
            return match.group(1)
        
        return ""
    
    def _extract_properties_table(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai tabela de propriedades/opções"""
        properties = []
        
        tables = content.find_all('table')
        
        for table in tables:
            try:
                # Buscar cabeçalhos
                headers = []
                header_row = table.find('tr')
                if header_row:
                    header_cells = header_row.find_all(['th', 'td'])
                    headers = [cell.get_text(strip=True).lower() for cell in header_cells]
                
                # Verificar se é tabela de propriedades
                if any(keyword in ' '.join(headers) for keyword in ['nome', 'tipo', 'descrição', 'classe', 'default']):
                    rows = table.find_all('tr')[1:]  # Pular cabeçalho
                    
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            cell_values = [cell.get_text(strip=True) for cell in cells]
                            
                            prop = {
                                "name": cell_values[0] if len(cell_values) > 0 else "",
                                "type": cell_values[1] if len(cell_values) > 1 else "",
                                "default": cell_values[2] if len(cell_values) > 2 else "",
                                "description": cell_values[-1] if len(cell_values) > 1 else ""
                            }
                            
                            if prop["name"]:
                                properties.append(prop)
            except Exception as e:
                logger.warning(f"Error parsing property table: {e}")
                continue
        
        return properties
    
    def _extract_methods_table(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai tabela de métodos"""
        methods = []
        
        tables = content.find_all('table')
        
        for table in tables:
            try:
                # Buscar cabeçalhos
                headers = []
                header_row = table.find('tr')
                if header_row:
                    header_cells = header_row.find_all(['th', 'td'])
                    headers = [cell.get_text(strip=True).lower() for cell in header_cells]
                
                # Verificar se é tabela de métodos
                if any(keyword in ' '.join(headers) for keyword in ['método', 'parâmetros', 'descrição']):
                    rows = table.find_all('tr')[1:]  # Pular cabeçalho
                    
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            cell_values = [cell.get_text(strip=True) for cell in cells]
                            
                            method = {
                                "name": cell_values[0] if len(cell_values) > 0 else "",
                                "parameters": cell_values[1] if len(cell_values) > 1 else "",
                                "description": cell_values[-1] if len(cell_values) > 1 else ""
                            }
                            
                            if method["name"]:
                                methods.append(method)
            except Exception as e:
                logger.warning(f"Error parsing method table: {e}")
                continue
        
        return methods
    
    def _extract_accessibility_text(self, content: Tag) -> str:
        """Extrai informações de acessibilidade"""
        accessibility_text = ""
        
        # Buscar por seções de acessibilidade
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if 'acessibilidade' in text or 'accessibility' in text:
                # Coletar conteúdo da seção
                content_section = []
                current = heading.find_next_sibling()
                
                while current and current.name not in ['h2', 'h3']:
                    if current.name in ['p', 'li']:
                        text = current.get_text(strip=True)
                        if text:
                            content_section.append(text)
                    current = current.find_next_sibling()
                
                if content_section:
                    accessibility_text = " ".join(content_section)
                    break
        
        return accessibility_text
    
    def _extract_examples(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai exemplos práticos (apenas exemplos válidos)"""
        examples = []
        
        # Buscar por divs que contenham exemplos reais
        story_divs = content.find_all('div', class_=lambda x: x and any(cls in str(x) for cls in ['sb-story', 'docs-story']))
        
        for div in story_divs:
            try:
                # Verificar se contém classes do design system
                if 'brad-' in str(div):
                    # Extrair nome do exemplo
                    name = div.get('data-name', '')
                    if not name:
                        # Buscar heading anterior
                        prev_heading = div.find_previous(['h3', 'h4', 'h2'])
                        if prev_heading:
                            name = prev_heading.get_text(strip=True)
                    
                    # Extrair HTML limpo
                    clean_html = self._extract_clean_html(div)
                    
                    if clean_html and name and len(clean_html) > 50:  # Verificar se tem conteúdo real
                        examples.append({
                            "name": name,
                            "html": clean_html
                        })
            except Exception as e:
                logger.debug(f"Erro ao extrair exemplo: {e}")
                continue
        
        # Limitar a apenas 3 exemplos mais relevantes
        return examples[:3]
    
    def _extract_clean_html(self, element: Tag) -> str:
        """Extrai HTML limpo de um elemento"""
        try:
            # Buscar div interno com conteúdo real
            inner_div = element.find('div')
            while inner_div:
                inner_html = str(inner_div)
                if 'brad-' in inner_html and not any(wrapper in inner_html for wrapper in ['sb-story', 'css-', 'innerZoom']):
                    # Limpar HTML
                    cleaned = self._clean_html(str(inner_div))
                    if cleaned:
                        return cleaned
                
                inner_div = inner_div.find('div')
            
            # Se não encontrou, tentar extrair diretamente do elemento
            element_html = str(element)
            if 'brad-' in element_html:
                cleaned = self._clean_html(element_html)
                if cleaned:
                    return cleaned
            
            return ""
        except Exception as e:
            logger.debug(f"Erro ao extrair HTML limpo: {e}")
            return ""
    
    def _clean_code(self, code: str) -> str:
        """Limpa código removendo elementos de syntax highlighting"""
        # Remover tags HTML de syntax highlighting
        code = re.sub(r'<span[^>]*>', '', code)
        code = re.sub(r'</span>', '', code)
        
        # Remover classes CSS de syntax highlighting
        code = re.sub(r'class="[^"]*css-[^"]*"', '', code)
        code = re.sub(r'class="[^"]*prismjs[^"]*"', '', code)
        code = re.sub(r'class="[^"]*token[^"]*"', '', code)
        
        # Remover atributos de syntax highlighting
        code = re.sub(r'style="[^"]*"', '', code)
        code = re.sub(r'data-[^=]*="[^"]*"', '', code)
        
        # Remover texto "Copy" e botões
        code = re.sub(r'Copy.*?button.*?css-[^"]*"', '', code)
        code = re.sub(r'<button[^>]*>Copy</button>', '', code)
        
        # Limpar espaços extras
        code = re.sub(r'\s+', ' ', code)
        code = re.sub(r'>\s+<', '><', code)
        
        # Remover quebras de linha desnecessárias
        code = re.sub(r'\n\s*\n', '\n', code)
        
        return code.strip()
    
    def _clean_html(self, html: str) -> str:
        """Limpa HTML mantendo apenas conteúdo relevante"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remover atributos desnecessários
            for elem in soup.find_all():
                # Manter apenas classes brad-*
                if elem.get('class'):
                    classes = elem.get('class')
                    brad_classes = [cls for cls in classes if cls.startswith('brad-')]
                    if brad_classes:
                        elem['class'] = brad_classes
                    else:
                        del elem['class']
                
                # Remover outros atributos
                for attr in ['id', 'data-name', 'scale', 'style']:
                    if elem.get(attr):
                        del elem[attr]
            
            return str(soup)
        except:
            return html 