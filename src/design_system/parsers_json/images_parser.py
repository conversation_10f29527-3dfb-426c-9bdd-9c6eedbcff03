"""
Images Parser

Parser específico para extrair informações de Images do Storybook.
Images incluem: Animation, Flags, Icons, Illustration e Logos.
"""

import logging
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup, Tag
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

logger = logging.getLogger(__name__)

class ImagesParser:
    """Parser para extrair informações de Images do Storybook"""
    
    def __init__(self):
        self.logger = logger
    
    def parse_images_category(self, driver: WebDriver, url: str, category: str, wait_time: int = 2, timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        Extrai informações estruturadas de uma categoria de images do Storybook
        
        Args:
            driver: Selenium WebDriver
            url: URL da página de images
            category: Categoria (animation, flags, icons, illustration, logos)
            wait_time: Tempo de espera entre ações
            timeout: Timeout para operações
            
        Returns:
            Dicionário com informações estruturadas da categoria de images
        """
        try:
            self.logger.info(f"Extraindo categoria de images: {category}")
            
            # Navegar para a página
            driver.get(url)
            time.sleep(wait_time)
            
            # Aguardar carregamento do iframe
            iframe_selector = "#storybook-preview-iframe"
            WebDriverWait(driver, timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, iframe_selector))
            )
            
            # Aguardar conteúdo
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.sbdocs-content"))
            )
            time.sleep(1)
            
            # Extrair HTML
            raw_html = driver.page_source
            soup = BeautifulSoup(raw_html, 'html.parser')
            
            # Extrair informações estruturadas
            result = {
                "category": category,
                "title": self._extract_title(soup),
                "description": self._extract_description(soup),
                "usage_instructions": self._extract_usage_instructions(soup),
                "code_examples": self._extract_code_examples(soup),
                "size_options": self._extract_size_options(soup),
                "items_table": self._extract_items_table(soup, category),
                "total_items": 0,  # Será calculado depois
                "metadata": {
                    "url": url,
                    "category": category,
                    "extracted_at": time.time()
                }
            }
            
            # Calcular total de items
            result["total_items"] = len(result["items_table"])
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erro ao extrair categoria {category}: {e}")
            return None
        finally:
            try:
                driver.switch_to.default_content()
            except:
                pass
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extrai título da categoria de images"""
        h1 = soup.find('h1', class_='sbdocs-title')
        if h1:
            return h1.get_text(strip=True)
        
        # Buscar em outros elementos de título
        for selector in ['h1', 'h2', '.sbdocs-title']:
            title_elem = soup.select_one(selector)
            if title_elem:
                return title_elem.get_text(strip=True)
        
        return f"Images - {soup.title.get_text(strip=True) if soup.title else 'Unknown'}"
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extrai descrição da categoria de images"""
        # Buscar descrição após o título
        h1 = soup.find('h1')
        if h1:
            next_elem = h1.find_next_sibling(['p', 'div'])
            if next_elem:
                return next_elem.get_text(strip=True)
        
        # Buscar em blockquote
        blockquote = soup.find('blockquote')
        if blockquote:
            return blockquote.get_text(strip=True)
        
        return ""
    
    def _extract_usage_instructions(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai todas as seções com headings e seu conteúdo"""
        sections = []
        
        # Detectar se é Illustration
        title = self._extract_title(soup).lower()
        if 'illustration' in title:
            # Encontrar o <h1>
            h1 = soup.find('h1')
            if h1:
                # Encontrar o primeiro <h2> após o <h1>
                first_h2 = h1.find_next('h2')
                # Buscar todos os <h5> entre <h1> e <h2>
                current = h1
                while current and current != first_h2:
                    if current.name == 'h5':
                        heading_text = current.get_text(strip=True)
                        if len(heading_text.strip()) < 2:
                            current = current.find_next()
                            continue
                        # Pegar parágrafos ou divs logo após o h5
                        content = []
                        next_elem = current.find_next_sibling()
                        while next_elem and next_elem.name not in ['h1', 'h2', 'h3', 'h4', 'h5']:
                            if next_elem.name in ['p', 'div', 'ul', 'ol', 'blockquote']:
                                text_content = next_elem.get_text(strip=True)
                                if text_content:
                                    content.append(text_content)
                            next_elem = next_elem.find_next_sibling()
                        if content:
                            sections.append({
                                "title": heading_text,
                                "content": '\n\n'.join(content)
                            })
                    current = current.find_next()
            return sections
        
        # Para outras categorias, manter o comportamento atual
        headings = soup.find_all(['h2', 'h3', 'h4', 'h5'])
        for heading in headings:
            heading_text = heading.get_text(strip=True)
            if len(heading_text.strip()) < 2:
                continue
            if self._is_items_section_heading(heading_text):
                continue
            content = self._extract_section_content(heading)
            if content:
                sections.append({
                    "title": heading_text,
                    "content": content
                })
        return sections
    
    def _extract_section_content(self, heading: Tag) -> str:
        """Extrai conteúdo de uma seção específica"""
        content = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h1', 'h2', 'h3', 'h4', 'h5']:
            if current.name in ['p', 'ul', 'ol', 'pre', 'blockquote', 'table', 'div']:
                # Para tabelas, extrair de forma mais estruturada
                if current.name == 'table':
                    table_content = self._extract_table_content(current)
                    if table_content:
                        content.append(table_content)
                # Para blocos de código, formatar como markdown
                elif current.name == 'pre':
                    code_element = current.find('code')
                    if code_element:
                        language = self._detect_language(code_element)
                        code = code_element.get_text()
                        content.append(f'```{language}\n{code}\n```')
                    else:
                        code = current.get_text()
                        content.append(f'```\n{code}\n```')
                else:
                    text_content = current.get_text(strip=True)
                    if text_content:
                        # Verificar se estamos chegando nas caixas de exemplos
                        if self._is_examples_section(current):
                            break
                        content.append(text_content)
            current = current.find_next_sibling()
        
        return '\n\n'.join(content)
    
    def _is_examples_section(self, element: Tag) -> bool:
        """Verifica se o elemento indica o início de uma seção de exemplos"""
        # Verificar classes CSS que indicam caixas de exemplos
        classes = element.get('class', [])
        class_text = ' '.join(classes).lower()
        
        # Padrões que indicam seção de exemplos
        example_patterns = [
            'brad-card',
            'brad-illustration',
            'brad-flag',
            'brad-logo',
            'brad-icon',
            'storybook',
            'examples',
            'items'
        ]
        
        for pattern in example_patterns:
            if pattern in class_text:
                return True
        
        # Verificar se o elemento contém muitas divs (indicando grid de exemplos)
        if element.name == 'div':
            child_divs = element.find_all('div', recursive=False)
            if len(child_divs) > 3:  # Se tem muitas divs filhas, provavelmente é grid de exemplos
                return True
        
        return False
    
    def _is_items_section_heading(self, heading_text: str) -> bool:
        """Verifica se o heading é uma seção de itens que deve ser ignorada"""
        heading_lower = heading_text.lower()
        
        # Padrões de headings que são seções de itens
        items_patterns = [
            'stories',
            'list backgrounds',
            'items',
            'examples',
            'components',
            'grid'
        ]
        
        for pattern in items_patterns:
            if pattern in heading_lower:
                return True
        
        return False
    
    def _extract_table_content(self, table: Tag) -> str:
        """Extrai conteúdo de tabela de forma estruturada"""
        rows = table.find_all('tr')
        if not rows:
            return ""
        
        table_content = []
        
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if cells:
                # Extrair texto de cada célula
                cell_texts = [cell.get_text(strip=True) for cell in cells]
                # Juntar células com separador
                row_text = ' | '.join(cell_texts)
                table_content.append(row_text)
        
        return '\n'.join(table_content)
    
    def _extract_code_examples(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai exemplos de código da categoria"""
        examples = []
        
        # Buscar por elementos pre
        pre_elements = soup.find_all('pre')
        
        for pre in pre_elements:
            code_element = pre.find('code')
            if code_element:
                language = self._detect_language(code_element)
                code = code_element.get_text()
                
                # Tentar encontrar título do exemplo
                title = self._find_example_title(pre)
                
                examples.append({
                    "title": title,
                    "language": language,
                    "code": code
                })
        
        return examples
    
    def _find_example_title(self, pre_element: Tag) -> str:
        """Encontra título do exemplo de código"""
        # Procurar por heading anterior
        prev_element = pre_element.find_previous_sibling(['h3', 'h4'])
        if prev_element:
            return prev_element.get_text(strip=True)
        
        return "Exemplo de código"
    
    def _detect_language(self, code_elem: Tag) -> str:
        """Detecta linguagem do código"""
        classes = code_elem.get('class', [])
        for cls in classes:
            if cls.startswith('language-'):
                return cls.replace('language-', '')
        return 'html'
    
    def _extract_size_options(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extrai opções de tamanho se disponíveis"""
        size_options = {}
        
        # Buscar por tabelas de tamanhos
        tables = soup.find_all('table')
        for table in tables:
            # Verificar se é tabela de tamanhos
            headers = table.find_all('th')
            if headers:
                header_text = ' '.join([h.get_text(strip=True) for h in headers]).lower()
                if any(keyword in header_text for keyword in ['size', 'tamanho', 'width', 'height']):
                    size_options = self._parse_size_table(table)
                    break
        
        return size_options
    
    def _parse_size_table(self, table: Tag) -> Dict[str, Any]:
        """Parse tabela de tamanhos"""
        sizes = {}
        
        rows = table.find_all('tr')[1:]  # Pular header
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 2:
                class_name = cells[0].get_text(strip=True)
                value = cells[1].get_text(strip=True)
                sizes[class_name] = value
        
        return sizes
    
    def _extract_items_table(self, soup: BeautifulSoup, category: str) -> List[Dict[str, str]]:
        """Extrai tabela de items da categoria"""
        items = []
        
        # Buscar por containers de items baseado na categoria
        if category == "icons":
            items = self._extract_icons_items(soup)
        elif category == "logos":
            items = self._extract_logos_items(soup)
        elif category == "flags":
            items = self._extract_flags_items(soup)
        elif category == "animation":
            items = self._extract_animation_items(soup)
        elif category == "illustration":
            items = self._extract_illustration_items(soup)
        else:
            # Fallback genérico
            items = self._extract_generic_items(soup, category)
        
        return items
    
    def _extract_icons_items(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai items de ícones"""
        items = []
        
        # Buscar por divs que contenham ícones
        icon_containers = soup.find_all('div', class_=re.compile(r'brad-card'))
        
        for container in icon_containers:
            # Extrair nome do ícone
            name_elem = container.find('p', class_=re.compile(r'brad-font-subtitle'))
            if name_elem:
                icon_name = name_elem.get_text(strip=True)
                
                # Extrair elemento em que contém a classe do ícone
                em_elem = container.find('em')
                if em_elem:
                    icon_class = em_elem.get('class', [])
                    # Filtrar apenas a classe do ícone (que começa com icon-)
                    icon_class = [cls for cls in icon_class if cls.startswith('icon-')]
                    if icon_class:
                        icon_class = icon_class[0]
                        
                        # Gerar código HTML
                        html_code = f'<em class="{icon_class} brad-icon-size-xxs"></em>'
                        
                        items.append({
                            "name": icon_name,
                            "class": icon_class,
                            "html_code": html_code
                        })
        
        return items
    
    def _extract_logos_items(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai items de logos"""
        items = []
        
        # Buscar por divs que contenham logos
        logo_containers = soup.find_all('div', class_=re.compile(r'brad-logo__card'))
        
        for container in logo_containers:
            # Extrair nome do logo
            name_elem = container.find('p', class_=re.compile(r'brad-font-subtitle'))
            if name_elem:
                logo_name = name_elem.get_text(strip=True)
                
                # Extrair elemento img que contém a classe do logo
                img_elem = container.find('img')
                if img_elem:
                    logo_class = img_elem.get('class', [])
                    # Filtrar apenas a classe do logo (que começa com brad-logo__)
                    logo_class = [cls for cls in logo_class if cls.startswith('brad-logo__')]
                    if logo_class:
                        logo_class = logo_class[0]
                        
                        # Gerar código HTML
                        html_code = f'<img class="brad-logo brad-size-sm {logo_class}">'
                        
                        items.append({
                            "name": logo_name,
                            "class": logo_class,
                            "html_code": html_code
                        })
        
        return items
    
    def _extract_flags_items(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai items de flags"""
        items = []
        
        # Buscar por divs que contenham flags - usar dois hífens (brad-flag--card)
        flag_containers = soup.find_all('div', class_=re.compile(r'brad-flag--card'))
        
        for container in flag_containers:
            # Extrair nome da flag
            name_elem = container.find('p', class_=re.compile(r'brad-font-subtitle'))
            if name_elem:
                flag_name = name_elem.get_text(strip=True)
                
                # Extrair elemento img que contém a classe da flag
                img_elem = container.find('img')
                if img_elem:
                    flag_classes = img_elem.get('class', [])
                    # Filtrar apenas a classe da flag (que começa com brad-flag-)
                    flag_class = [cls for cls in flag_classes if cls.startswith('brad-flag-') and cls != 'brad-flag']
                    if flag_class:
                        flag_class = flag_class[0]
                        
                        # Gerar código HTML
                        html_code = f'<img class="brad-flag {flag_class}">'
                        
                        items.append({
                            "name": flag_name,
                            "class": flag_class,
                            "html_code": html_code
                        })
        
        return items
    
    def _extract_animation_items(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai items de animações e tabelas de documentação."""
        items = []
        
        # Buscar por divs que contenham animações - procurar por divs com id que começa com brad-animation
        animation_containers = soup.find_all('div', id=re.compile(r'brad-animation'))
        
        for container in animation_containers:
            # Extrair nome da animação do id
            animation_id = container.get('id', '')
            if animation_id.startswith('brad-animation-'):
                animation_name = animation_id.replace('brad-animation-', '')
                
                # Gerar código HTML usando o id
                html_code = f'<div id="{animation_id}"></div>'
                
                items.append({
                    "name": animation_name,
                    "class": animation_id,
                    "html_code": html_code
                })
        
        # Extrair tabelas de documentação
        tables = soup.find_all('table')
        for table in tables:
            # Verificar se é uma tabela de documentação (tem cabeçalho)
            headers = table.find_all('th')
            if headers:
                # Extrair dados da tabela
                table_data = self._extract_table_data(table)
                if table_data:
                    items.append(table_data)
        
        return items
    
    def _extract_table_data(self, table: Tag) -> Optional[Dict[str, str]]:
        """Extrai dados de uma tabela de documentação."""
        headers = table.find_all('th')
        rows = table.find_all('tr')
        
        if not headers or len(rows) < 2:  # Precisa de cabeçalho e pelo menos uma linha de dados
            return None
        
        # Determinar tipo de tabela baseado no primeiro cabeçalho
        first_header = headers[0].get_text(strip=True).lower()
        
        if 'nome' in first_header or 'name' in first_header:
            # Tabela de Options/Propriedades
            return self._extract_options_table(table, headers, rows)
        elif 'método' in first_header or 'method' in first_header:
            # Tabela de Métodos
            return self._extract_methods_table(table, headers, rows)
        else:
            # Tabela genérica
            return self._extract_generic_table(table, headers, rows)
    
    def _extract_options_table(self, table: Tag, headers: List[Tag], rows: List[Tag]) -> Dict[str, str]:
        """Extrai tabela de Options/Propriedades."""
        header_texts = [h.get_text(strip=True) for h in headers]
        
        # Criar markdown da tabela
        table_md = "| " + " | ".join(header_texts) + " |\n"
        table_md += "| " + " | ".join(["---"] * len(headers)) + " |\n"
        
        # Adicionar linhas de dados (pular primeira linha se for cabeçalho)
        for row in rows[1:]:  # Pular primeira linha se for cabeçalho
            cells = row.find_all(['td', 'th'])
            if cells:
                cell_texts = [cell.get_text(strip=True) for cell in cells]
                table_md += "| " + " | ".join(cell_texts) + " |\n"
        
        return {
            "name": "Options/Propriedades",
            "class": "documentation-table",
            "html_code": table_md.strip(),
            "type": "options_table"
        }
    
    def _extract_methods_table(self, table: Tag, headers: List[Tag], rows: List[Tag]) -> Dict[str, str]:
        """Extrai tabela de Métodos."""
        header_texts = [h.get_text(strip=True) for h in headers]
        
        # Criar markdown da tabela
        table_md = "| " + " | ".join(header_texts) + " |\n"
        table_md += "| " + " | ".join(["---"] * len(headers)) + " |\n"
        
        # Adicionar linhas de dados (pular primeira linha se for cabeçalho)
        for row in rows[1:]:  # Pular primeira linha se for cabeçalho
            cells = row.find_all(['td', 'th'])
            if cells:
                cell_texts = [cell.get_text(strip=True) for cell in cells]
                table_md += "| " + " | ".join(cell_texts) + " |\n"
        
        return {
            "name": "Métodos",
            "class": "documentation-table",
            "html_code": table_md.strip(),
            "type": "methods_table"
        }
    
    def _extract_generic_table(self, table: Tag, headers: List[Tag], rows: List[Tag]) -> Dict[str, str]:
        """Extrai tabela genérica."""
        header_texts = [h.get_text(strip=True) for h in headers]
        
        # Criar markdown da tabela
        table_md = "| " + " | ".join(header_texts) + " |\n"
        table_md += "| " + " | ".join(["---"] * len(headers)) + " |\n"
        
        # Adicionar linhas de dados (pular primeira linha se for cabeçalho)
        for row in rows[1:]:  # Pular primeira linha se for cabeçalho
            cells = row.find_all(['td', 'th'])
            if cells:
                cell_texts = [cell.get_text(strip=True) for cell in cells]
                table_md += "| " + " | ".join(cell_texts) + " |\n"
        
        return {
            "name": "Tabela de Documentação",
            "class": "documentation-table",
            "html_code": table_md.strip(),
            "type": "generic_table"
        }
    
    def _extract_illustration_items(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extrai items de ilustrações"""
        items = []
        
        # Buscar por divs que contenham ilustrações - usar brad-ilustration--card (com dois hífens)
        illustration_containers = soup.find_all('div', class_=re.compile(r'brad-ilustration--card'))
        
        for container in illustration_containers:
            # Extrair nome da ilustração
            name_elem = container.find('p', class_=re.compile(r'brad-font-subtitle'))
            if name_elem:
                illustration_name = name_elem.get_text(strip=True)
                
                # Extrair elemento img que contém a classe da ilustração
                img_elem = container.find('img', class_=re.compile(r'brad-illustration__content'))
                if img_elem:
                    ill_classes = img_elem.get('class', [])
                    # Filtrar apenas a classe da ilustração
                    ill_class = [cls for cls in ill_classes if cls.startswith('brad-illustration__content--')]
                    if ill_class:
                        ill_class = ill_class[0]
                        
                        # Gerar código HTML
                        html_code = f'<img class="brad-illustration__content {ill_class}">'
                        
                        items.append({
                            "name": illustration_name,
                            "class": ill_class,
                            "html_code": html_code
                        })
        
        return items
    
    def _extract_generic_items(self, soup: BeautifulSoup, category: str) -> List[Dict[str, str]]:
        """Extrai items genéricos para categorias não específicas"""
        items = []
        
        # Buscar por elementos que possam conter items
        containers = soup.find_all('div', class_=re.compile(r'card|item'))
        
        for container in containers:
            # Tentar extrair nome e classe
            name_elem = container.find(['p', 'span', 'div'])
            if name_elem:
                name = name_elem.get_text(strip=True)
                
                # Buscar por elemento que contenha a classe
                class_elem = container.find(['em', 'img', 'div'])
                if class_elem:
                    classes = class_elem.get('class', [])
                    if classes:
                        main_class = classes[0]
                        html_code = f'<div class="{main_class}"></div>'
                        
                        items.append({
                            "name": name,
                            "class": main_class,
                            "html_code": html_code
                        })
        
        return items 