# -*- coding: utf-8 -*-
"""
Parser para documentação principal do Storybook.
Extrai informações estruturadas da documentação principal do Design System.
"""

import logging
from typing import Dict, Any, List
from bs4 import BeautifulSoup, Tag

logger = logging.getLogger(__name__)

class MainDocsParser:
    """Parser específico para documentação principal do Storybook."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_main_docs(self, html_content: str, url: str = "") -> Dict[str, Any]:
        """
        Extrai informações estruturadas da documentação principal.
        
        Args:
            html_content: Conteúdo HTML da página
            url: URL da página
            
        Returns:
            Dicionário com informações estruturadas da documentação
        """
        self.logger.info("Parsing documentação principal...")
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Encontrar o conteúdo principal
        content_div = soup.find('div', class_='sbdocs-content')
        if not content_div:
            self.logger.warning("Conteúdo principal não encontrado")
            return self._create_empty_docs_data(url)
        
        # Extrair título
        title_element = content_div.find('h1', class_='sbdocs-title')
        title = title_element.get_text(strip=True) if title_element else "Design System Documentation"
        
        # Extrair descrição (primeiro parágrafo ou blockquote)
        description = self._extract_description(content_div)
        
        # Extrair seções principais
        sections = self._extract_sections(content_div)
        
        # Extrair exemplos de código
        code_examples = self._extract_code_examples(content_div)
        
        # Extrair links importantes
        important_links = self._extract_important_links(content_div)
        
        # Criar dados estruturados
        docs_data = {
            "title": title,
            "description": description,
            "sections": sections,
            "code_examples": code_examples,
            "important_links": important_links,
            "raw_html": str(content_div),
            "content_text": content_div.get_text(separator='\n', strip=True),
            "url": url,
            "type": "main_documentation"
        }
        
        self.logger.info(f"Documentação principal extraída: {title}")
        return docs_data
    
    def _create_empty_docs_data(self, url: str) -> Dict[str, Any]:
        """Cria dados vazios para documentação."""
        return {
            "title": "Design System Documentation",
            "description": "",
            "sections": [],
            "code_examples": [],
            "important_links": [],
            "raw_html": "",
            "content_text": "",
            "url": url,
            "type": "main_documentation"
        }
    
    def _extract_description(self, content_div: Tag) -> str:
        """Extrai descrição da documentação."""
        # Procurar por blockquote primeiro
        blockquote = content_div.find('blockquote')
        if blockquote:
            return blockquote.get_text(strip=True)
        
        # Procurar por primeiro parágrafo
        first_p = content_div.find('p')
        if first_p:
            return first_p.get_text(strip=True)
        
        return ""
    
    def _extract_sections(self, content_div: Tag) -> Dict[str, str]:
        """Extrai seções principais da documentação."""
        sections = {}
        
        # Procurar por todos os h2 e h3
        headings = content_div.find_all(['h2', 'h3'])
        
        for heading in headings:
            heading_text = heading.get_text(strip=True)
            if heading_text:
                # Extrair conteúdo da seção (até o próximo heading)
                section_content = self._extract_section_content(heading)
                sections[heading_text] = section_content
        
        return sections
    
    def _extract_section_content(self, heading: Tag) -> str:
        """Extrai conteúdo de uma seção específica."""
        content = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h1', 'h2', 'h3', 'h4']:
            if current.name in ['p', 'ul', 'ol', 'pre', 'blockquote']:
                content.append(current.get_text(strip=True))
            current = current.find_next_sibling()
        
        return '\n'.join(content)
    
    def _extract_code_examples(self, content_div: Tag) -> List[Dict[str, str]]:
        """Extrai exemplos de código da documentação."""
        examples = []
        
        # Procurar por elementos pre
        pre_elements = content_div.find_all('pre')
        
        for pre in pre_elements:
            code_element = pre.find('code')
            if code_element:
                language = code_element.get('class', [''])[0] if code_element.get('class') else 'html'
                code = code_element.get_text()
                
                # Tentar encontrar título do exemplo
                title = self._find_example_title(pre)
                
                examples.append({
                    "title": title,
                    "language": language,
                    "code": code
                })
        
        return examples
    
    def _find_example_title(self, pre_element: Tag) -> str:
        """Encontra título do exemplo de código."""
        # Procurar por heading anterior
        prev_element = pre_element.find_previous_sibling(['h3', 'h4'])
        if prev_element:
            return prev_element.get_text(strip=True)
        
        return "Exemplo de código"
    
    def _extract_important_links(self, content_div: Tag) -> List[Dict[str, str]]:
        """Extrai links importantes da documentação."""
        links = []
        
        # Procurar por links
        a_elements = content_div.find_all('a', href=True)
        
        for a in a_elements:
            href = a.get('href')
            text = a.get_text(strip=True)
            
            if href and text:
                links.append({
                    "text": text,
                    "url": href
                })
        
        return links
    
    def to_markdown(self, docs_data: Dict[str, Any]) -> str:
        """
        Converte dados da documentação para Markdown.
        
        Args:
            docs_data: Dados da documentação
            
        Returns:
            Conteúdo em Markdown
        """
        title = docs_data.get("title", "Design System Documentation")
        description = docs_data.get("description", "")
        sections = docs_data.get("sections", {})
        code_examples = docs_data.get("code_examples", [])
        important_links = docs_data.get("important_links", [])
        url = docs_data.get("url", "")
        
        lines = [
            f"# {title}",
            "",
            f"**URL Original:** {url}",
            ""
        ]
        
        if description:
            lines.extend([
                "## Descrição",
                "",
                description,
                ""
            ])
        
        if sections:
            lines.append("## Seções Principais")
            lines.append("")
            for section_title, section_content in sections.items():
                lines.extend([
                    f"### {section_title}",
                    "",
                    section_content,
                    ""
                ])
        
        if code_examples:
            lines.append("## Exemplos de Código")
            lines.append("")
            for example in code_examples:
                lines.extend([
                    f"### {example['title']}",
                    "",
                    f"```{example['language']}",
                    example['code'],
                    "```",
                    ""
                ])
        
        if important_links:
            lines.append("## Links Importantes")
            lines.append("")
            for link in important_links:
                lines.append(f"- [{link['text']}]({link['url']})")
            lines.append("")
        
        lines.extend([
            "---",
            f"*Extraído em: {docs_data.get('extracted_at', '')}*"
        ])
        
        return "\n".join(lines) 