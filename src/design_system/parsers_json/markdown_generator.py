"""
Markdown Generator para Storybook
Gera arquivos Markdown estruturados a partir dos dados do Storybook
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class MarkdownGenerator:
    """
    Gerador de arquivos Markdown estruturados para componentes do Storybook.
    
    Responsável por:
    1. Converter componentes para formato Markdown
    2. Organizar em pastas por categoria
    3. Gerar índice JSON estruturado
    4. Manter backup JSON opcional
    """
    
    def __init__(self, output_dir: Path, backup_json: bool = True, storybook_url: str = ""):
        """
        Inicializa o gerador de Markdown.
        
        Args:
            output_dir: Diretório de saída
            backup_json: Se deve manter backup JSON
            storybook_url: URL do Storybook para referência
        """
        self.output_dir = Path(output_dir)
        self.backup_json = backup_json
        self.storybook_url = storybook_url
        
        # Criar estrutura de diretórios
        self._create_directory_structure()
        
        logger.info("Markdown Generator inicializado")
    
    def _create_directory_structure(self):
        """Cria a estrutura de diretórios para organização."""
        directories = [
            "components",
            "templates", 
            "services",
            "classes",
            "images",
            "json_backup"
        ]
        
        for directory in directories:
            (self.output_dir / directory).mkdir(parents=True, exist_ok=True)
    
    def save_categorized_components(self, components: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Salva componentes categorizados em formato Markdown + JSON Index.
        
        Args:
            components: Dicionário de componentes extraídos
            
        Returns:
            Relatório da operação
        """
        logger.info("Iniciando geração de arquivos Markdown...")
        
        # Categorizar componentes
        categorized = self._categorize_components(components)
        
        # Gerar arquivos Markdown
        markdown_files = self._generate_markdown_files(categorized)
        
        # Gerar índice JSON
        index_data = self._generate_structured_index(categorized, markdown_files)
        
        # Salvar índice
        index_file = self.output_dir / "storybook_index.json"
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, indent=2, ensure_ascii=False)
        
        # Gerar documentação principal
        main_docs = self._generate_main_documentation(categorized, getattr(self, 'storybook_url', ''))
        main_docs_file = self.output_dir / "main_documentation.md"
        with open(main_docs_file, 'w', encoding='utf-8') as f:
            f.write(main_docs)
        
        # Backup JSON se solicitado
        if self.backup_json:
            self._save_json_backup(categorized)
        
        report = {
            "total_components": len(components),
            "markdown_files_generated": len(markdown_files),
            "categories": list(categorized.keys()),
            "index_file": str(index_file),
            "main_docs_file": str(main_docs_file)
        }
        
        logger.info(f"Geração concluída: {len(markdown_files)} arquivos Markdown criados")
        return report
    
    def _categorize_components(self, components: Dict[str, Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Categoriza componentes por tipo.
        
        Args:
            components: Componentes extraídos
            
        Returns:
            Componentes organizados por categoria
        """
        categorized = {
            "components": [],
            "templates": [],
            "services": [],
            "classes": [],
            "images": []
        }
        
        for comp_id, comp_data in components.items():
            # Pular documentação principal (será tratada separadamente)
            if comp_data.get("type") == "main_documentation" or comp_data.get("component_type") == "main_docs":
                continue
                
            category = self._determine_category(comp_id, comp_data)
            
            if category in categorized:
                categorized[category].append({
                    "id": comp_id,
                    "data": comp_data
                })
            else:
                # Fallback para componentes
                categorized["components"].append({
                    "id": comp_id,
                    "data": comp_data
                })
        
        return categorized
    
    def _determine_category(self, comp_id: str, comp_data: Dict[str, Any]) -> str:
        """
        Determina a categoria do componente baseado no ID e dados.
        
        Args:
            comp_id: ID do componente
            comp_data: Dados do componente
            
        Returns:
            Categoria do componente
        """
        comp_id_lower = comp_id.lower()
        description = comp_data.get("description", "").lower()
        name = comp_data.get("name", "").lower()
        
        # Priorizar componentes que começam com "designsystem-components"
        if comp_id_lower.startswith("designsystem-components"):
            return "components"
        
        # Verificar se é um componente "no-preview" ou erro
        if "no preview" in description or "no stories" in description or "sorry" in description:
            # Tentar determinar categoria baseada no contexto
            if any(keyword in comp_id_lower for keyword in ["icon", "logo", "flag", "animation", "illustration"]):
                return "images"
            elif any(keyword in comp_id_lower for keyword in ["template", "layout"]):
                return "templates"
            elif any(keyword in comp_id_lower for keyword in ["service", "utility", "toggle", "scroll"]):
                return "services"
            elif any(keyword in comp_id_lower for keyword in ["class", "color", "border", "spacing", "typography"]):
                return "classes"
            else:
                return "components"
        
        # Mapeamento baseado em padrões do ID
        if any(keyword in comp_id_lower for keyword in ["template", "layout"]):
            return "templates"
        elif any(keyword in comp_id_lower for keyword in ["service", "utility", "toggle", "scroll"]):
            return "services"
        elif any(keyword in comp_id_lower for keyword in ["class", "color", "border", "spacing", "typography"]):
            return "classes"
        elif any(keyword in comp_id_lower for keyword in ["icon", "logo", "flag", "animation", "illustration"]):
            return "images"
        else:
            return "components"
    
    def _generate_markdown_files(self, categorized: Dict[str, List[Dict[str, Any]]]) -> Dict[str, str]:
        """
        Gera arquivos Markdown para cada componente.
        
        Args:
            categorized: Componentes categorizados
            
        Returns:
            Mapeamento de ID para arquivo gerado
        """
        markdown_files = {}
        
        for category, components in categorized.items():
            category_dir = self.output_dir / category
            
            # Tratamento especial para imagens
            if category == "images" and components:
                # Criar um arquivo por categoria de images (icons, logos, etc.)
                images_by_type = self._group_images_by_type(components)
                
                for image_type, image_data in images_by_type.items():
                    markdown_content = self._images_to_markdown(image_data)
                    
                    # Nome do arquivo
                    filename = f"{image_type}.md"
                    file_path = category_dir / filename
                    
                    # Salvar arquivo
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(markdown_content)
                    
                    markdown_files[f"images_{image_type}"] = str(file_path)
                    logger.debug(f"Gerado: {file_path}")
            else:
                # Gerar arquivos normais para outras categorias
                for comp_info in components:
                    comp_id = comp_info["id"]
                    comp_data = comp_info["data"]
                    
                    # Gerar conteúdo Markdown normal
                    markdown_content = self._component_to_markdown(comp_data)
                    
                    # Nome do arquivo
                    filename = self._generate_filename(comp_id, comp_data)
                    file_path = category_dir / f"{filename}.md"
                    
                    # Salvar arquivo
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(markdown_content)
                    
                    markdown_files[comp_id] = str(file_path)
                    logger.debug(f"Gerado: {file_path}")
        
        return markdown_files
    
    def _group_images_by_type(self, components: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Agrupa componentes de images por tipo (icons, logos, etc.)"""
        images_by_type = {}
        
        for comp_info in components:
            comp_data = comp_info["data"]
            
            # Verificar se é um componente de images
            if comp_data.get("category") in ["icons", "logos", "flags", "animation", "illustration"]:
                image_type = comp_data.get("category")
                if image_type:
                    images_by_type[image_type] = comp_data
        
        return images_by_type
    
    def _images_to_markdown(self, image_data: Dict[str, Any]) -> str:
        """
        Converte dados de images para formato Markdown.
        
        Args:
            image_data: Dados da categoria de images
            
        Returns:
            Conteúdo Markdown formatado
        """
        title = image_data.get("title", "Images")
        description = image_data.get("description", "")
        category = image_data.get("category", "images")
        usage_instructions = image_data.get("usage_instructions", [])
        code_examples = image_data.get("code_examples", [])
        size_options = image_data.get("size_options", {})
        items_table = image_data.get("items_table", [])
        total_items = image_data.get("total_items", 0)
        
        lines = [
            f"# {title}",
            "",
            f"**Categoria:** {category.capitalize()}",
            f"**Total de itens:** {total_items}",
            "",
        ]
        
        if description:
            lines.extend([
                "## Descrição",
                "",
                description,
                ""
            ])
        
        if usage_instructions:
            lines.append("## Instruções de Uso")
            lines.append("")
            for instruction in usage_instructions:
                if isinstance(instruction, dict):
                    lines.extend([
                        f"### {instruction.get('title', 'Instrução')}",
                        "",
                        instruction.get('content', ''),
                        ""
                    ])
                else:
                    lines.append(f"- {instruction}")
                    lines.append("")
        
        if code_examples:
            lines.append("## Exemplos de Código")
            lines.append("")
            for example in code_examples:
                lines.extend([
                    f"### {example.get('title', 'Exemplo')}",
                    "",
                    f"```{example.get('language', 'html')}",
                    example.get('code', ''),
                    "```",
                    ""
                ])
        
        if size_options:
            lines.append("## Opções de Tamanho")
            lines.append("")
            lines.append("| Classe | Valor |")
            lines.append("|--------|-------|")
            for class_name, value in size_options.items():
                lines.append(f"| `{class_name}` | {value} |")
            lines.append("")
        
        if items_table:
            lines.append("## Itens Disponíveis")
            lines.append("")
            lines.append("| Nome | Classe | Código HTML |")
            lines.append("|------|--------|-------------|")
            
            for item in items_table:
                name = item.get("name", "")
                class_name = item.get("class", "")
                html_code = item.get("html_code", "")
                
                # Escapar pipes no markdown
                name = name.replace("|", "\\|")
                class_name = class_name.replace("|", "\\|")
                html_code = html_code.replace("|", "\\|")
                
                lines.append(f"| {name} | `{class_name}` | `{html_code}` |")
            
            lines.append("")
        
        return "\n".join(lines)
    
    def _generate_filename(self, comp_id: str, comp_data: Dict[str, Any]) -> str:
        """
        Gera nome de arquivo para o componente.
        
        Args:
            comp_id: ID do componente
            comp_data: Dados do componente
            
        Returns:
            Nome do arquivo
        """
        # Extrair nome do componente
        name = comp_data.get("name", comp_data.get("title", comp_id))
        
        # Limpar nome para uso em arquivo
        filename = name.lower().replace(" ", "-").replace("_", "-")
        filename = "".join(c for c in filename if c.isalnum() or c == "-")
        
        # Remover duplicatas de hífens
        while "--" in filename:
            filename = filename.replace("--", "-")
        
        # Remover hífens no início e fim
        filename = filename.strip("-")
        
        return filename or "component"
    
    def _component_to_markdown(self, comp_data: Dict[str, Any]) -> str:
        """
        Converte componente para formato Markdown.
        
        Args:
            comp_data: Dados do componente
            
        Returns:
            Conteúdo Markdown formatado
        """
        name = comp_data.get("name", comp_data.get("component_name", comp_data.get("title", "Component")))
        description = comp_data.get("description", "")
        category = comp_data.get("category", "Component")
        
        # Determinar tipo do componente
        component_type = self._determine_component_type(comp_data)
        
        # Extrair propriedades
        properties = comp_data.get("properties", {})
        props_table = self._generate_properties_table(properties)
        
        # Extrair exemplos
        examples = comp_data.get("examples", [])
        examples_section = self._generate_examples_section(examples)
        
        # Extrair classes CSS
        css_classes = comp_data.get("css_classes", [])
        css_section = self._generate_css_classes_section(css_classes)
        
        # Extrair código de uso
        html_usage = comp_data.get("html_usage", "")
        js_usage = comp_data.get("javascript_usage", "")
        usage_section = self._generate_usage_section(html_usage, js_usage)
        
        # Gerar Markdown
        markdown = f"""# {name}

**Tipo:** {component_type}  
**Categoria:** {category}  
**Descrição:** {description}

## Propriedades

{props_table}

## Uso

{usage_section}

## Classes CSS Relacionadas

{css_section}

## Exemplos

{examples_section}

---
*Gerado automaticamente em {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
        
        return markdown
    
    def _image_item_to_markdown(self, item: Dict[str, Any], category_data: Dict[str, Any]) -> str:
        """
        Converte item de imagem para formato Markdown.
        
        Args:
            item: Dados do item de imagem
            category_data: Dados da categoria pai
            
        Returns:
            Conteúdo Markdown formatado
        """
        name = item.get("name", "Unknown Image")
        description = item.get("description", "")
        category = item.get("category", "image")
        
        # Determinar tipo do item
        item_type = self._determine_image_type(item)
        
        # Extrair classes CSS
        css_classes = item.get("css_classes", [])
        css_section = self._generate_css_classes_section(css_classes)
        
        # Extrair código de uso
        code = item.get("code", "")
        usage_section = self._generate_image_usage_section(code, category_data)
        
        # Extrair dados da imagem
        image_data = item.get("image", {})
        image_section = self._generate_image_section(image_data)
        
        # Gerar Markdown
        markdown = f"""# {name}

**Tipo:** {item_type}  
**Categoria:** {category}  
**Descrição:** {description}

## Uso

{usage_section}

## Classes CSS Relacionadas

{css_section}

{image_section}

---
*Gerado automaticamente em {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
        
        return markdown
    
    def _determine_image_type(self, item: Dict[str, Any]) -> str:
        """
        Determina o tipo do item de imagem.
        
        Args:
            item: Dados do item
            
        Returns:
            Tipo do item
        """
        name = item.get("name", "").lower()
        category = item.get("category", "").lower()
        
        if "icon" in name or "icon" in category:
            return "Icon"
        elif "flag" in name or "flag" in category:
            return "Flag"
        elif "logo" in name or "logo" in category:
            return "Logo"
        elif "animation" in name or "animation" in category:
            return "Animation"
        elif "illustration" in name or "illustration" in category:
            return "Illustration"
        else:
            return "Image Asset"
    
    def _generate_image_usage_section(self, code: str, category_data: Dict[str, Any]) -> str:
        """
        Gera seção de uso para imagens.
        
        Args:
            code: Código de uso
            category_data: Dados da categoria
            
        Returns:
            Seção de uso formatada
        """
        usage = ""
        
        if code:
            usage += f"""### HTML
```html
{code}
```

"""
        
        # Adicionar diretrizes de uso da categoria
        guidelines = category_data.get("usage_guidelines", [])
        if guidelines:
            usage += "### Diretrizes de Uso\n\n"
            for guideline in guidelines:
                usage += f"- {guideline}\n"
            usage += "\n"
        
        if not usage:
            usage = "Exemplos de uso não disponíveis."
        
        return usage
    
    def _generate_image_section(self, image_data: Dict[str, Any]) -> str:
        """
        Gera seção de informações da imagem.
        
        Args:
            image_data: Dados da imagem
            
        Returns:
            Seção de imagem formatada
        """
        if not image_data:
            return ""
        
        image_type = image_data.get("type", "")
        src = image_data.get("src", "")
        alt = image_data.get("alt", "")
        width = image_data.get("width", "")
        height = image_data.get("height", "")
        
        section = "## Informações da Imagem\n\n"
        
        if image_type:
            section += f"**Tipo:** {image_type}\n"
        if src:
            section += f"**Src:** {src}\n"
        if alt:
            section += f"**Alt:** {alt}\n"
        if width:
            section += f"**Largura:** {width}\n"
        if height:
            section += f"**Altura:** {height}\n"
        
        return section
    
    def _determine_component_type(self, comp_data: Dict[str, Any]) -> str:
        """
        Determina o tipo do componente.
        
        Args:
            comp_data: Dados do componente
            
        Returns:
            Tipo do componente
        """
        comp_id = comp_data.get("id", "").lower()
        
        if "webcomponent" in comp_id:
            return "WebComponent"
        elif "html" in comp_id:
            return "HTML"
        elif "service" in comp_id:
            return "Service"
        elif "template" in comp_id:
            return "Template"
        elif "class" in comp_id:
            return "CSS Class"
        elif "icon" in comp_id or "logo" in comp_id:
            return "Image Asset"
        else:
            return "Component"
    
    def _generate_properties_table(self, properties: Any) -> str:
        """
        Gera tabela de propriedades em Markdown.
        
        Args:
            properties: Propriedades do componente
            
        Returns:
            Tabela Markdown formatada
        """
        if not properties:
            return "Nenhuma propriedade documentada."
        
        # Se properties é uma lista, converter para dicionário
        if isinstance(properties, list):
            properties_dict = {}
            for i, prop in enumerate(properties):
                if isinstance(prop, dict):
                    prop_name = prop.get("name", f"prop_{i}")
                    properties_dict[prop_name] = prop
                else:
                    properties_dict[f"prop_{i}"] = prop
            properties = properties_dict
        
        # Se ainda não é um dicionário, retornar mensagem
        if not isinstance(properties, dict):
            return f"Propriedades em formato não suportado: {type(properties).__name__}"
        
        table = """| Nome | Tipo | Obrigatório | Descrição |
|------|------|-------------|-----------|
"""
        
        for prop_name, prop_info in properties.items():
            if isinstance(prop_info, dict):
                prop_type = prop_info.get("type", "string")
                required = "Sim" if prop_info.get("required", False) else "Não"
                description = prop_info.get("description", "")
            else:
                prop_type = "string"
                required = "Não"
                description = str(prop_info)
            
            table += f"| {prop_name} | {prop_type} | {required} | {description} |\n"
        
        return table
    
    def _generate_usage_section(self, html_usage: str, js_usage: str) -> str:
        """
        Gera seção de uso com exemplos de código.
        
        Args:
            html_usage: Exemplo HTML
            js_usage: Exemplo JavaScript
            
        Returns:
            Seção de uso formatada
        """
        usage = ""
        
        if html_usage:
            usage += f"""### HTML
```html
{html_usage}
```

"""
        
        if js_usage:
            usage += f"""### JavaScript
```javascript
{js_usage}
```

"""
        
        if not usage:
            usage = "Exemplos de uso não disponíveis."
        
        return usage
    
    def _generate_css_classes_section(self, css_classes: List[str]) -> str:
        """
        Gera seção de classes CSS.
        
        Args:
            css_classes: Lista de classes CSS
            
        Returns:
            Seção de classes CSS formatada
        """
        if not css_classes:
            return "Nenhuma classe CSS documentada."
        
        classes_list = "\n".join([f"- `{cls}`" for cls in css_classes])
        return classes_list
    
    def _generate_examples_section(self, examples: List[Dict[str, str]]) -> str:
        """
        Gera seção de exemplos.
        
        Args:
            examples: Lista de exemplos
            
        Returns:
            Seção de exemplos formatada
        """
        if not examples:
            return "Nenhum exemplo disponível."
        
        examples_text = ""
        
        for i, example in enumerate(examples, 1):
            title = example.get("title", f"Exemplo {i}")
            description = example.get("description", "")
            code = example.get("code", "")
            
            examples_text += f"""### {title}
{description}

```html
{code}
```

"""
        
        return examples_text
    
    def _generate_structured_index(self, categorized: Dict[str, List[Dict[str, Any]]], 
                       markdown_files: Dict[str, str]) -> Dict[str, Any]:
        """
        Gera índice JSON estruturado.
        
        Args:
            categorized: Componentes categorizados
            markdown_files: Mapeamento de arquivos Markdown
            
        Returns:
            Dados do índice
        """
        # Contar componentes por categoria
        category_counts = {cat: len(comps) for cat, comps in categorized.items()}
        
        # Gerar detalhes dos componentes
        component_details = {}
        components_by_category = {}
        
        for category, components in categorized.items():
            components_by_category[category] = []
            
            for comp_info in components:
                comp_id = comp_info["id"]
                comp_data = comp_info["data"]
                
                # Gerar nome do arquivo
                filename = self._generate_filename(comp_id, comp_data)
                
                # Adicionar à lista da categoria
                components_by_category[category].append({
                    "id": comp_id,
                    "name": comp_data.get("name", comp_data.get("title", comp_id)),
                    "filename": filename,
                    "file": markdown_files.get(comp_id, ""),
                    "description": comp_data.get("description", "")
                })
                
                # Detalhes completos
                component_details[comp_id] = {
                    "name": comp_data.get("name", comp_data.get("title", comp_id)),
                    "filename": filename,
                    "category": category,
                    "file": markdown_files.get(comp_id, ""),
                    "description": comp_data.get("description", ""),
                    "type": self._determine_component_type(comp_data),
                    "properties_count": len(comp_data.get("properties", {})),
                    "examples_count": len(comp_data.get("examples", []))
                }
        
        index_data = {
            "metadata": {
                "total_components": sum(category_counts.values()),
                "categories": category_counts,
                "generated_at": datetime.now().isoformat()
            },
            "components_by_category": components_by_category,
            "component_details": component_details
        }
        
        return index_data
    
    def _generate_main_documentation(self, categorized: Dict[str, List[Dict[str, Any]]], storybook_url: str = "") -> str:
        """
        Gera documentação principal do Design System.
        
        Args:
            categorized: Componentes categorizados
            storybook_url: URL do Storybook
            
        Returns:
            Documentação principal em Markdown
        """
        total_components = sum(len(comps) for comps in categorized.values())
        
        docs = f"""# Design System Documentation

## 📚 Documentação Original

**Storybook URL:** {storybook_url}

## 📊 Resumo da Extração

Este Design System contém **{total_components} componentes** organizados nas seguintes categorias:

"""
        
        for category, components in categorized.items():
            if components:
                docs += f"""### {category.capitalize()} ({len(components)} componentes)

"""
                for comp_info in components:
                    comp_data = comp_info["data"]
                    name = comp_data.get("name", comp_data.get("title", comp_info["id"]))
                    description = comp_data.get("description", "")
                    
                    docs += f"- **{name}**: {description}\n"
                
                docs += "\n"
        
        docs += f"""
## 📁 Estrutura de Arquivos

```
{self.output_dir.name}/
├── storybook_index.json          # Índice principal estruturado
├── main_documentation.md         # Esta documentação
├── components/                   # Componentes principais
├── templates/                    # Templates e layouts
├── services/                     # Serviços e utilitários
├── classes/                      # Classes CSS utilitárias
├── images/                       # Ícones, logos e imagens
└── json_backup/                  # Backup em JSON (opcional)
```

## 🎯 Como Usar

1. **Buscar Componente**: Consulte o `storybook_index.json` para encontrar componentes
2. **Ler Documentação**: Acesse o arquivo Markdown específico do componente
3. **Implementar**: Use os exemplos de código fornecidos

---
*Documentação gerada automaticamente em {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
        
        return docs
    
    def _save_json_backup(self, categorized: Dict[str, List[Dict[str, Any]]]):
        """
        Salva backup dos dados em JSON.
        
        Args:
            categorized: Componentes categorizados
        """
        backup_dir = self.output_dir / "json_backup"
        
        for category, components in categorized.items():
            if components:
                # Converter para formato JSON original
                json_data = {}
                for comp_info in components:
                    comp_id = comp_info["id"]
                    json_data[comp_id] = comp_info["data"]
                
                # Salvar arquivo JSON
                backup_file = backup_dir / f"{category}_components.json"
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, indent=2, ensure_ascii=False)
                
                logger.debug(f"Backup JSON criado: {backup_file}") 