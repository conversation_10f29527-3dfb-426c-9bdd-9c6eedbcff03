"""
Storybook Complete Parser
Parser completo e robusto para extrair informações estruturadas do Storybook
"""

import logging
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup, Tag

logger = logging.getLogger(__name__)

class StorybookCompleteParser:
    """Parser completo para extrair informações estruturadas do Storybook para IA"""
    
    def __init__(self, scraper_instance=None):
        self.css_class_pattern = re.compile(r'brad-[\w-]+')
        self.scraper = scraper_instance  # Para seguir links quando necessário
    
    def parse_component(self, html_content: str, component_name: str = "") -> Dict[str, Any]:
        """
        Extrai informações estruturadas de um componente do Storybook
        
        Args:
            html_content: Conteúdo HTML do Storybook
            component_name: Nome do componente
            
        Returns:
            Dicionário com informações estruturadas do componente
        """
        logger.info(f"Parsing component: {component_name}")
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Encontrar área principal
        content = self._find_main_content(soup)
        
        # Extrair informações básicas
        result = {
            "component_name": self._extract_name(content) or component_name,
            "description": self._extract_description(content),
            "purpose": "",
            "html_usage": self._extract_html_code(content),
            "javascript_usage": self._extract_js_code(content),
            "css_classes": self._extract_css_classes(content),
            "properties": self._extract_properties_table(content),
            "methods": self._extract_methods_table(content),
            "accessibility": self._extract_accessibility_text(content),
            "variants": self._extract_variants(content),
            "examples": self._extract_examples(content)
        }
        
        # Extrair propósito da descrição
        result["purpose"] = self._extract_purpose(result["description"])
        
        return result
    
    def _find_main_content(self, soup: BeautifulSoup) -> Tag:
        """Encontra área principal de conteúdo"""
        content_selectors = [
            'div.sbdocs-content',
            'div.sbdocs-wrapper', 
            '#storybook-docs',
            'main',
            'body'
        ]
        
        for selector in content_selectors:
            content = soup.select_one(selector)
            if content:
                return content
        
        return soup
    
    def _extract_name(self, content: Tag) -> str:
        """Extrai nome do componente"""
        logger.debug("Iniciando extração de nome do componente...")
        
        # Buscar h1
        h1 = content.find('h1')
        if h1:
            name = h1.get_text(strip=True)
            logger.debug(f"H1 encontrado: '{name}'")
            if name and name != "Storybook":
                logger.debug(f"✅ Nome extraído do H1: {name}")
                return name
        
        # Buscar por ID que contenha nome do componente (lista expandida)
        component_keywords = [
            'button', 'accordion', 'dropdown', 'card', 'modal', 'tooltip',
            'bottomsheet', 'tabs', 'input', 'select', 'checkbox', 'radio',
            'slider', 'spinner', 'alert', 'badge', 'breadcrumb', 'carousel',
            'collapse', 'datepicker', 'form', 'grid', 'list', 'menu',
            'navigation', 'pagination', 'progress', 'table', 'toast'
        ]
        
        for elem in content.find_all(attrs={'id': True}):
            id_val = elem.get('id', '').lower()
            for keyword in component_keywords:
                if keyword in id_val:
                    text = elem.get_text(strip=True)
                    if text and len(text) < 100:  # Evitar textos muito longos
                        return text
        
        # Buscar em títulos de anchor
        anchors = content.find_all('div', class_='sb-anchor')
        logger.debug(f"Encontrados {len(anchors)} elementos anchor")
        
        for elem in anchors:
            anchor_id = elem.get('id', '')
            logger.debug(f"Anchor ID: '{anchor_id}'")
            
            if anchor_id:
                # Extrair nome do ID do anchor
                # Ex: anchor--designsystem-components-bottomsheet-fixed--default
                match = re.search(r'anchor--.*?components-([^-]+)(?:-([^-]+))?--', anchor_id)
                if match:
                    base_name = match.group(1)
                    variant = match.group(2)
                    logger.debug(f"Match anchor - base: '{base_name}', variant: '{variant}'")
                    
                    component_name = base_name.replace('webcomponent', '').replace('html', '').strip('-')
                    component_name = component_name.capitalize()
                    
                    if variant and variant not in ['webcomponent', 'html', 'default']:
                        component_name += f" {variant.capitalize()}"
                    
                    logger.debug(f"✅ Nome extraído do anchor: {component_name}")
                    return component_name
        
        # Buscar por IDs que começam com anchor
        all_anchors = content.find_all(attrs={'id': lambda x: x and x.startswith('anchor--')})
        logger.debug(f"Encontrados {len(all_anchors)} elementos com ID anchor")
        
        for elem in all_anchors:
            anchor_id = elem.get('id', '')
            logger.debug(f"ID anchor encontrado: '{anchor_id}'")
            
            # Tentar extrair nome mais flexível
            match = re.search(r'anchor--.*?-([^-]+)(?:-([^-]+))?--', anchor_id)
            if match:
                base_name = match.group(1)
                variant = match.group(2)
                logger.debug(f"Match flexível - base: '{base_name}', variant: '{variant}'")
                
                # Lista mais abrangente de palavras a evitar
                skip_names = ['designsystem', 'components', 'docs', 'documentation', 'default', 'example']
                if base_name not in skip_names:
                    component_name = base_name.replace('webcomponent', '').replace('html', '').strip('-')
                    component_name = component_name.capitalize()
                    
                    # Verificar se o nome final é válido
                    if component_name and len(component_name) > 1:
                        if variant and variant not in ['webcomponent', 'html', 'default', 'docs']:
                            component_name += f" {variant.capitalize()}"
                        
                        logger.debug(f"✅ Nome extraído (flexível): {component_name}")
                        return component_name
        
        logger.debug("❌ Nenhum nome encontrado")
        return "Unknown Component"
    
    def _extract_description(self, content: Tag) -> str:
        """Extrai descrição do componente"""
        h1 = content.find('h1')
        if not h1:
            return ""
        
        descriptions = []
        current = h1.next_sibling
        
        # Buscar próximos elementos após h1
        while current and len(descriptions) < 3:
            if hasattr(current, 'name') and current.name == 'p':
                text = current.get_text(strip=True)
                if text and len(text) > 20:
                    descriptions.append(text)
            elif hasattr(current, 'name') and current.name in ['h2', 'h3']:
                break
            
            try:
                current = current.next_sibling
            except:
                break
        
        return " ".join(descriptions)
    
    def _extract_purpose(self, description: str) -> str:
        """Extrai propósito do componente da descrição"""
        if not description:
            return ""
        
        # Padrões para identificar propósito
        patterns = [
            r'é utilizado para ([^.!]+)',
            r'serve para ([^.!]+)',
            r'usado para ([^.!]+)',
            r'utilizado quando ([^.!]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # Fallback: primeiras 150 caracteres
        return description[:150] + "..." if len(description) > 150 else description
    
    def _extract_html_code(self, content: Tag) -> str:
        """Extrai código HTML do componente"""
        html_codes = []
        
        # Buscar todas as tags <pre> e <code>
        code_blocks = content.find_all(['pre', 'code'])
        
        for block in code_blocks:
            code = block.get_text()
            # Filtrar apenas código HTML (que contém < e >)
            if '<' in code and '>' in code:
                # Verificar se contém classes do design system ou tags HTML
                if any(keyword in code for keyword in ['brad-', '<div', '<button', '<span', '<p', '<h']):
                    cleaned_code = self._clean_code(code)
                    if cleaned_code:
                        html_codes.append(cleaned_code)
        
        # Buscar também em divs com classes específicas do Storybook
        storybook_code_blocks = content.find_all('div', class_=lambda x: x and any(cls in x for cls in ['docblock-source', 'css-', 'prismjs', 'os-content']))
        
        for block in storybook_code_blocks:
            code = block.get_text()
            if '<' in code and '>' in code:
                cleaned_code = self._clean_code(code)
                if cleaned_code:
                    html_codes.append(cleaned_code)
        
        # Buscar especificamente em divs com classe "os-content" (formato do Storybook)
        os_content_divs = content.find_all('div', class_='os-content')
        for div in os_content_divs:
            # Buscar dentro do os-content por pre com prismjs
            pre_blocks = div.find_all('pre', class_=lambda x: x and 'prismjs' in x)
            for pre in pre_blocks:
                code = pre.get_text()
                if '<' in code and '>' in code:
                    cleaned_code = self._clean_code(code)
                    if cleaned_code:
                        html_codes.append(cleaned_code)
        
        return "\n\n".join(html_codes)
    
    def _extract_js_code(self, content: Tag) -> str:
        """Extrai código JavaScript do componente"""
        js_codes = []
        
        # Buscar todas as tags <pre> e <code>
        code_blocks = content.find_all(['pre', 'code'])
        
        for block in code_blocks:
            code = block.get_text()
            # Filtrar apenas código JS (que contém palavras-chave JS)
            if any(keyword in code for keyword in ['const', 'function', 'getInstance', 'LiquidCorp', 'var', 'let', 'document', 'querySelector', 'addEventListener', 'onclick']):
                cleaned_code = self._clean_code(code)
                if cleaned_code:
                    js_codes.append(cleaned_code)
        
        # Buscar também em divs com classes específicas do Storybook
        storybook_code_blocks = content.find_all('div', class_=lambda x: x and any(cls in x for cls in ['docblock-source', 'css-', 'prismjs', 'os-content']))
        
        for block in storybook_code_blocks:
            code = block.get_text()
            if any(keyword in code for keyword in ['const', 'function', 'getInstance', 'LiquidCorp', 'var', 'let', 'document', 'querySelector', 'addEventListener', 'onclick']):
                cleaned_code = self._clean_code(code)
                if cleaned_code:
                    js_codes.append(cleaned_code)
        
        # Buscar especificamente em divs com classe "os-content" (formato do Storybook)
        os_content_divs = content.find_all('div', class_='os-content')
        for div in os_content_divs:
            # Buscar dentro do os-content por pre com prismjs
            pre_blocks = div.find_all('pre', class_=lambda x: x and 'prismjs' in x)
            for pre in pre_blocks:
                code = pre.get_text()
                if any(keyword in code for keyword in ['const', 'function', 'getInstance', 'LiquidCorp', 'var', 'let', 'document', 'querySelector', 'addEventListener', 'onclick']):
                    cleaned_code = self._clean_code(code)
                    if cleaned_code:
                        js_codes.append(cleaned_code)
        
        return "\n\n".join(js_codes)
    
    def _extract_css_classes(self, content: Tag) -> List[str]:
        """Extrai classes CSS do design system"""
        classes = set()
        
        # Buscar em todo o conteúdo HTML
        text_content = content.get_text()
        found_classes = self.css_class_pattern.findall(text_content)
        
        # Limpar e filtrar classes
        for class_name in found_classes:
            # Remover texto adicional que pode estar concatenado
            # Ex: "brad-badgeComponenteSimNãoComponente" -> "brad-badge"
            clean_class = self._clean_css_class(class_name)
            if clean_class:
                classes.add(clean_class)
        
        # Buscar especificamente em elementos <code>
        code_tags = content.find_all('code')
        for code in code_tags:
            text = code.get_text()
            found_classes = self.css_class_pattern.findall(text)
            
            for class_name in found_classes:
                clean_class = self._clean_css_class(class_name)
                if clean_class:
                    classes.add(clean_class)
        
        return sorted(list(classes))
    
    def _clean_css_class(self, class_name: str) -> str:
        """
        Limpa uma classe CSS removendo texto adicional concatenado.
        
        Args:
            class_name: Nome da classe CSS (pode conter texto adicional)
            
        Returns:
            Nome da classe limpo
        """
        if not class_name.startswith('brad-'):
            return ""
        
        # Padrões para identificar onde a classe CSS termina
        # Ex: "brad-badgeComponenteSimNãoComponente" -> "brad-badge"
        patterns = [
            # Classes que terminam com palavras específicas
            r'(brad-[\w-]+?)(?:Componente|Sim|Não|string|boolean|number|Valor|Se|Tipo|Default|Descrição)',
            # Classes que terminam com letras maiúsculas seguidas de minúsculas
            r'(brad-[\w-]+?)(?=[A-Z][a-z])',
            # Classes que terminam com números
            r'(brad-[\w-]+?)(?=\d)',
            # Classes que terminam com caracteres especiais
            r'(brad-[\w-]+?)(?=[^\w-])',
        ]
        
        for pattern in patterns:
            match = re.match(pattern, class_name)
            if match:
                return match.group(1)
        
        # Se não encontrou padrão, verificar se é uma classe válida
        # Classes válidas geralmente terminam com hífen seguido de palavra
        if re.match(r'^brad-[\w-]+$', class_name):
            return class_name
        
        # Fallback: extrair apenas a parte que parece uma classe válida
        match = re.match(r'(brad-[\w-]+)', class_name)
        if match:
            return match.group(1)
        
        return ""
    
    def _extract_properties_table(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai tabela de propriedades/opções"""
        properties = []
        
        tables = content.find_all('table')
        
        for table in tables:
            try:
                # Buscar cabeçalhos
                headers = []
                header_row = table.find('tr')
                if header_row:
                    header_cells = header_row.find_all(['th', 'td'])
                    headers = [cell.get_text(strip=True).lower() for cell in header_cells]
                
                # Verificar se é tabela de propriedades
                if any(keyword in ' '.join(headers) for keyword in ['nome', 'tipo', 'descrição', 'classe', 'default']):
                    rows = table.find_all('tr')[1:]  # Pular cabeçalho
                    
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            cell_values = [cell.get_text(strip=True) for cell in cells]
                            
                            prop = {
                                "name": cell_values[0] if len(cell_values) > 0 else "",
                                "type": cell_values[1] if len(cell_values) > 1 else "",
                                "default": cell_values[2] if len(cell_values) > 2 else "",
                                "description": cell_values[-1] if len(cell_values) > 1 else ""
                            }
                            
                            if prop["name"]:
                                properties.append(prop)
            except Exception as e:
                logger.warning(f"Error parsing property table: {e}")
                continue
        
        return properties
    
    def _extract_methods_table(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai tabela de métodos"""
        methods = []
        
        tables = content.find_all('table')
        
        for table in tables:
            try:
                # Buscar cabeçalhos
                headers = []
                header_row = table.find('tr')
                if header_row:
                    header_cells = header_row.find_all(['th', 'td'])
                    headers = [cell.get_text(strip=True).lower() for cell in header_cells]
                
                # Verificar se é tabela de métodos
                if any(keyword in ' '.join(headers) for keyword in ['método', 'method', 'parâmetro', 'parameter']):
                    rows = table.find_all('tr')[1:]  # Pular cabeçalho
                    
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            cell_values = [cell.get_text(strip=True) for cell in cells]
                            
                            method = {
                                "name": cell_values[0] if len(cell_values) > 0 else "",
                                "parameters": cell_values[1] if len(cell_values) > 1 else "",
                                "description": cell_values[2] if len(cell_values) > 2 else cell_values[-1]
                            }
                            
                            if method["name"]:
                                methods.append(method)
            except Exception as e:
                logger.warning(f"Error parsing methods table: {e}")
                continue
        
        return methods
    
    def _extract_accessibility_text(self, content: Tag) -> str:
        """Extrai texto sobre acessibilidade, seguindo links quando necessário"""
        accessibility_text = []
        
        # Buscar cabeçalhos relacionados à acessibilidade
        headings = content.find_all(['h2', 'h3', 'h4'])
        
        for heading in headings:
            heading_text = heading.get_text(strip=True).lower()
            if 'acessibilidade' in heading_text or 'accessibility' in heading_text:
                # Buscar parágrafos seguintes
                current = heading.next_sibling
                
                while current:
                    if hasattr(current, 'name'):
                        if current.name == 'p':
                            text = current.get_text(strip=True)
                            if text:
                                # Verificar se é uma referência a outro componente
                                if self._is_accessibility_reference(text):
                                    logger.debug("Encontrada referência de acessibilidade")
                                    # Buscar link na seção
                                    link = self._find_accessibility_link(current.parent if current.parent else content)
                                    if link and self.scraper:
                                        logger.info(f"Seguindo link de acessibilidade: {link}")
                                        linked_content = self._fetch_accessibility_from_link(link)
                                        if linked_content:
                                            accessibility_text.append(linked_content)
                                        else:
                                            accessibility_text.append(text)  # Fallback
                                    else:
                                        accessibility_text.append(text)
                                else:
                                    accessibility_text.append(text)
                        elif current.name in ['h2', 'h3', 'h4']:
                            break  # Nova seção
                    
                    try:
                        current = current.next_sibling
                    except:
                        break
        
        result = " ".join(accessibility_text)
        return result if result else ""
    
    def _extract_variants(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai variantes do componente"""
        variants = []
        
        # Buscar cabeçalhos de nível 3 e 4 (potenciais variantes)
        headings = content.find_all(['h3', 'h4'])
        
        for heading in headings:
            heading_text = heading.get_text(strip=True)
            
            # Pular seções que não são variantes
            if any(skip in heading_text.lower() for skip in ['método', 'acessibilidade', 'inicialização', 'options']):
                continue
            
            # Buscar exemplo HTML relacionado
            html_example = self._find_variant_html(heading)
            
            if html_example:
                variant = {
                    "name": heading_text,
                    "html_example": html_example,
                    "css_classes": ', '.join(self.css_class_pattern.findall(html_example))
                }
                variants.append(variant)
        
        return variants
    
    def _find_variant_html(self, heading: Tag) -> str:
        """Encontra HTML de exemplo para uma variante"""
        current = heading
        
        # Buscar nos próximos 20 elementos
        for _ in range(20):
            try:
                current = current.next_sibling
                if not current:
                    break
                
                if hasattr(current, 'find_all'):
                    # Buscar divs que podem conter exemplos
                    example_divs = current.find_all('div')
                    
                    for div in example_divs:
                        # Verificar se contém classes do design system
                        div_html = str(div)
                        if 'brad-' in div_html and 'sb-story' in div_html:
                            # Extrair conteúdo interno limpo
                            inner_content = self._extract_clean_html(div)
                            if inner_content:
                                return inner_content
            except:
                continue
        
        return ""
    
    def _extract_examples(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai exemplos práticos"""
        examples = []
        
        # Buscar divs que contenham story blocks ou exemplos
        story_divs = content.find_all('div')
        
        for div in story_divs:
            try:
                div_classes = div.get('class', [])
                div_id = div.get('id', '')
                
                # Verificar se é um story block ou exemplo
                is_story = any('story' in str(cls) for cls in div_classes) or 'story' in div_id
                is_example = any('example' in str(cls) for cls in div_classes) or 'example' in div_id
                has_brad_classes = 'brad-' in str(div)
                
                if is_story or is_example or has_brad_classes:
                    # Extrair nome do exemplo
                    name = div.get('data-name', '')
                    if not name:
                        # Buscar heading anterior
                        prev_heading = div.find_previous(['h3', 'h4', 'h2'])
                        if prev_heading:
                            name = prev_heading.get_text(strip=True)
                    
                    # Extrair HTML limpo
                    clean_html = self._extract_clean_html(div)
                    
                    if clean_html and name:
                        examples.append({
                            "name": name,
                            "html": clean_html
                        })
            except Exception as e:
                logger.debug(f"Erro ao extrair exemplo: {e}")
                continue
        
        # Se não encontrou exemplos, tentar buscar em outras estruturas
        if not examples:
            # Buscar em iframes (exemplos renderizados)
            iframes = content.find_all('iframe')
            for iframe in iframes:
                try:
                    src = iframe.get('src', '')
                    if 'story' in src:
                        # Extrair nome do iframe
                        name = iframe.get('title', 'Exemplo')
                        examples.append({
                            "name": name,
                            "html": f"<iframe src=\"{src}\" title=\"{name}\"></iframe>"
                        })
                except:
                    continue
        
        return examples
    
    def _extract_clean_html(self, element: Tag) -> str:
        """Extrai HTML limpo de um elemento"""
        try:
            # Buscar div interno com conteúdo real
            inner_div = element.find('div')
            while inner_div:
                inner_html = str(inner_div)
                if 'brad-' in inner_html and not any(wrapper in inner_html for wrapper in ['sb-story', 'css-', 'innerZoom']):
                    # Limpar HTML
                    cleaned = self._clean_html(str(inner_div))
                    if cleaned:
                        return cleaned
                
                inner_div = inner_div.find('div')
            
            # Se não encontrou, tentar extrair diretamente do elemento
            element_html = str(element)
            if 'brad-' in element_html:
                cleaned = self._clean_html(element_html)
                if cleaned:
                    return cleaned
            
            return ""
        except Exception as e:
            logger.debug(f"Erro ao extrair HTML limpo: {e}")
            return ""
    
    def _clean_code(self, code: str) -> str:
        """Limpa código removendo elementos de syntax highlighting"""
        # Remover tags HTML de syntax highlighting
        code = re.sub(r'<span[^>]*>', '', code)
        code = re.sub(r'</span>', '', code)
        
        # Remover classes CSS de syntax highlighting
        code = re.sub(r'class="[^"]*css-[^"]*"', '', code)
        code = re.sub(r'class="[^"]*prismjs[^"]*"', '', code)
        code = re.sub(r'class="[^"]*token[^"]*"', '', code)
        
        # Remover atributos de syntax highlighting
        code = re.sub(r'style="[^"]*"', '', code)
        code = re.sub(r'data-[^=]*="[^"]*"', '', code)
        
        # Limpar espaços extras
        code = re.sub(r'\s+', ' ', code)
        code = re.sub(r'>\s+<', '><', code)
        
        # Remover quebras de linha desnecessárias
        code = re.sub(r'\n\s*\n', '\n', code)
        
        return code.strip()
    
    def _clean_html(self, html: str) -> str:
        """Limpa HTML mantendo apenas conteúdo relevante"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remover atributos desnecessários
            for elem in soup.find_all():
                # Manter apenas classes brad-*
                if elem.get('class'):
                    classes = elem.get('class')
                    brad_classes = [cls for cls in classes if cls.startswith('brad-')]
                    if brad_classes:
                        elem['class'] = brad_classes
                    else:
                        del elem['class']
                
                # Remover outros atributos
                for attr in ['id', 'data-name', 'scale', 'style']:
                    if elem.get(attr):
                        del elem[attr]
            
            return str(soup)
        except:
            return html
    
    def to_markdown(self, data: Dict[str, Any]) -> str:
        """Converte dados para markdown estruturado"""
        lines = [
            f"# {data['component_name']}",
            "",
            "## Descrição",
            data['description'],
            "",
            "## Propósito", 
            data['purpose'],
            ""
        ]
        
        if data['html_usage']:
            lines.extend([
                "## Uso HTML",
                "```html",
                data['html_usage'],
                "```",
                ""
            ])
        
        if data['javascript_usage']:
            lines.extend([
                "## Uso JavaScript",
                "```javascript",
                data['javascript_usage'],
                "```",
                ""
            ])
        
        if data['css_classes']:
            lines.extend([
                "## Classes CSS",
                "",
                *[f"- `{cls}`" for cls in data['css_classes']],
                ""
            ])
        
        if data['properties']:
            lines.extend([
                "## Propriedades",
                "",
                "| Nome | Tipo | Default | Descrição |",
                "|------|------|---------|-----------|"
            ])
            for prop in data['properties']:
                lines.append(f"| `{prop['name']}` | {prop['type']} | {prop['default']} | {prop['description']} |")
            lines.append("")
        
        if data['methods']:
            lines.extend([
                "## Métodos",
                "",
                "| Método | Parâmetros | Descrição |",
                "|--------|------------|-----------|"
            ])
            for method in data['methods']:
                lines.append(f"| `{method['name']}` | {method['parameters']} | {method['description']} |")
            lines.append("")
        
        if data['accessibility']:
            lines.extend([
                "## Acessibilidade",
                data['accessibility'],
                ""
            ])
        
        if data['variants']:
            lines.extend([
                "## Variantes",
                ""
            ])
            for variant in data['variants']:
                lines.extend([
                    f"### {variant['name']}",
                    "",
                    "```html",
                    variant['html_example'],
                    "```",
                    "",
                    f"**Classes:** {variant['css_classes']}",
                    ""
                ])
        
        return "\n".join(lines)
    
    def _is_accessibility_reference(self, text: str) -> bool:
        """Verifica se o texto é uma referência a acessibilidade de outro componente"""
        reference_patterns = [
            r'segue o mesmo.*html',
            r'clique aqui',
            r'ver.*documentação.*completa',
            r'consulte.*componente.*html'
        ]
        
        for pattern in reference_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False
    
    def _find_accessibility_link(self, section: Tag) -> Optional[str]:
        """Encontra link de acessibilidade na seção"""
        try:
            # Buscar links na seção atual
            links = section.find_all('a', href=True)
            for link in links:
                href = link.get('href', '')
                link_text = link.get_text(strip=True).lower()
                
                logger.debug(f"Analisando link: href='{href}', text='{link_text}'")
                
                # Verificar se é um link para componente HTML com âncora de acessibilidade
                if any(indicator in href for indicator in ['html--docs', '-html-']):
                    return href
                
                # Verificar links relativos com âncora de acessibilidade
                if href.startswith('./?path=') and 'acessibilidade' in href:
                    return href
                    
                # Verificar texto do link
                if any(keyword in link_text for keyword in ['clique aqui', 'html', 'documentação']):
                    return href
                    
        except Exception as e:
            logger.debug(f"Erro ao buscar link de acessibilidade: {e}")
        
        return None
    
    def _fetch_accessibility_from_link(self, link: str) -> Optional[str]:
        """Busca conteúdo de acessibilidade do link"""
        try:
            if not self.scraper or not self.scraper.driver:
                return None
            
            # Construir URL completa
            full_url = self._build_full_url(link)
            if not full_url:
                return None
            
            logger.info(f"Navegando para link de acessibilidade: {full_url}")
            
            # Salvar URL atual
            current_url = self.scraper.driver.current_url
            
            # Navegar para o link
            self.scraper.driver.get(full_url)
            
            # Aguardar carregamento
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By
            import time
            
            WebDriverWait(self.scraper.driver, 10).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, "#storybook-preview-iframe"))
            )
            
            # Aguardar um pouco mais para a âncora carregar
            time.sleep(1)
            
            # Extrair apenas seção de acessibilidade
            html_content = self.scraper.driver.page_source
            soup = BeautifulSoup(html_content, 'html.parser')
            content = self._find_main_content(soup)
            
            accessibility_content = self._extract_direct_accessibility_text(content)
            
            # Voltar para o iframe padrão
            self.scraper.driver.switch_to.default_content()
            
            # Retornar para URL original
            self.scraper.driver.get(current_url)
            
            # Aguardar retorno
            WebDriverWait(self.scraper.driver, 10).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, "#storybook-preview-iframe"))
            )
            
            return accessibility_content
            
        except Exception as e:
            logger.warning(f"Erro ao buscar acessibilidade do link {link}: {e}")
            try:
                # Tentar voltar para o iframe original
                self.scraper.driver.switch_to.default_content()
                self.scraper.driver.get(current_url)
                WebDriverWait(self.scraper.driver, 5).until(
                    EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, "#storybook-preview-iframe"))
                )
            except:
                pass
            return None
    
    def _build_full_url(self, link: str) -> Optional[str]:
        """Constrói URL completa a partir de um link relativo ou absoluto"""
        try:
            base_url = self.scraper.config.base_url.rstrip('/')
            
            if link.startswith('http'):
                return link
            elif link.startswith('./?path='):
                # Link relativo do Storybook: ./?path=/docs/...
                path_part = link[2:]  # Remove "./"
                full_url = base_url + path_part
                logger.debug(f"URL construída: {full_url}")
                return full_url
            elif link.startswith('/'):
                return base_url + link
            elif link.startswith('?'):
                return base_url + link
            else:
                # Tentar como relativo
                return base_url + '/' + link
                
        except Exception as e:
            logger.debug(f"Erro ao construir URL: {e}")
            return None
    
    def _extract_direct_accessibility_text(self, content: Tag) -> str:
        """Extrai texto de acessibilidade diretamente, sem seguir links"""
        accessibility_text = []
        
        headings = content.find_all(['h2', 'h3', 'h4'])
        
        for heading in headings:
            heading_text = heading.get_text(strip=True).lower()
            if 'acessibilidade' in heading_text or 'accessibility' in heading_text:
                current = heading.next_sibling
                
                while current:
                    if hasattr(current, 'name'):
                        if current.name == 'p':
                            text = current.get_text(strip=True)
                            if text and not self._is_accessibility_reference(text):
                                accessibility_text.append(text)
                        elif current.name in ['h2', 'h3', 'h4']:
                            break
                    
                    try:
                        current = current.next_sibling
                    except:
                        break
        
        return " ".join(accessibility_text)
    
    def to_ai_optimized_markdown(self, data: Dict[str, Any]) -> str:
        """Converte dados para markdown otimizado para contexto de IA"""
        lines = [
            f"# {data['component_name']}",
            "",
            f"**Tipo:** {data.get('category', 'Componente')}",
            f"**Propósito:** {data['purpose']}",
            "",
            data['description'],
            ""
        ]
        
        # Classes CSS (compacto)
        if data['css_classes']:
            lines.extend([
                "## Classes CSS",
                f"```{', '.join(data['css_classes'])}```",
                ""
            ])
        
        # Uso HTML (prioritário)
        if data['html_usage']:
            lines.extend([
                "## Implementação",
                "```html",
                data['html_usage'],
                "```",
                ""
            ])
        
        # JavaScript (se relevante)
        if data['javascript_usage'] and 'LiquidCorp' in data['javascript_usage']:
            lines.extend([
                "## API JavaScript",
                "```javascript",
                data['javascript_usage'][:500] + "..." if len(data['javascript_usage']) > 500 else data['javascript_usage'],
                "```",
                ""
            ])
        
        # Propriedades (compacto)
        if data['properties']:
            lines.extend(["## API Props"])
            for prop in data['properties'][:5]:  # Limitar para evitar sobrecarga
                lines.append(f"- **{prop['name']}** ({prop['type']}): {prop['description']}")
            lines.append("")
        
        # Variantes (essencial para geração)
        if data['variants']:
            lines.extend(["## Variantes"])
            for variant in data['variants'][:3]:  # Top 3 variantes
                lines.extend([
                    f"### {variant['name']}",
                    f"```html\n{variant['html_example']}\n```",
                    ""
                ])
        
        # Acessibilidade (importante)
        if data['accessibility']:
            lines.extend([
                "## Acessibilidade",
                data['accessibility'][:300] + "..." if len(data['accessibility']) > 300 else data['accessibility'],
                ""
            ])
        
        return "\n".join(lines)
