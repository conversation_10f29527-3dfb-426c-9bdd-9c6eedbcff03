"""
Templates Parser

Parser específico para extrair informações de Templates do Storybook.
Templates são estruturas mais complexas que combinam múltiplos componentes.
"""

import logging
import re
from typing import Dict, List, Any
from bs4 import BeautifulSoup, Tag

logger = logging.getLogger(__name__)

class TemplatesParser:
    """Parser para extrair informações de Templates do Storybook"""
    
    def __init__(self):
        self.logger = logger
    
    def parse_template(self, html_content: str, template_name: str = "") -> Dict[str, Any]:
        """
        Extrai informações estruturadas de um template do Storybook
        
        Args:
            html_content: Conteúdo HTML do Storybook
            template_name: Nome do template
            
        Returns:
            Dicionário com informações estruturadas do template
        """
        self.logger.info(f"Parsing template: {template_name}")
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Encontrar área principal
        content = self._find_main_content(soup)
        
        # Extrair informações estruturadas
        result = {
            "template_name": self._extract_name(content) or template_name,
            "description": self._extract_description(content),
            "purpose": self._extract_purpose(content),
            "layout_structure": self._extract_layout_structure(content),
            "components_used": self._extract_components_used(content),
            "html_usage": self._extract_html_code(content),
            "javascript_usage": self._extract_javascript_code(content),
            "css_classes": self._extract_css_classes(content),
            "variants": self._extract_variants(content),
            "examples": self._extract_examples(content),
            "responsive_behavior": self._extract_responsive_behavior(content),
            "accessibility": self._extract_accessibility_info(content),
            "best_practices": self._extract_best_practices(content),
            "implementation_notes": self._extract_implementation_notes(content),
            "color_variations": self._extract_color_variations(content),
            "script_requirements": self._extract_script_requirements(content)
        }
        
        return result
    
    def _find_main_content(self, soup: BeautifulSoup) -> Tag:
        """Encontra área principal de conteúdo"""
        content_selectors = [
            'div.sbdocs-content',
            'div.sbdocs-wrapper', 
            '#storybook-docs',
            'main',
            'body'
        ]
        
        for selector in content_selectors:
            content = soup.select_one(selector)
            if content:
                return content
        
        return soup
    
    def _extract_name(self, content: Tag) -> str:
        """Extrai nome do template"""
        # Buscar h1
        h1 = content.find('h1')
        if h1:
            name = h1.get_text(strip=True)
            if name and name != "Storybook":
                return name
        
        # Buscar por IDs que contenham 'template'
        for elem in content.find_all(attrs={'id': True}):
            id_val = elem.get('id', '').lower()
            if 'template' in id_val:
                text = elem.get_text(strip=True)
                if text and len(text) < 100:
                    return text
        
        return "Unknown Template"
    
    def _extract_description(self, content: Tag) -> str:
        """Extrai descrição do template"""
        h1 = content.find('h1')
        if not h1:
            return ""
        
        descriptions = []
        current = h1.next_sibling
        
        # Buscar próximos elementos após h1
        while current and len(descriptions) < 3:
            if hasattr(current, 'name') and current.name == 'p':
                text = current.get_text(strip=True)
                if text and len(text) > 20:
                    descriptions.append(text)
            elif hasattr(current, 'name') and current.name in ['h2', 'h3']:
                break
            
            try:
                current = current.next_sibling
            except:
                break
        
        return " ".join(descriptions)
    
    def _extract_purpose(self, content: Tag) -> str:
        """Extrai propósito do template"""
        # Buscar por seções que mencionem propósito
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if any(keyword in text for keyword in ['purpose', 'objetivo', 'when to use', 'quando usar']):
                # Coletar conteúdo da seção
                content_text = []
                current = heading.find_next_sibling()
                while current and current.name not in ['h2', 'h3']:
                    if current.name == 'p':
                        text = current.get_text(strip=True)
                        if text:
                            content_text.append(text)
                    current = current.find_next_sibling()
                
                if content_text:
                    return " ".join(content_text)
        
        return ""
    
    def _extract_layout_structure(self, content: Tag) -> Dict[str, Any]:
        """Extrai estrutura de layout do template"""
        layout_info = {
            "type": "unknown",
            "sections": [],
            "grid_system": "",
            "breakpoints": []
        }
        
        # Buscar por informações de layout
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            
            if 'layout' in text or 'estrutura' in text:
                # Extrair informações de layout
                sections = self._extract_layout_sections(heading)
                layout_info["sections"] = sections
            
            elif 'grid' in text:
                # Extrair informações de grid
                grid_info = self._extract_grid_info(heading)
                layout_info["grid_system"] = grid_info
            
            elif 'breakpoint' in text or 'responsive' in text:
                # Extrair breakpoints
                breakpoints = self._extract_breakpoints(heading)
                layout_info["breakpoints"] = breakpoints
        
        return layout_info
    
    def _extract_layout_sections(self, heading: Tag) -> List[Dict[str, str]]:
        """Extrai seções do layout"""
        sections = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name in ['h3', 'h4']:
                section_name = current.get_text(strip=True)
                section_desc = ""
                
                # Buscar descrição da seção
                desc_elem = current.find_next_sibling('p')
                if desc_elem:
                    section_desc = desc_elem.get_text(strip=True)
                
                sections.append({
                    "name": section_name,
                    "description": section_desc
                })
            
            current = current.find_next_sibling()
        
        return sections
    
    def _extract_grid_info(self, heading: Tag) -> str:
        """Extrai informações do sistema de grid"""
        current = heading.find_next_sibling()
        grid_info = []
        
        while current and current.name not in ['h2', 'h3']:
            if current.name in ['p', 'li']:
                text = current.get_text(strip=True)
                if text:
                    grid_info.append(text)
            current = current.find_next_sibling()
        
        return " ".join(grid_info)
    
    def _extract_breakpoints(self, heading: Tag) -> List[str]:
        """Extrai breakpoints responsivos"""
        breakpoints = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name in ['p', 'li']:
                text = current.get_text(strip=True)
                if text and any(bp in text.lower() for bp in ['xs', 'sm', 'md', 'lg', 'xl']):
                    breakpoints.append(text)
            current = current.find_next_sibling()
        
        return breakpoints
    
    def _extract_components_used(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai componentes utilizados no template"""
        components = []
        
        # Buscar por seções que mencionem componentes
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if any(keyword in text for keyword in ['component', 'componente', 'used', 'utilizado']):
                # Extrair lista de componentes
                component_list = self._extract_component_list(heading)
                components.extend(component_list)
        
        return components
    
    def _extract_component_list(self, heading: Tag) -> List[Dict[str, str]]:
        """Extrai lista de componentes"""
        components = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name == 'li':
                text = current.get_text(strip=True)
                if text:
                    # Tentar extrair nome e descrição do componente
                    parts = text.split(':', 1)
                    if len(parts) > 1:
                        components.append({
                            "name": parts[0].strip(),
                            "description": parts[1].strip()
                        })
                    else:
                        components.append({
                            "name": text.strip(),
                            "description": ""
                        })
            current = current.find_next_sibling()
        
        return components
    
    def _extract_html_code(self, content: Tag) -> str:
        """Extrai código HTML do template"""
        # Buscar por blocos de código HTML
        code_blocks = content.find_all('pre')
        
        for block in code_blocks:
            # Buscar por elementos que contenham código HTML
            code_elem = block.find('code')
            if not code_elem:
                # Tentar encontrar div com classe de linguagem HTML
                code_elem = block.find('div', class_=re.compile(r'language-html'))
            
            if code_elem:
                code_text = code_elem.get_text()
                # Verificar se é HTML (contém tags)
                if '<' in code_text and '>' in code_text:
                    # Limpar o código HTML removendo spans de syntax highlighting
                    clean_html = self._clean_html_code(code_text)
                    if clean_html:
                        return clean_html
        
        return ""
    
    def _clean_html_code(self, html_code: str) -> str:
        """Limpa código HTML removendo elementos de syntax highlighting"""
        # Remover spans de syntax highlighting do Storybook
        clean_code = re.sub(r'<span[^>]*>', '', html_code)
        clean_code = re.sub(r'</span>', '', clean_code)
        
        # Remover quebras de linha extras
        clean_code = re.sub(r'\n\s*\n', '\n', clean_code)
        
        return clean_code.strip()
    
    def _extract_javascript_code(self, content: Tag) -> str:
        """Extrai código JavaScript do template"""
        # Buscar por blocos de código JavaScript
        code_blocks = content.find_all('pre')
        
        for block in code_blocks:
            # Buscar por elementos que contenham código JavaScript
            code_elem = block.find('code')
            if not code_elem:
                # Tentar encontrar div com classe de linguagem JavaScript
                code_elem = block.find('div', class_=re.compile(r'language-js'))
            
            if code_elem:
                code_text = code_elem.get_text()
                # Verificar se é JavaScript
                if any(keyword in code_text.lower() for keyword in ['function', 'const', 'let', 'var', '=>', 'document', 'window']):
                    # Limpar o código JavaScript removendo spans de syntax highlighting
                    clean_js = self._clean_javascript_code(code_text)
                    if clean_js:
                        return clean_js
        
        return ""
    
    def _clean_javascript_code(self, js_code: str) -> str:
        """Limpa código JavaScript removendo elementos de syntax highlighting"""
        # Remover spans de syntax highlighting do Storybook
        clean_code = re.sub(r'<span[^>]*>', '', js_code)
        clean_code = re.sub(r'</span>', '', clean_code)
        
        # Remover quebras de linha extras
        clean_code = re.sub(r'\n\s*\n', '\n', clean_code)
        
        return clean_code.strip()
    
    def _extract_css_classes(self, content: Tag) -> List[str]:
        """Extrai classes CSS do template"""
        classes = []
        css_class_pattern = re.compile(r'brad-[\w-]+')
        
        # Buscar por classes brad-* em todo o conteúdo
        for elem in content.find_all(attrs={'class': True}):
            element_classes = elem.get('class', [])
            if isinstance(element_classes, str):
                element_classes = element_classes.split()
            
            for cls in element_classes:
                if css_class_pattern.match(cls):
                    classes.append(cls)
        
        return list(set(classes))  # Remover duplicatas
    
    def _extract_variants(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai variantes do template"""
        variants = []
        
        # Buscar por seções de variantes
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if 'variant' in text or 'variação' in text:
                # Extrair variantes
                variant_list = self._extract_variant_list(heading)
                variants.extend(variant_list)
        
        return variants
    
    def _extract_variant_list(self, heading: Tag) -> List[Dict[str, str]]:
        """Extrai lista de variantes"""
        variants = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name in ['h3', 'h4']:
                variant_name = current.get_text(strip=True)
                variant_desc = ""
                variant_code = ""
                
                # Buscar descrição e código da variante
                desc_elem = current.find_next_sibling('p')
                if desc_elem:
                    variant_desc = desc_elem.get_text(strip=True)
                
                # Buscar código da variante
                code_elem = current.find_next_sibling('pre')
                if code_elem:
                    code_inner = code_elem.find('code')
                    if code_inner:
                        variant_code = code_inner.get_text(strip=True)
                
                variants.append({
                    "name": variant_name,
                    "description": variant_desc,
                    "code": variant_code
                })
            
            current = current.find_next_sibling()
        
        return variants
    
    def _extract_examples(self, content: Tag) -> List[Dict[str, str]]:
        """Extrai exemplos do template"""
        examples = []
        
        # Buscar por seções de exemplos
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if 'example' in text or 'exemplo' in text:
                # Extrair exemplos
                example_list = self._extract_example_list(heading)
                examples.extend(example_list)
        
        return examples
    
    def _extract_example_list(self, heading: Tag) -> List[Dict[str, str]]:
        """Extrai lista de exemplos"""
        examples = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name in ['h3', 'h4']:
                example_name = current.get_text(strip=True)
                example_desc = ""
                example_code = ""
                
                # Buscar descrição e código do exemplo
                desc_elem = current.find_next_sibling('p')
                if desc_elem:
                    example_desc = desc_elem.get_text(strip=True)
                
                # Buscar código do exemplo
                code_elem = current.find_next_sibling('pre')
                if code_elem:
                    code_inner = code_elem.find('code')
                    if code_inner:
                        example_code = code_inner.get_text(strip=True)
                
                examples.append({
                    "name": example_name,
                    "description": example_desc,
                    "code": example_code
                })
            
            current = current.find_next_sibling()
        
        return examples
    
    def _extract_responsive_behavior(self, content: Tag) -> Dict[str, Any]:
        """Extrai comportamento responsivo do template"""
        responsive_info = {
            "mobile": "",
            "tablet": "",
            "desktop": "",
            "breakpoints": []
        }
        
        # Buscar por informações responsivas
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            
            if 'mobile' in text:
                responsive_info["mobile"] = self._extract_section_content(heading)
            elif 'tablet' in text:
                responsive_info["tablet"] = self._extract_section_content(heading)
            elif 'desktop' in text:
                responsive_info["desktop"] = self._extract_section_content(heading)
            elif 'breakpoint' in text:
                responsive_info["breakpoints"] = self._extract_breakpoints(heading)
        
        return responsive_info
    
    def _extract_section_content(self, heading: Tag) -> str:
        """Extrai conteúdo de uma seção"""
        content = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name in ['p', 'li']:
                text = current.get_text(strip=True)
                if text:
                    content.append(text)
            current = current.find_next_sibling()
        
        return " ".join(content)
    
    def _extract_accessibility_info(self, content: Tag) -> Dict[str, str]:
        """Extrai informações de acessibilidade"""
        accessibility_info = {
            "guidelines": "",
            "aria_labels": [],
            "keyboard_navigation": "",
            "screen_reader": ""
        }
        
        # Buscar por seções de acessibilidade
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            
            if 'accessibility' in text or 'acessibilidade' in text:
                accessibility_info["guidelines"] = self._extract_section_content(heading)
            elif 'aria' in text:
                aria_labels = self._extract_aria_labels(heading)
                accessibility_info["aria_labels"] = aria_labels
            elif 'keyboard' in text:
                accessibility_info["keyboard_navigation"] = self._extract_section_content(heading)
            elif 'screen reader' in text:
                accessibility_info["screen_reader"] = self._extract_section_content(heading)
        
        return accessibility_info
    
    def _extract_aria_labels(self, heading: Tag) -> List[str]:
        """Extrai labels ARIA"""
        aria_labels = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name in ['p', 'li']:
                text = current.get_text(strip=True)
                if text and 'aria-' in text.lower():
                    aria_labels.append(text)
            current = current.find_next_sibling()
        
        return aria_labels
    
    def _extract_best_practices(self, content: Tag) -> List[str]:
        """Extrai melhores práticas"""
        best_practices = []
        
        # Buscar por seções de melhores práticas
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if any(keyword in text for keyword in ['best practice', 'melhor prática', 'guideline', 'diretriz']):
                # Extrair lista de práticas
                practices = self._extract_practice_list(heading)
                best_practices.extend(practices)
        
        return best_practices
    
    def _extract_practice_list(self, heading: Tag) -> List[str]:
        """Extrai lista de melhores práticas"""
        practices = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name == 'li':
                text = current.get_text(strip=True)
                if text:
                    practices.append(text)
            current = current.find_next_sibling()
        
        return practices
    
    def _extract_implementation_notes(self, content: Tag) -> List[str]:
        """Extrai notas de implementação"""
        notes = []
        
        # Buscar por seções de implementação
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if any(keyword in text for keyword in ['implementation', 'implementação', 'usage', 'uso']):
                # Extrair notas
                notes.extend(self._extract_section_list(heading))
        
        return notes
    
    def _extract_section_list(self, heading: Tag) -> List[str]:
        """Extrai lista de uma seção"""
        items = []
        current = heading.find_next_sibling()
        
        while current and current.name not in ['h2', 'h3']:
            if current.name in ['p', 'li']:
                text = current.get_text(strip=True)
                if text:
                    items.append(text)
            current = current.find_next_sibling()
        
        return items
    
    def _extract_color_variations(self, content: Tag) -> List[str]:
        """Extrai variações de cores"""
        color_variations = []
        
        # Buscar por seções de cores
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if any(keyword in text for keyword in ['color', 'cor', 'variation', 'variação']):
                # Extrair variações de cores
                variations = self._extract_section_list(heading)
                color_variations.extend(variations)
        
        return color_variations
    
    def _extract_script_requirements(self, content: Tag) -> Dict[str, Any]:
        """Extrai requisitos de scripts"""
        script_info = {
            "required_scripts": [],
            "optional_scripts": [],
            "implementation_notes": ""
        }
        
        # Buscar por seções de scripts
        for heading in content.find_all(['h2', 'h3']):
            text = heading.get_text(strip=True).lower()
            if 'script' in text:
                # Extrair informações de scripts
                script_info["implementation_notes"] = self._extract_section_content(heading)
                
                # Buscar por blocos de código JavaScript
                code_blocks = heading.find_next_siblings('pre')
                for block in code_blocks:
                    code_elem = block.find('code')
                    if code_elem:
                        script_code = code_elem.get_text(strip=True)
                        if script_code:
                            script_info["required_scripts"].append(script_code)
        
        return script_info 