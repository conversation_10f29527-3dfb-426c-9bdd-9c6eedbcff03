# -*- coding: utf-8 -*-
"""
Extrator para componentes do Storybook.

Este extrator é responsável por:
- Extrair conteúdo de componentes usando parsers específicos
- Converter para markdown ou outros formatos
- Salvar arquivos estruturados
"""

import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

logger = logging.getLogger(__name__)

@dataclass
class StorybookExtractorConfig:
    """Configuração para o extrator do Storybook."""
    base_url: str
    output_dir: Path
    headless: bool = True
    timeout: int = 30
    wait_time: int = 2
    max_retries: int = 3

class StorybookExtractor:
    """
    Extrator para componentes do Storybook usando parsers específicos.
    """
    
    def __init__(self, config: StorybookExtractorConfig):
        """Inicializa o extrator do Storybook."""
        self.config = config
        self.driver = None
        self.components = {}
        self.visited_urls = set()
        
        # Inicializar parsers
        from src.design_system.parsers_json.storybook_parser import StorybookCompleteParser
        from src.design_system.parsers_json.templates_parser import TemplatesParser
        from src.design_system.parsers_json.classes_parser import ClassesJsonParser
        from src.design_system.parsers_json.images_parser import ImagesParser
        from src.design_system.parsers import BaseParser
        from src.design_system.parsers.classes_parser import ClassesParser
        
        self.storybook_parser = StorybookCompleteParser()
        self.templates_parser = TemplatesParser()
        self.classes_parser = ClassesJsonParser()
        self.images_parser = ImagesParser()
        self.markdown_extractor = BaseParser()
        self.classes_markdown_parser = ClassesParser()
        logger.debug(f"BaseParser inicializado: {type(self.markdown_extractor)}")
        logger.debug(f"Métodos disponíveis: {[m for m in dir(self.markdown_extractor) if not m.startswith('_')]}")
        
        self.config.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Storybook Extractor inicializado para: {config.base_url}")
    
    def __enter__(self):
        self._setup_driver()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self._cleanup()
    
    def extract_components(self, component_urls: Dict[str, Any], limit: Optional[int] = None) -> Dict[str, Dict[str, Any]]:
        """
        Extrai componentes usando parsers específicos.
        
        Args:
            component_urls: URLs dos componentes a serem extraídos
            limit: Número máximo de componentes a serem extraídos
            
        Returns:
            Dicionário com componentes extraídos
        """
        logger.info("Iniciando extração de componentes do Storybook...")
        
        try:
            # Aplicar limite se especificado
            if limit:
                component_urls = dict(list(component_urls.items())[:limit])
            
            # Processar componentes por tipo
            self._process_components_by_type(component_urls)
            
            logger.info(f"✅ Extração concluída: {len(self.components)} componentes extraídos.")
            return self.components
            
        except Exception as e:
            logger.error(f"❌ Erro durante extração: {e}", exc_info=True)
            raise
    
    def _process_components_by_type(self, urls: Dict[str, Any]) -> None:
        """Processa componentes usando parsers específicos por tipo."""
        for i, (component_id, url_data) in enumerate(urls.items(), 1):
            try:
                if isinstance(url_data, dict) and url_data.get("type") == "images_item":
                    # Processar item de Images
                    self._process_images_item(component_id, url_data, i, len(urls))
                else:
                    # Processar componente normal
                    self._process_normal_component(component_id, url_data, i, len(urls))
                
                time.sleep(self.config.wait_time)
                
            except Exception as e:
                logger.error(f"❌ Erro ao processar {component_id}: {e}")
                continue
    
    def _process_normal_component(self, component_id: str, url: str, current: int, total: int) -> None:
        """Processa componente normal usando parser apropriado."""
        
        # Determinar tipo de componente
        component_type = self._determine_component_type(component_id, url)
        logger.info(f"Extraindo {component_type} {current}/{total}: {component_id}")
        
        # Extrair dados usando parser específico
        component_data = self._extract_component_with_parser(component_id, url, component_type)
        
        if component_data:
            self.components[component_id] = component_data
            logger.info(f"✅ Componente {component_data.get('name', component_id)} extraído com sucesso")
        else:
            logger.warning(f"⚠️ Falha na extração do componente {component_id}")
    
    def _process_images_item(self, component_id: str, url_data: Dict[str, Any], current: int, total: int) -> None:
        """Processa item de Images usando ImagesParser."""
        logger.info(f"Extraindo item de Images {current}/{total}: {component_id}")
        
        url = url_data["url"]
        category = url_data["category"]
        
        images_data = self.images_parser.parse_images_category(
            self.driver, url, category, self.config.wait_time, self.config.timeout
        )
        
        if images_data:
            self.components[component_id] = images_data
            logger.info(f"✅ Item de Images {category} extraído com sucesso: {images_data.get('total_items', 0)} itens")
        else:
            logger.warning(f"⚠️ Falha na extração do item de Images {component_id}")
    
    def _determine_component_type(self, component_id: str, url: str) -> str:
        """Determina o tipo de componente baseado no ID e URL."""
        component_id_lower = component_id.lower()
        url_lower = url.lower()
        
        if 'template' in component_id_lower or 'template' in url_lower:
            return 'template'
        elif 'class' in component_id_lower or 'utility' in component_id_lower or 'utilities' in url_lower:
            return 'class'
        else:
            return 'component'
    
    def _extract_component_with_parser(self, component_id: str, url: str, component_type: str) -> Optional[Dict[str, Any]]:
        """Extrai componente usando parser específico."""
        if url in self.visited_urls:
            return None
        
        self.visited_urls.add(url)
        
        try:
            # Navegar para a página
            self.driver.get(url)
            
            # Aguardar carregamento
            iframe_selector = "#storybook-preview-iframe"
            WebDriverWait(self.driver, self.config.timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, iframe_selector))
            )
            
            WebDriverWait(self.driver, self.config.timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.sbdocs-content"))
            )
            time.sleep(1)
            
            # Clicar em botões "Show code"
            self._click_show_code_buttons()
            
            # Extrair HTML
            raw_html = self.driver.page_source
            
            # Usar parser específico
            if component_type == 'template':
                parsed_data = self.templates_parser.parse_template(raw_html, component_id)
            elif component_type == 'class':
                parsed_data = self.classes_parser.parse_classes(raw_html, component_id)
            else:
                parsed_data = self.storybook_parser.parse_component(raw_html, component_id)
            
            # Adicionar metadados
            parsed_data.update({
                "id": component_id,
                "url": url,
                "component_type": component_type,
                "extracted_at": datetime.now().isoformat()
            })
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"Erro ao extrair {component_id}: {e}")
            return None
        finally:
            try:
                self.driver.switch_to.default_content()
            except:
                pass
    
    def _click_show_code_buttons(self) -> None:
        """Clica em botões 'Show code' para revelar código de forma otimizada."""
        try:
            # Aguardar menos tempo para carregamento inicial
            time.sleep(0.5)
            # Seletores mais específicos e eficientes
            show_code_selectors = [
                "button[class*='docblock-code-toggle']",  # Mais específico
                "button[class*='show-code']",
                "button[class*='ShowCode']",
                "//button[contains(text(), 'Show code')]",  # XPath como fallback
                "//button[contains(text(), 'Show Code')]"
            ]
            
            buttons_clicked = 0
            max_buttons = 10  # Limitar número de botões para evitar loops infinitos
            
            for selector in show_code_selectors:
                if buttons_clicked >= max_buttons:
                    break
                    
                try:
                    if selector.startswith("//"):
                        # XPath selector
                        buttons = self.driver.find_elements(By.XPATH, selector)
                    else:
                        # CSS selector
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for button in buttons:
                        if buttons_clicked >= max_buttons:
                            break
                            
                        try:
                            # Verificar se o botão está visível e clicável
                            if button.is_displayed() and button.is_enabled():
                                # Usar JavaScript para clicar (mais rápido e confiável)
                                self.driver.execute_script("arguments[0].click();", button)
                                buttons_clicked += 1
                                logger.debug(f"Clicado em botão 'Show code' ({buttons_clicked}/{max_buttons})")
                                time.sleep(0.2)  # Reduzir delay entre cliques
                        except Exception as e:
                            logger.debug(f"Erro ao clicar em botão específico: {e}")
                            continue
                except Exception as e:
                    logger.debug(f"Erro com selector {selector}: {e}")
                    continue
            
            if buttons_clicked > 0:
                logger.debug(f"✅ {buttons_clicked} botões 'Show code' clicados com sucesso")
                # Aguardar menos tempo para o código aparecer
                time.sleep(0.5)
            else:
                logger.debug("Nenhum botão Show codeencontrado ou clicado")
                
        except Exception as e:
            logger.warning(f"Não foi possível clicar nos botões 'Show Code': {e}")
    
    def save_components(self, filename: str = "storybook_components.json") -> str:
        """Salva componentes extraídos em JSON."""
        if not self.components:
            logger.warning("Nenhum componente para salvar.")
            return ""
        
        output_path = self.config.output_dir / filename
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.components, f, indent=2, ensure_ascii=False)
            logger.info(f"Componentes salvos em: {output_path}")
            logger.info(f"Total de componentes salvos: {len(self.components)}")
        except Exception as e:
            logger.error(f"Erro ao salvar componentes: {e}")
            return ""
        
        return str(output_path)
    
    def save_categorized_components(self) -> Dict[str, str]:
        """Salva componentes categorizados por tipo de parser."""
        if not self.components:
            logger.warning("Nenhum componente para salvar.")
            return {}
        
        categorized_files = {}
        
        # Agrupar componentes por tipo de parser
        parser_types = {
            "components": {},
            "templates": {},
            "services": {},
            "classes": {},
            "main_docs": {}
        }
        
        for component_id, component_data in self.components.items():
            component_type = component_data.get("component_type", "component")
            
            # Mapear tipos para categorias de parser
            if component_type == "main_docs":
                parser_types["main_docs"][component_id] = component_data
            elif component_type == "template":
                parser_types["templates"][component_id] = component_data
            elif component_type == "class":
                parser_types["classes"][component_id] = component_data
            elif component_data.get("type") == "images_item":
                # Agrupar por categoria de Images
                category = component_data.get("category", "unknown")
                if category != "unknown":
                    images_key = f"images_{category}"
                    if images_key not in parser_types:
                        parser_types[images_key] = {}
                    parser_types[images_key][component_id] = component_data
            else:
                # Verificar se é service baseado no ID
                if "service" in component_id.lower() or "services" in component_id.lower():
                    parser_types["services"][component_id] = component_data
                else:
                    parser_types["components"][component_id] = component_data
        
        # Salvar cada tipo de parser em arquivo separado
        for parser_type, components in parser_types.items():
            if not components:
                continue
                
            filename = f"{parser_type}_components.json"
            output_path = self.config.output_dir / filename
            
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(components, f, indent=2, ensure_ascii=False)
                categorized_files[parser_type] = str(output_path)
                logger.info(f"Componentes do tipo '{parser_type}' salvos em: {output_path} ({len(components)} componentes)")
            except Exception as e:
                logger.error(f"Erro ao salvar componentes do tipo '{parser_type}': {e}")
        
        # Criar índice estruturado
        index_data = self._generate_structured_index(parser_types)
        
        index_path = self.config.output_dir / "storybook_index.json"
        try:
            with open(index_path, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, indent=2, ensure_ascii=False)
            categorized_files["index"] = str(index_path)
            logger.info(f"Índice estruturado salvo em: {index_path}")
        except Exception as e:
            logger.error(f"Erro ao salvar índice estruturado: {e}")
        
        return categorized_files
    
    def _generate_structured_index(self, parser_types: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Gera índice estruturado dos componentes."""
        index = {
            "metadata": {
                "total_components": len(self.components),
                "categories": {},
                "generated_at": datetime.now().isoformat()
            },
            "components_by_category": {},
            "component_details": {}
        }
        
        # Construir índice por categoria
        for parser_type, components in parser_types.items():
            if components:
                index["metadata"]["categories"][parser_type] = len(components)
                index["components_by_category"][parser_type] = list(components.keys())
                
                # Detalhes dos componentes
                for component_id, component_data in components.items():
                    # Extrair nome do componente
                    component_name = component_data.get('name', '')
                    if not component_name:
                        component_name = component_data.get('component_name', '')
                    if not component_name:
                        # Fallback: extrair nome do ID do componente
                        component_name = self._extract_name_from_component_id(component_id)
                    
                    # Gerar nome do arquivo
                    filename = self._generate_filename_from_name(component_name, component_id)
                    
                    # Para Images, usar o título da categoria
                    if component_data.get("type") == "images_item":
                        category = component_data.get("category", "unknown")
                        component_name = f"{category.capitalize()} ({component_data.get('total_items', 0)} itens)"
                        filename = f"{category}.md"
                    
                    # Verificar se tem descrição válida
                    description = component_data.get('description', '')
                    if not description or description.lower() in ['no preview', 'sorry, but you either have no stories or none are selected somehow']:
                        description = f"Documentação de {component_name}"
                    
                    index["component_details"][component_id] = {
                        "name": component_name,
                        "filename": filename,
                        "category": parser_type,
                        "description": description,
                        "file": f"{parser_type}_components.json"
                    }
        
        return index
    
    def _generate_filename_from_name(self, name: str, component_id: str) -> str:
        """Gera nome de arquivo baseado no nome do componente."""
        if not name:
            return component_id
        
        # Limpar nome para uso em arquivo
        filename = name.lower().replace(" ", "-").replace("_", "-")
        filename = "".join(c for c in filename if c.isalnum() or c == "-")
        
        # Remover duplicatas de hífens
        while "--" in filename:
            filename = filename.replace("--", "-")
        
        # Remover hífens no início e fim
        filename = filename.strip("-")
        
        return filename or component_id
    
    def _extract_name_from_component_id(self, component_id: str) -> str:
        """Extrai um nome legível do ID do componente."""
        if not component_id:
            return "Unknown"
        
        # Remover prefixos comuns
        clean_id = component_id
        prefixes_to_remove = [
            'designsystem-components-',
            'designsystem-services-',
            'designsystem-templates-',
            'utilities-classes-',
            'images-',
            'webcomponent',
            'html'
        ]
        
        for prefix in prefixes_to_remove:
            clean_id = clean_id.replace(prefix, '')
        
        # Remover hífens e converter para título
        words = clean_id.split('-')
        if words:
            # Converter primeira palavra para título
            first_word = words[0].capitalize()
            
            # Se há mais palavras, adicionar como subtítulo
            if len(words) > 1:
                subtitle = ' '.join(word.capitalize() for word in words[1:])
                return f"{first_word} {subtitle}"
            else:
                return first_word
        
        return "Unknown"
    
    def get_extracted_components(self) -> Dict[str, Dict[str, Any]]:
        """
        Retorna os componentes extraídos.
        
        Returns:
            Dicionário com componentes extraídos
        """
        return self.components.copy()
    
    def generate_report(self) -> Dict[str, Any]:
        """Gera relatório da extração."""
        total_components = len(self.components)
        categories = {}
        
        for component in self.components.values():
            category = component.get("category", "Unknown")
            categories[category] = categories.get(category, 0) + 1
        
        return {
            "total_components": total_components,
            "categories": categories,
            "extraction_time": time.time(),
            "urls_visited": len(self.visited_urls)
        }
    
    def extract_markdown_components(self, component_urls: Dict[str, Any], limit: Optional[int] = None) -> Dict[str, str]:
        """
        Extrai componentes do Storybook e converte para Markdown usando a nova estrutura.
        
        Args:
            component_urls: URLs dos componentes a serem extraídos
            limit: Número máximo de componentes a extrair
            
        Returns:
            Dicionário com mapeamento de ID para arquivo Markdown
        """
        logger.info("Iniciando extração de componentes para Markdown...")
        
        try:
            # Aplicar limite se especificado
            if limit:
                component_urls = dict(list(component_urls.items())[:limit])
            
            # Criar estrutura de diretórios por categoria
            base_dir = self.config.output_dir
            categories = ["components", "templates", "services", "classes", "images"]
            
            for category in categories:
                category_dir = base_dir / category
                category_dir.mkdir(parents=True, exist_ok=True)
            
            markdown_files = {}
            
            # Processar componentes e categorizar
            categorized_components = {}
            
            for i, (component_id, url_data) in enumerate(component_urls.items(), 1):
                try:
                    # Determinar categoria do componente
                    category = self._determine_component_category(component_id, url_data)
                    
                    if category not in categorized_components:
                        categorized_components[category] = []
                    
                    # Extrair conteúdo markdown
                    if isinstance(url_data, dict) and url_data.get("type") == "images_item":
                        # Processar imagens
                        markdown_content = self._extract_images_markdown(component_id, url_data, i, len(component_urls))
                    else:
                        # Processar componente normal
                        markdown_content = self._extract_component_markdown(component_id, url_data, i, len(component_urls))
                    
                    if markdown_content:
                        # Salvar arquivo Markdown na pasta correta
                        filename = self._generate_filename_from_name(component_id, component_id)
                        category_dir = base_dir / category
                        filepath = category_dir / f"{filename}.md"
                        
                        with open(filepath, 'w', encoding='utf-8') as f:
                            f.write(markdown_content)
                        
                        markdown_files[component_id] = str(filepath)
                        categorized_components[category].append({
                            'id': component_id,
                            'filepath': str(filepath),
                            'filename': f"{filename}.md"
                        })
                        
                        logger.info(f"✅ Markdown gerado ({i}/{len(component_urls)}): {category}/{filename}.md")
                    
                    time.sleep(self.config.wait_time)
                    
                except Exception as e:
                    logger.error(f"❌ Erro ao processar {component_id}: {e}")
                    continue
            
            # Gerar índice estruturado
            self._generate_markdown_index(categorized_components, base_dir)
            
            # Gerar documentação principal
            self._generate_main_documentation(categorized_components, base_dir)
            
            logger.info(f"✅ Extração Markdown concluída: {len(markdown_files)} arquivos gerados")
            return markdown_files
            
        except Exception as e:
            logger.error(f"❌ Erro durante extração Markdown: {e}", exc_info=True)
            raise
    
    def _categorize_by_prefix(self, component_id: str) -> str:
        comp_id_lower = component_id.lower()
        if comp_id_lower.startswith("designsystem-components-"):
            return "components"
        elif comp_id_lower.startswith("designsystem-services-"):
            return "services"
        elif comp_id_lower.startswith("designsystem-templates-"):
            return "templates"
        elif comp_id_lower.startswith("images-"):
            return "images"
        elif comp_id_lower.startswith("utilities-classes-"):
            return "classes"
        else:
            return "components"

    def _determine_component_category(self, component_id: str, url_data: Dict[str, Any]) -> str:
        # Se for images_item, prioriza images
        if isinstance(url_data, dict) and url_data.get("type") == "images_item":
            return "images"
        return self._categorize_by_prefix(component_id)
    
    def _generate_markdown_index(self, categorized_components: Dict[str, List[Dict[str, Any]]], base_dir: Path):
        """
        Gera índice JSON estruturado dos componentes markdown.
        
        Args:
            categorized_components: Componentes categorizados
            base_dir: Diretório base
        """
        index_data = {
            "metadata": {
                "total_components": sum(len(comps) for comps in categorized_components.values()),
                "categories": {cat: len(comps) for cat, comps in categorized_components.items()},
                "generated_at": datetime.now().isoformat()
            },
            "components": {}
        }
        
        for category, components in categorized_components.items():
            for comp_info in components:
                comp_id = comp_info["id"]
                filename = comp_info["filename"]
                filepath = comp_info["filepath"]
                
                # Extrair descrição real do componente
                description = self._extract_component_description(filepath)
                
                # Detalhes do componente
                index_data["components"][comp_id] = {
                    "name": self._extract_name_from_component_id(comp_id),
                    "filename": filename,
                    "category": category,
                    "file": filepath,
                    "description": description
                }
        
        # Salvar índice
        index_file = base_dir / "storybook_index.json"
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Índice JSON gerado: {index_file}")
    
    def _extract_component_description(self, filepath: str) -> str:
        """Extrai a primeira linha de descrição do arquivo markdown."""
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Dividir em linhas
            lines = content.split('\n')
            
            # Procurar pela primeira linha de descrição após o título
            for i, line in enumerate(lines):
                line = line.strip()
                # Pular linhas vazias e títulos
                if not line or line.startswith('#'):
                    continue
                # Retornar a primeira linha de conteúdo que não seja título
                if line and not line.startswith('---') and not line.startswith('*'):
                    return line
            
            # Fallback: descrição padrão
            return "Documentação do componente"
            
        except Exception as e:
            logger.debug(f"Erro ao extrair descrição de {filepath}: {e}")
            return "Documentação do componente"
    
    def _generate_main_documentation(self, categorized_components: Dict[str, List[Dict[str, Any]]], base_dir: Path):
        """
        Gera documentação principal do design system extraindo o conteúdo real.
        
        Args:
            categorized_components: Componentes categorizados
            base_dir: Diretório base
        """
        # Tentar extrair documentação principal se disponível
        main_docs_content = self._extract_main_documentation_content()
        
        if main_docs_content:
            # Salvar documentação principal real como "main_documentation.md"
            main_docs_file = base_dir / "main_documentation.md"
            with open(main_docs_file, 'w', encoding='utf-8') as f:
                f.write(main_docs_content)
            logger.info(f"✅ Documentação principal extraída: {main_docs_file}")
            
            # Também gerar o índice como "storybook_index.md" para compatibilidade
            self._generate_main_docs_index(categorized_components, base_dir, "storybook_index.md")
        else:
            # Fallback: gerar índice como "storybook_index.md"
            self._generate_main_docs_index(categorized_components, base_dir, "storybook_index.md")
    
    def _extract_main_documentation_content(self) -> Optional[str]:
        """Extrai o conteúdo real da documentação principal do Storybook."""
        if not hasattr(self, 'main_docs_url') or not self.main_docs_url:
            logger.debug("URL da documentação principal não encontrada")
            return None
        
        try:
            logger.info(f"Extraindo documentação principal de: {self.main_docs_url}")
            
            # Navegar para a página
            self.driver.get(self.main_docs_url)
            
            # Aguardar carregamento
            iframe_selector = "#storybook-preview-iframe"
            WebDriverWait(self.driver, self.config.timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, iframe_selector))
            )
            
            WebDriverWait(self.driver, self.config.timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.sbdocs-content"))
            )
            time.sleep(1)
            
            # Extrair HTML
            raw_html = self.driver.page_source
            
            # Converter para Markdown usando o parser
            markdown_content = self.markdown_extractor.parse_to_markdown(raw_html, "Design System Documentation")
            
            if markdown_content and "Conteúdo não encontrado" not in markdown_content:
                return markdown_content
            else:
                logger.warning("Conteúdo da documentação principal não encontrado")
                return None
                
        except Exception as e:
            logger.error(f"Erro ao extrair documentação principal: {e}")
            return None
    
    def _generate_main_docs_index(self, categorized_components: Dict[str, List[Dict[str, Any]]], base_dir: Path, filename: str = "storybook_index.md"):
        """
        Gera índice como fallback quando não consegue extrair documentação principal.
        
        Args:
            categorized_components: Componentes categorizados
            base_dir: Diretório base
            filename: Nome do arquivo a ser gerado
        """
        total_components = sum(len(comps) for comps in categorized_components.values())
        
        docs = f"""# Design System Documentation\n\nDocumentação completa do Design System extraída do Storybook.\n\n## 📊 Estatísticas\n\n- **Total de componentes:** {total_components}\n- **Categorias:** {len(categorized_components)}\n- **Gerado em:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n## 📁 Estrutura de Arquivos\n\n```\n{base_dir.name}/\n├── storybook_index.json          # Índice principal estruturado\n├── main_documentation.md         # Documentação oficial (se extraída)\n├── storybook_index.md            # Esta documentação (fallback)\n├── components/                   # Componentes principais\n├── templates/                    # Templates e layouts\n├── services/                     # Serviços e utilitários\n├── classes/                      # Classes CSS utilitárias\n└── images/                       # Ícones, logos e imagens\n```\n\n## 🎯 Categorias\n\n"""
        
        for category, components in categorized_components.items():
            if components:
                docs += f"### {category.title()}\n"
                docs += f"- **Quantidade:** {len(components)} componentes\n\n"
                
                for comp_info in components:
                    comp_id = comp_info["id"]
                    filename = comp_info["filename"]
                    name = self._extract_name_from_component_id(comp_id)
                    docs += f"- [{name}]({category}/{filename})\n"
                
                docs += "\n"
        
        docs += f"""\n---\n*Documentação gerada automaticamente em {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*\n"""
        
        # Salvar documentação principal
        main_docs_file = base_dir / filename
        with open(main_docs_file, 'w', encoding='utf-8') as f:
            f.write(docs)
        
        logger.info(f"✅ Documentação principal (índice) gerada: {main_docs_file}")
    
    def _extract_component_markdown(self, component_id: str, url: str, current: int, total: int) -> Optional[str]:
        """Extrai componente e converte para Markdown."""
        if url in self.visited_urls:
            return None
        
        self.visited_urls.add(url)
        
        try:
            # Navegar para a página
            self.driver.get(url)
            
            # Aguardar carregamento
            iframe_selector = "#storybook-preview-iframe"
            WebDriverWait(self.driver, self.config.timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, iframe_selector))
            )
            
            WebDriverWait(self.driver, self.config.timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.sbdocs-content"))
            )
            time.sleep(1)
            
            # Clicar em botões "Show code"
            self._click_show_code_buttons()
            
            # Extrair HTML
            raw_html = self.driver.page_source
            
            # Determinar tipo de componente para usar parser específico
            component_type = self._determine_component_type(component_id, url)
            
            # Usar parser específico para classes
            if component_type == 'class':
                try:
                    parsed_data = self.classes_markdown_parser.parse_classes_component(raw_html, component_id)
                    markdown_content = self._classes_data_to_markdown(parsed_data)
                except Exception as e:
                    logger.error(f"Erro no parser de classes: {e}")
                    # Fallback para parser genérico
                    markdown_content = self.markdown_extractor.parse_to_markdown(raw_html, component_id)
            else:
                # Usar extrator de Markdown genérico
                try:
                    markdown_content = self.markdown_extractor.parse_to_markdown(raw_html, component_id)
                except AttributeError as e:
                    logger.error(f"Erro de atributo no BaseParser: {e}")
                    logger.error(f"Métodos disponíveis: {[m for m in dir(self.markdown_extractor) if not m.startswith('_')]}")
                    return None
                except Exception as e:
                    logger.error(f"Erro inesperado no parse_to_markdown: {e}")
                    return None
            
            return markdown_content
            
        except Exception as e:
            logger.error(f"Erro ao extrair Markdown para {component_id}: {e}")
            logger.error(f"Tipo de erro: {type(e).__name__}")
            logger.error(f"Detalhes do erro: {str(e)}")
            return None
        finally:
            try:
                self.driver.switch_to.default_content()
            except:
                pass
    
    def _extract_images_markdown(self, component_id: str, url_data: Dict[str, Any], current: int, total: int) -> Optional[str]:
        """Extrai imagens e converte para Markdown usando ImagesParser específico."""
        url = url_data["url"]
        category = url_data["category"]
        
        try:
            # Navegar para a página
            self.driver.get(url)
            
            # Aguardar carregamento
            iframe_selector = "#storybook-preview-iframe"
            WebDriverWait(self.driver, self.config.timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.CSS_SELECTOR, iframe_selector))
            )
            
            WebDriverWait(self.driver, self.config.timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.sbdocs-content"))
            )
            time.sleep(1)
            
            # Clicar em botões "Show code"
            self._click_show_code_buttons()
            
            # Extrair dados estruturados usando ImagesParser específico
            images_data = self.images_parser.parse_images_category(
                self.driver, url, category, self.config.wait_time, self.config.timeout
            )
            
            if not images_data:
                logger.warning(f"Falha na extração estruturada de imagens {component_id}")
                return None
            
            # Converter dados estruturados para markdown
            markdown_content = self._images_data_to_markdown(images_data)
            
            return markdown_content
            
        except Exception as e:
            logger.error(f"Erro ao extrair Markdown para imagens {component_id}: {e}")
            return None
        finally:
            try:
                self.driver.switch_to.default_content()
            except:
                pass
    
    def _images_data_to_markdown(self, images_data: Dict[str, Any]) -> str:
        """Converte dados estruturados de imagens para markdown."""
        lines = []
        
        # Título
        title = images_data.get('title', 'Images')
        lines.append(f"# {title}")
        lines.append("")
        
        # Descrição
        description = images_data.get('description', '')
        if description:
            lines.append(description)
            lines.append("")
        
        # Seções de documentação (todas as seções com headings)
        usage_instructions = images_data.get('usage_instructions', [])
        if usage_instructions:
            for section in usage_instructions:
                if isinstance(section, dict):
                    title = section.get('title', 'Seção')
                    content = section.get('content', '')
                    
                    # Pular seções muito curtas ou vazias
                    if len(content.strip()) < 10:
                        continue
                    
                    lines.append(f"## {title}")
                    lines.append("")
                    lines.append(content)
                    lines.append("")
        
        # Exemplos de código
        code_examples = images_data.get('code_examples', [])
        if code_examples:
            lines.append("## Exemplos de Código")
            lines.append("")
            for example in code_examples:
                if isinstance(example, dict):
                    title = example.get('title', 'Exemplo')
                    code = example.get('code', '')
                    language = example.get('language', 'html')
                    
                    lines.append(f"### {title}")
                    lines.append(f"```{language}")
                    lines.append(code)
                    lines.append("```")
                    lines.append("")
        
        # Tabela de itens
        items_table = images_data.get('items_table', [])
        if items_table:
            lines.append("## Itens")
            lines.append("")
            
            # Separar itens de animação das tabelas de documentação
            animation_items = [item for item in items_table if item.get('type') != 'options_table' and item.get('type') != 'methods_table']
            documentation_tables = [item for item in items_table if item.get('type') in ['options_table', 'methods_table']]
            
            # Determinar colunas baseado na categoria
            category = images_data.get('category', '')
            if category == 'animation':
                if animation_items:
                    lines.append("### Animações")
                    lines.append("")
                    lines.append("| Nome | Exemplo de Uso |")
                    lines.append("| --- | --- |")
                    for item in animation_items:
                        name = item.get('name', '')
                        html_code = item.get('html_code', '')
                        lines.append(f"| {name} | `{html_code}` |")
                    lines.append("")
                
                # Adicionar tabelas de documentação
                if documentation_tables:
                    lines.append("### Documentação")
                    lines.append("")
                    for table in documentation_tables:
                        table_name = table.get('name', '')
                        table_content = table.get('html_code', '')
                        
                        lines.append(f"#### {table_name}")
                        lines.append("")
                        lines.append(table_content)
                        lines.append("")
                        
            elif category in ['icons', 'logos']:
                lines.append("| Nome | Classe | Exemplo de Uso |")
                lines.append("| --- | --- | --- |")
                for item in items_table:
                    name = item.get('name', '')
                    class_name = item.get('class', '')
                    html_code = item.get('html_code', '')
                    lines.append(f"| {name} | `{class_name}` | `{html_code}` |")
            elif category in ['flags', 'illustration']:
                lines.append("| Nome | Classe | Exemplo de Uso |")
                lines.append("| --- | --- | --- |")
                for item in items_table:
                    name = item.get('name', '')
                    class_name = item.get('class', '')
                    html_code = item.get('html_code', '')
                    lines.append(f"| {name} | `{html_code}` |")
            else:
                # Formato genérico
                lines.append("| Nome | Classe | Exemplo de Uso |")
                lines.append("| --- | --- | --- |")
                for item in items_table:
                    name = item.get('name', '')
                    class_name = item.get('class', '')
                    html_code = item.get('html_code', '')
                    lines.append(f"| {name} | `{class_name}` | `{html_code}` |")
            
            lines.append("")
        
        # Estatísticas
        total_items = images_data.get('total', 0)
        if total_items > 0:
            lines.append(f"**Total de itens:** {total_items}")
            lines.append("")
        
        # Metadados
        metadata = images_data.get('metadata', {})
        if metadata:
            lines.append("---")
            lines.append(f"*Extraído automaticamente do Storybook em {metadata.get('extracted_at', 'N/A')}*")
        
        return "\n".join(lines)
    
    def _classes_data_to_markdown(self, parsed_data: Dict[str, Any]) -> str:
        """Converte dados do parser de classes para markdown."""
        lines = []
        
        # Título
        component_name = parsed_data.get('component_name', 'Classes')
        lines.append(f"# {component_name}")
        lines.append("")
        
        # Descrição
        description = parsed_data.get('description', '')
        if description:
            lines.append(description)
            lines.append("")
        
        # Tipo de classe
        class_type = parsed_data.get('class_type', 'generic')
        lines.append(f"**Tipo de Classe:** {class_type}")
        lines.append("")
        
        # Seções com títulos (h2-h5) e suas tabelas
        sections = parsed_data.get('sections', [])
        if sections:
            for section in sections:
                title = section.get('title', '')
                content = section.get('content', '')
                tables = section.get('tables', [])
                
                if title:
                    # Determinar nível do heading baseado no level
                    level = section.get('level', 'h3')
                    heading_level = int(level[1]) if level.startswith('h') else 3
                    heading_markdown = '#' * heading_level + ' ' + title
                    lines.append(heading_markdown)
                    lines.append("")
                
                if content:
                    lines.append(content)
                    lines.append("")
                
                # Adicionar tabelas da seção
                for table in tables:
                    lines.append(table)
                    lines.append("")
        
        # Exemplos
        examples = parsed_data.get('examples', [])
        if examples:
            lines.append("## Exemplos")
            lines.append("")
            for i, example in enumerate(examples, 1):
                name = example.get('name', f'Exemplo {i}')
                class_name = example.get('class', '')
                html_code = example.get('html_code', '')
                
                lines.append(f"### {name}")
                if class_name:
                    lines.append(f"**Classe:** `{class_name}`")
                    lines.append("")
                if html_code:
                    lines.append("```html")
                    lines.append(html_code)
                    lines.append("```")
                lines.append("")
        
        # Código de exemplo
        code_examples = parsed_data.get('code_examples', [])
        if code_examples:
            lines.append("## Código de Exemplo")
            lines.append("")
            for i, code_ex in enumerate(code_examples, 1):
                title = code_ex.get('title', f'Código {i}')
                code = code_ex.get('code', '')
                language = code_ex.get('language', 'html')
                
                lines.append(f"### {title}")
                lines.append(f"```{language}")
                lines.append(code)
                lines.append("```")
                lines.append("")
        
        # Tabela de propriedades
        properties_table = parsed_data.get('properties_table', {})
        if properties_table:
            lines.append("## Propriedades")
            lines.append("")
            for prop_name, prop_info in properties_table.items():
                if isinstance(prop_info, dict):
                    description = prop_info.get('description', '')
                    default = prop_info.get('default', '')
                    lines.append(f"- **{prop_name}** - {description}")
                    if default:
                        lines.append(f"  - Padrão: `{default}`")
                else:
                    lines.append(f"- **{prop_name}** - {prop_info}")
            lines.append("")
        
        # Total de exemplos
        total_examples = parsed_data.get('total_examples', 0)
        if total_examples > 0:
            lines.append(f"**Total de exemplos:** {total_examples}")
            lines.append("")
        
        return "\n".join(lines)
    
    def _setup_driver(self) -> None:
        """Configura o driver do Selenium."""
        options = Options()
        if self.config.headless:
            options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1920,1080")
        
        self.driver = webdriver.Chrome(options=options)
        self.driver.implicitly_wait(10)
        logger.debug("Driver do Selenium configurado")
    
    def _cleanup(self) -> None:
        """Limpa recursos do extrator."""
        if self.driver:
            self.driver.quit()
            logger.debug("Driver do Selenium encerrado") 