# -*- coding: utf-8 -*-
"""
Scraper para leitura e abertura de menus do Storybook.

Este scraper é responsável apenas por:
- Navegar para o Storybook
- Expandir menus
- Extrair URLs dos componentes
- Filtrar componentes que devem ser utilizados
"""

import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import re

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

from src.design_system.utils.storybook_utils import (
    extract_component_name_from_url
)

logger = logging.getLogger(__name__)

@dataclass
class StorybookConfig:
    """Configuração para o scraper do Storybook."""
    base_url: str
    output_dir: Path
    headless: bool = True
    timeout: int = 30
    wait_time: int = 2
    max_retries: int = 3

class StorybookScraper:
    """
    Scraper focado apenas na leitura e abertura de menus do Storybook.
    """
    
    def __init__(self, config: StorybookConfig):
        """Inicializa o scraper do Storybook."""
        self.config = config
        self.driver = None
        self.visited_urls = set()
        self.main_docs_url = None
        
        # Flags de debug
        self.debug_urls = False
        self.skip_menu_expansion = False
        
        self.config.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("Storybook Scraper inicializado")
    
    def __enter__(self):
        self._setup_driver()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self._cleanup()
    
    def extract_component_urls(self, limit: Optional[int] = None, category_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Extrai URLs dos componentes do Storybook.
        
        Args:
            limit: Número máximo de componentes a serem extraídos
            category_filter: Filtro por categoria específica
            
        Returns:
            Dicionário com URLs dos componentes organizados por tipo e main_docs_url
        """
        logger.info("Iniciando extração de URLs dos componentes...")
        
        try:
            self._navigate_to_main_page()
            component_urls = self._extract_component_urls()
            
            # Extrair documentação principal se encontrada
            if self.main_docs_url:
                logger.info(f"Documentação principal encontrada: {self.main_docs_url}")
            
            # Priorizar URLs
            prioritized_urls = self._prioritize_urls(component_urls)
            
            # Aplicar filtros
            if category_filter:
                prioritized_urls = self._apply_category_filter(prioritized_urls, category_filter)
            
            if limit:
                prioritized_urls = dict(list(prioritized_urls.items())[:limit])
            
            # Retornar URLs dos componentes junto com a URL da documentação principal
            result = {
                "component_urls": prioritized_urls,
                "main_docs_url": self.main_docs_url
            }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erro durante extração de URLs: {e}", exc_info=True)
            raise
    
    def _navigate_to_main_page(self) -> None:
        """Navega para a página principal do Storybook."""
        logger.debug(f"Navegando para: {self.config.base_url}")
        self.driver.get(self.config.base_url)
        
        # Aguardar carregamento da página
        WebDriverWait(self.driver, self.config.timeout).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "#storybook-explorer-tree"))
        )
        time.sleep(2)
    
    def _extract_component_urls(self) -> Dict[str, Any]:
        """Extrai URLs dos componentes do Storybook."""
        logger.debug("Extraindo URLs dos componentes...")
        
        component_urls = {}
        
        # Lista de componentes que devem ser excluídos
        excluded_components = [
            "design-system-liquid-bradesco",  # Documentação principal
            "design-system-liquid-bradesco--docs",  # Documentação principal
            "release-log-changelog",  # Release log/changelog (sem --docs)
            "release-log-changelog--docs",  # Release log/changelog (com --docs)
            "support",  # Support docs (sem --docs)
            "support--docs"  # Support docs (com --docs)
        ]
        
        try:
            # Aguardar carregamento da sidebar
            sidebar = WebDriverWait(self.driver, self.config.timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "#storybook-explorer-tree"))
            )
            
            time.sleep(2)
            
            # Pular expansão de menus se solicitado
            if not self.skip_menu_expansion:
                self._expand_all_menus(sidebar)
                time.sleep(3)
            else:
                logger.info("⚠️ Pulando expansão de menus (modo debug)")
            
            # Estratégia única: Procurar por links de documentação com --docs
            docs_links = sidebar.find_elements(By.CSS_SELECTOR, "a[href*='--docs']")
            logger.debug(f"Encontrados {len(docs_links)} links com '--docs'")
            
            # Processar links com --docs
            total_links_found = len(docs_links)
            links_processed = 0
            links_added = 0
            links_excluded = 0
            links_errors = 0
            
            logger.info(f"Processando {total_links_found} links encontrados...")
            
            for i, link in enumerate(docs_links):
                href = link.get_attribute("href")
                if self.debug_urls:
                    logger.debug(f"Link {i+1}/{total_links_found}: {href}")
                else:
                    logger.debug(f"Link {i+1}/{total_links_found}: {href}")
                links_processed += 1
                
                if href:
                    try:
                        # Verificar se é documentação principal
                        if "design-system-liquid-bradesco--docs" in href:
                            # Salvar URL da documentação principal para extração separada
                            self.main_docs_url = href
                            links_excluded += 1
                            logger.debug(f"Documentação principal identificada: {href}")
                            continue
                        
                        # Verificar se é item de Images
                        if "images-" in href and href.endswith("--docs"):
                            # Extrair categoria do URL (ex: images-animation--docs -> animation)
                            category = self._extract_images_category_from_url(href)
                            if category:
                                component_name = f"images_{category}"
                                component_urls[component_name] = {
                                    "url": href,
                                    "category": category,
                                    "type": "images_item"
                                }
                                links_added += 1
                                logger.debug(f"Adicionado item de Images: {component_name}")
                        else:
                            # Componente normal
                            component_name = extract_component_name_from_url(href)
                            if component_name:
                                # Verificar se o componente deve ser excluído
                                should_exclude = any(excluded in component_name.lower() for excluded in excluded_components)
                                if not should_exclude:
                                    # Aplicar regra de escolha entre HTML e Web Component
                                    processed_name = self._apply_html_webcomponent_choice(component_name, href, component_urls)
                                    if processed_name:
                                        component_urls[processed_name] = href
                                        links_added += 1
                                        logger.debug(f"Adicionado componente: {processed_name}")
                                    else:
                                        links_excluded += 1
                                        logger.debug(f"Componente ignorado por regra HTML/WebComponent: {component_name}")
                                else:
                                    links_excluded += 1
                                    logger.info(f"✅ Excluído componente: {component_name} (URL: {href})")
                            else:
                                links_errors += 1
                                logger.debug(f"Não foi possível extrair nome do componente de: {href}")
                    except Exception as e:
                        links_errors += 1
                        logger.warning(f"Erro ao processar link {i+1}: {e}")
                        continue
                else:
                    links_errors += 1
                    logger.debug(f"Link {i+1} não possui href válido")
            
            logger.debug("Processamento de links concluído:")
            logger.debug(f"  - Links encontrados: {total_links_found}")
            logger.debug(f"  - Links processados: {links_processed}")
            logger.debug(f"  - Links adicionados: {links_added}")
            logger.debug(f"  - Links excluídos: {links_excluded}")
            logger.debug(f"  - Links com erro: {links_errors}")
            logger.debug(f"  - Total de URLs para extração: {len(component_urls)}")
            
            # Mostrar URLs encontradas se debug ativado
            if self.debug_urls:
                logger.debug("URLs encontradas:")
                for component_id, url_data in component_urls.items():
                    if isinstance(url_data, dict):
                        logger.debug(f"  - {component_id}: {url_data['url']} (Images)")
                    else:
                        logger.debug(f"  - {component_id}: {url_data}")
            
            if links_added < total_links_found:
                logger.info(f"Nota: {total_links_found - links_added} links não foram incluídos na extração (excluídos por filtro ou erro)")
            return component_urls
            
        except Exception as e:
            logger.error(f"Erro ao extrair URLs: {e}")
            return {}
    
    def _extract_images_category_from_url(self, url: str) -> Optional[str]:
        """Extrai categoria de Images do URL de forma mais robusta."""
        # Padrão: images-{category}--docs
        # Exemplo: images-animation--docs -> animation
        pattern = r'images-([^-]+)--docs'
        match = re.search(pattern, url)
        
        if match:
            category = match.group(1)
            # Validar se é uma categoria conhecida
            valid_categories = ["animation", "flags", "icons", "illustration", "logos"]
            if category in valid_categories:
                return category
        
        return None
    
    def _expand_all_menus(self, sidebar) -> None:
        """Expande todos os menus do Storybook de forma otimizada."""
        if not self.driver:
            logger.error("Driver não inicializado")
            return
        
        try:
            logger.info("Iniciando expansão completa dos menus do Storybook...")
            max_iterations = 8  # Reduzir iterações máximas
            total_buttons_clicked = 0
            
            for iteration in range(max_iterations):
                # Buscar todos os botões fechados
                buttons_to_expand = sidebar.find_elements(By.CSS_SELECTOR, "button[aria-expanded='false']")
                
                if not buttons_to_expand:
                    logger.info(f"✅ Todos os menus expandidos após {iteration} iterações")
                    break
                
                logger.info(f"📂 Iteração {iteration + 1}: expandindo {len(buttons_to_expand)} botões...")
                
                # Clicar em cada botão encontrado de forma otimizada
                buttons_clicked_this_iteration = 0
                for i, button in enumerate(buttons_to_expand):
                    try:
                        if button.is_displayed() and button.is_enabled():
                            # Clicar diretamente sem scroll (mais rápido)
                            self.driver.execute_script("arguments[0].click();", button)
                            time.sleep(0.1)  # Reduzir tempo de espera
                            
                            buttons_clicked_this_iteration += 1
                            
                            # Mostrar progresso a cada 10 botões
                            if buttons_clicked_this_iteration % 10 == 0:
                                logger.debug(f"   Progresso: {buttons_clicked_this_iteration}/{len(buttons_to_expand)} botões expandidos")
                    except Exception as e:
                        logger.debug(f"Erro ao expandir botão {i+1}: {e}")
                        continue
                
                total_buttons_clicked += buttons_clicked_this_iteration
                logger.debug(f"✅ Iteração {iteration + 1} concluída: {buttons_clicked_this_iteration} botões clicados")
                
                # Aguardar menos tempo
                time.sleep(0.5)
                
                # Verificar se não houve progresso
                if buttons_clicked_this_iteration == 0:
                    logger.info("Nenhum botão foi clicado nesta iteração. Parando expansão.")
                    break
            
            logger.debug(f"✅ Expansão de menus concluída: {total_buttons_clicked} botões clicados em {max_iterations} iterações")
            
        except Exception as e:
            logger.error(f"Erro durante expansão de menus: {e}")
    
    def _prioritize_urls(self, urls: Dict[str, Any]) -> Dict[str, Any]:
        """Prioriza URLs baseado em categorias importantes."""
        if not urls:
            return {}
        
        # Categorias prioritárias (em ordem de prioridade)
        priority_categories = [
            "components",  # Componentes principais
            "templates",   # Templates
            "services",    # Serviços
            "classes",     # Classes/utilities
            "images"       # Images (menor prioridade)
        ]
        
        prioritized = {}
        
        # Primeiro, adicionar componentes por categoria de prioridade
        for category in priority_categories:
            for component_id, url_data in urls.items():
                if component_id not in prioritized:
                    # Verificar se é da categoria atual
                    if category == "images" and isinstance(url_data, dict) and url_data.get("type") == "images_item":
                        prioritized[component_id] = url_data
                    elif category != "images" and not isinstance(url_data, dict):
                        # Verificar categoria do componente normal baseado no ID
                        component_category = self._determine_category_from_id(component_id)
                        if component_category == category:
                            prioritized[component_id] = url_data
        
        # Adicionar componentes restantes
        for component_id, url_data in urls.items():
            if component_id not in prioritized:
                prioritized[component_id] = url_data
        
        logger.info(f"URLs priorizadas: {len(prioritized)} componentes")
        return prioritized
    
    def _apply_html_webcomponent_choice(self, component_name: str, href: str, existing_urls: Dict[str, Any]) -> Optional[str]:
        """
        Aplica regra de escolha entre HTML e Web Component.
        Se há ambos os tipos, escolhe Web Component.
        
        Args:
            component_name: Nome do componente extraído
            href: URL do componente
            existing_urls: URLs já processadas
            
        Returns:
            Nome do componente a ser usado ou None se deve ser ignorado
        """
        # Verificar se é HTML ou Web Component
        is_html = "html" in href.lower()
        is_webcomponent = "webcomponent" in href.lower()
        
        logger.debug(f"Analisando componente: {component_name} (HTML: {is_html}, WebComponent: {is_webcomponent})")
        
        # Se não é nem HTML nem Web Component, manter como está
        if not is_html and not is_webcomponent:
            logger.debug(f"Componente não é HTML nem WebComponent, mantendo: {component_name}")
            return component_name
        
        # Remover sufixos para obter nome base
        base_name = component_name.replace("-html", "").replace("-webcomponent", "")
        logger.debug(f"Nome base do componente: {base_name}")
        
        # Verificar se já existe um componente com o mesmo nome base
        existing_html = None
        existing_webcomponent = None
        
        for existing_name, existing_href in existing_urls.items():
            existing_base = existing_name.replace("-html", "").replace("-webcomponent", "")
            if existing_base == base_name:
                if "html" in str(existing_href).lower():
                    existing_html = existing_name
                    logger.debug(f"Encontrado HTML existente: {existing_name}")
                elif "webcomponent" in str(existing_href).lower():
                    existing_webcomponent = existing_name
                    logger.debug(f"Encontrado WebComponent existente: {existing_name}")
        
        # Aplicar regras de escolha
        if is_html:
            if existing_webcomponent:
                # Já existe Web Component, ignorar HTML
                logger.info(f"✅ Ignorando HTML '{component_name}' pois já existe Web Component '{existing_webcomponent}'")
                return None
            else:
                # Não existe Web Component, manter HTML
                logger.debug(f"Mantendo HTML '{component_name}' (não há WebComponent)")
                return component_name
        
        elif is_webcomponent:
            if existing_html:
                # Já existe HTML, substituir por Web Component
                logger.info(f"✅ Substituindo HTML '{existing_html}' por Web Component '{component_name}'")
                # Remover HTML existente
                if existing_html in existing_urls:
                    del existing_urls[existing_html]
                return component_name
            else:
                # Não existe HTML, manter Web Component
                logger.debug(f"Mantendo WebComponent '{component_name}' (não há HTML)")
                return component_name
        
        return component_name
    
    def _determine_category_from_id(self, component_id: str) -> str:
        """
        Determina a categoria do componente baseado no ID.
        
        Args:
            component_id: ID do componente
            
        Returns:
            Categoria do componente
        """
        comp_id_lower = component_id.lower()
        
        # Mapeamento baseado em padrões do ID
        if any(keyword in comp_id_lower for keyword in ["template", "layout"]):
            return "templates"
        elif any(keyword in comp_id_lower for keyword in ["service", "utility", "toggle", "scroll"]):
            return "services"
        elif any(keyword in comp_id_lower for keyword in ["class", "color", "border", "spacing", "typography"]):
            return "classes"
        elif any(keyword in comp_id_lower for keyword in ["icon", "logo", "flag", "animation", "illustration"]):
            return "images"
        else:
            return "components"
    
    def _apply_category_filter(self, urls: Dict[str, Any], category_filter: str) -> Dict[str, Any]:
        """Aplica filtro por categoria específica."""
        if not urls:
            return {}
        
        filtered_urls = {}
        
        for component_id, url_data in urls.items():
            if isinstance(url_data, dict) and url_data.get("type") == "images_item":
                # Para items de Images, verificar se a categoria corresponde
                if url_data.get("category") == category_filter:
                    filtered_urls[component_id] = url_data
            else:
                # Para componentes normais, verificar categoria
                component_category = self._determine_category_from_id(component_id)
                if component_category == category_filter:
                    filtered_urls[component_id] = url_data
        
        logger.info(f"Filtro aplicado para categoria '{category_filter}': {len(filtered_urls)} componentes")
        return filtered_urls
    
    def _setup_driver(self) -> None:
        """Configura o driver do Selenium."""
        options = Options()
        if self.config.headless:
            options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1920,1080")
        
        self.driver = webdriver.Chrome(options=options)
        self.driver.implicitly_wait(10)
        logger.debug("Driver do Selenium configurado")
    
    def _cleanup(self) -> None:
        """Limpa recursos do scraper."""
        if self.driver:
            self.driver.quit()
            logger.debug("Driver do Selenium encerrado") 