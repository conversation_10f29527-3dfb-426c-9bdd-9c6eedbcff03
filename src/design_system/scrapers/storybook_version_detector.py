import requests
import re
import logging
from typing import Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)


class StorybookVersionDetector:
    """
    Detecta automaticamente a versão do Storybook.
    """
    
    def __init__(self, base_url: str):
        """
        Inicializa o detector.
        
        Args:
            base_url: URL base do CDN do Storybook
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        })
    
    def detect_latest_version(self) -> Optional[str]:
        """
        Detecta a versão mais recente do Storybook.
        
        Returns:
            Versão mais recente ou None se não conseguir detectar
        """
        logger.info("Detectando versão mais recente do Storybook")
        
        try:
            # Método 1: Tentar acessar página principal
            latest_version = self._try_main_page()
            if latest_version:
                logger.debug(f"Versão detectada via página principal: {latest_version}")
                return latest_version
            
            # Método 2: Tentar listar diretórios
            latest_version = self._try_directory_listing()
            if latest_version:
                logger.debug(f"Versão detectada via listagem de diretório: {latest_version}")
                return latest_version
            
            # Método 3: Tentar padrões conhecidos
            latest_version = self._try_known_patterns()
            if latest_version:
                logger.debug(f"Versão detectada via padrões conhecidos: {latest_version}")
                return latest_version
            
            logger.warning("Não foi possível detectar a versão automaticamente")
            return None
            
        except Exception as e:
            logger.error(f"Erro ao detectar versão: {e}")
            return None
    
    def _try_main_page(self) -> Optional[str]:
        """Tenta detectar versão acessando a página principal."""
        try:
            # Tentar acessar a URL base
            response = self.session.get(self.base_url, timeout=10)
            response.raise_for_status()
            
            # Procurar por redirecionamentos ou links de versão
            content = response.text
            
            # Padrões para encontrar versão
            patterns = [
                r'storybook-(\d+\.\d+\.\d+)',
                r'version["\"]?\s*:\s*["\"]?(\d+\.\d+\.\d+)',
                r'storybook@(\d+\.\d+\.\d+)',
                r'v(\d+\.\d+\.\d+)'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    # Retornar a versão mais recente
                    versions = [self._parse_version(v) for v in matches]
                    latest = max(versions)
                    return f"{latest[0]}.{latest[1]}.{latest[2]}"
            
            return None
            
        except Exception as e:
            logger.debug(f"Erro ao tentar página principal: {e}")
            return None
    
    def _try_directory_listing(self) -> Optional[str]:
        """Tenta detectar versão listando diretórios."""
        try:
            # Tentar acessar diretório pai para listar versões
            parent_url = str(Path(self.base_url).parent)
            response = self.session.get(parent_url, timeout=10)
            
            if response.status_code == 200:
                content = response.text
                
                # Procurar por links de versão
                version_pattern = r'href=["\"]?storybook-(\d+\.\d+\.\d+)["\"]?'
                matches = re.findall(version_pattern, content)
                
                if matches:
                    versions = [self._parse_version(v) for v in matches]
                    latest = max(versions)
                    return f"{latest[0]}.{latest[1]}.{latest[2]}"
            
            return None
            
        except Exception as e:
            logger.debug(f"Erro ao tentar listagem de diretório: {e}")
            return None
    
    def _try_known_patterns(self) -> Optional[str]:
        """Tenta detectar versão usando padrões conhecidos."""
        try:
            # Tentar versões recentes conhecidas
            recent_versions = [
                "1.33.4", "1.31.7"
            ]
            
            for version in recent_versions:
                test_url = f"{self.base_url}/storybook-{version}/"
                try:
                    response = self.session.head(test_url, timeout=5)
                    if response.status_code == 200:
                        logger.debug(f"Versão local {version} encontrada e acessível")
                        return version
                except:
                    continue
            
            return None
            
        except Exception as e:
            logger.debug(f"Erro ao tentar padrões conhecidos: {e}")
            return None
    
    def _parse_version(self, version_str: str) -> tuple:
        """Converte string de versão para tupla para comparação."""
        parts = version_str.split('.')
        return tuple(int(part) for part in parts[:3])
    
    def get_storybook_url(self, version: Optional[str] = None) -> str:
        """
        Obtém URL completa do Storybook.
        
        Args:
            version: Versão específica (None para detectar automaticamente)
            
        Returns:
            URL completa do Storybook
        """
        if not version or version == "latest":
            detected_version = self.detect_latest_version()
            if detected_version:
                version = detected_version
            else:
                # Fallback para versão padrão
                version = "1.31.7"
                logger.warning(f"Usando versão padrão: {version}")
        
        return f"{self.base_url}/storybook-{version}/"
    
    def validate_version(self, version: str) -> bool:
        """
        Valida se uma versão específica está disponível.
        
        Args:
            version: Versão a validar
            
        Returns:
            True se a versão estiver disponível
        """
        try:
            test_url = f"{self.base_url}/storybook-{version}/"
            response = self.session.head(test_url, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def get_available_versions(self) -> List[str]:
        """
        Lista todas as versões disponíveis.
        
        Returns:
            Lista de versões disponíveis
        """
        versions = []
        
        try:
            # Tentar listar diretórios
            parent_url = str(Path(self.base_url).parent)
            response = self.session.get(parent_url, timeout=10)
            
            if response.status_code == 200:
                content = response.text
                version_pattern = r'href=["\"]?storybook-(\d+\.\d+\.\d+)["\"]?'
                matches = re.findall(version_pattern, content)
                versions = list(set(matches))  # Remover duplicatas
                
                # Ordenar por versão
                versions.sort(key=lambda v: self._parse_version(v), reverse=True)
            
        except Exception as e:
            logger.warning(f"Erro ao listar versões: {e}")
        
        return versions 