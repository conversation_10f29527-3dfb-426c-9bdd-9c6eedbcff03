"""
Integração inteligente com design system usando main_docs e ai_context.
Faz matching entre padrões detectados e componentes do design system.
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from src.utils.config import Config<PERSON>oader

@dataclass
class DesignSystemMatch:
    """Representa um match entre padrão detectado e componente do design system."""
    component_name: str
    confidence: float
    code_examples: Dict[str, str]  # language -> code
    css_classes: List[str]
    documentation: str
    usage_patterns: List[str]

class DesignSystemIntegration:
    """
    Integra padrões detectados do Figma com componentes reais do design system.
    """
    
    def __init__(self, design_system_path: str):
        self.design_system_path = Path(design_system_path)
        self.main_docs = self._load_main_docs()
        self.ai_context = self._load_ai_context()
        self.component_registry = self._build_component_registry()
        
    def _load_main_docs(self) -> Dict[str, Any]:
        """Carrega main_docs.json com definições gerais."""
        main_docs_file = self.design_system_path / "main_docs.json"
        if main_docs_file.exists():
            with open(main_docs_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def _load_ai_context(self) -> Dict[str, Dict[str, Any]]:
        """Carrega contextos de IA dos componentes."""
        ai_context = {}
        ai_context_dir = self.design_system_path / "ai_context"
        
        if ai_context_dir.exists():
            for md_file in ai_context_dir.glob("*.md"):
                component_name = md_file.stem
                try:
                    content = md_file.read_text(encoding='utf-8')
                    ai_context[component_name] = self._parse_component_context(content)
                except Exception:
                    # Ignorar arquivos com linhas muito longas ou outros problemas
                    ai_context[component_name] = {"content": "", "examples": [], "classes": []}
        
        return ai_context
    
    def _parse_component_context(self, content: str) -> Dict[str, Any]:
        """Parse do conteúdo markdown do componente."""
        # Extrair exemplos de código
        code_examples = []
        code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', content, re.DOTALL)
        for lang, code in code_blocks:
            if code.strip():
                code_examples.append({
                    'language': lang or 'html',
                    'code': code.strip()
                })
        
        # Extrair classes CSS (brad-*)
        css_classes = re.findall(r'brad-[\w-]+', content)
        css_classes = list(set(css_classes))  # Remover duplicatas
        
        # Extrair texto principal (primeira parte antes dos exemplos)
        main_text = content.split('```')[0] if '```' in content else content
        main_text = main_text.strip()[:1000]  # Limitar tamanho
        
        return {
            "content": main_text,
            "examples": code_examples,
            "classes": css_classes
        }
    
    def _build_component_registry(self) -> Dict[str, Dict[str, Any]]:
        """Constrói registro de componentes com informações consolidadas."""
        registry = {}
        
        for component_name, context in self.ai_context.items():
            # Determinar tipo de componente
            component_type = self._classify_component_type(component_name, context)
            
            # Extrair padrões de uso
            usage_patterns = self._extract_usage_patterns(context)
            
            registry[component_name] = {
                'type': component_type,
                'context': context,
                'usage_patterns': usage_patterns,
                'css_classes': context.get('classes', []),
                'examples': context.get('examples', [])
            }
        
        return registry
    
    def _classify_component_type(self, component_name: str, context: Dict[str, Any]) -> str:
        """Classifica o tipo do componente baseado no nome e contexto."""
        name_lower = component_name.lower()
        
        if 'table' in name_lower:
            return 'table'
        elif any(nav_word in name_lower for nav_word in ['tab', 'nav', 'menu', 'breadcrumb']):
            return 'navigation'
        elif any(form_word in name_lower for form_word in ['form', 'input', 'textfield', 'select', 'checkbox', 'radio']):
            return 'form'
        elif any(btn_word in name_lower for btn_word in ['button', 'btn']):
            return 'button'
        elif any(card_word in name_lower for card_word in ['card', 'modal', 'dialog']):
            return 'card'
        elif any(list_word in name_lower for list_word in ['list', 'item']):
            return 'list'
        else:
            return 'generic'
    
    def _extract_usage_patterns(self, context: Dict[str, Any]) -> List[str]:
        """Extrai padrões de uso do contexto."""
        patterns = []
        
        # Analisar exemplos de código para identificar padrões
        for example in context.get('examples', []):
            code = example.get('code', '')
            
            # Padrões de inicialização de serviços
            service_patterns = re.findall(r'LiquidCorp\.(\w+)\.getInstance', code)
            patterns.extend([f"service:{pattern}" for pattern in service_patterns])
            
            # Padrões de classes CSS
            class_patterns = re.findall(r'class="([^"]*brad[^"]*)"', code)
            patterns.extend([f"css:{pattern}" for pattern in class_patterns])
            
            # Padrões de estrutura HTML
            if '<table' in code:
                patterns.append('structure:table')
            elif '<nav' in code or 'nav-' in code:
                patterns.append('structure:navigation')
            elif '<form' in code or 'form-' in code:
                patterns.append('structure:form')
        
        return list(set(patterns))  # Remover duplicatas
    
    def find_best_match(self, detected_pattern_type: str, real_data: Dict[str, Any]) -> Optional[DesignSystemMatch]:
        """
        Encontra o melhor match no design system para o padrão detectado.
        """
        candidates = []
        
        # Buscar componentes do mesmo tipo
        for component_name, component_info in self.component_registry.items():
            if component_info['type'] == detected_pattern_type:
                confidence = self._calculate_match_confidence(
                    detected_pattern_type, real_data, component_info
                )
                if confidence > 0.3:  # Threshold mínimo
                    candidates.append((component_name, component_info, confidence))
        
        if not candidates:
            # Buscar matches genéricos se não encontrou específicos
            for component_name, component_info in self.component_registry.items():
                if detected_pattern_type in component_name.lower():
                    confidence = 0.5  # Confidence moderada para match por nome
                    candidates.append((component_name, component_info, confidence))
        
        if not candidates:
            return None
        
        # Ordenar por confidence e retornar o melhor
        candidates.sort(key=lambda x: x[2], reverse=True)
        best_component_name, best_component_info, best_confidence = candidates[0]
        
        return self._create_design_system_match(
            best_component_name, best_component_info, best_confidence
        )
    
    def _calculate_match_confidence(self, 
                                  pattern_type: str, 
                                  real_data: Dict[str, Any], 
                                  component_info: Dict[str, Any]) -> float:
        """Calcula confidence do match baseado em múltiplos fatores."""
        confidence = 0.0
        
        # 1. Match de tipo base
        if component_info['type'] == pattern_type:
            confidence += 0.4
        
        # 2. Análise de dados reais vs padrões do componente
        if pattern_type == 'table':
            confidence += self._analyze_table_match(real_data, component_info)
        elif pattern_type == 'navigation':
            confidence += self._analyze_navigation_match(real_data, component_info)
        
        # 3. Disponibilidade de exemplos
        if component_info.get('examples'):
            confidence += 0.1
        
        # 4. Qualidade da documentação
        content_length = len(component_info.get('context', {}).get('content', ''))
        if content_length > 100:
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _analyze_table_match(self, real_data: Dict[str, Any], component_info: Dict[str, Any]) -> float:
        """Analisa match específico para tabelas."""
        score = 0.0
        
        # Verificar se tem classes de tabela
        css_classes = component_info.get('css_classes', [])
        if any('table' in cls for cls in css_classes):
            score += 0.2
        
        # Verificar exemplos com estrutura de tabela
        for example in component_info.get('examples', []):
            code = example.get('code', '')
            if '<table' in code or 'brad-table' in code:
                score += 0.2
                break
        
        return score
    
    def _analyze_navigation_match(self, real_data: Dict[str, Any], component_info: Dict[str, Any]) -> float:
        """Analisa match específico para navegação."""
        score = 0.0
        
        # Verificar classes de navegação
        css_classes = component_info.get('css_classes', [])
        if any(nav_word in cls for cls in css_classes for nav_word in ['nav', 'tab', 'menu']):
            score += 0.2
        
        # Verificar número de itens de navegação
        texts = real_data.get('texts', [])
        nav_items = len([t for t in texts if len(t.get('content', '').split()) <= 2])
        if 2 <= nav_items <= 10:  # Número razoável de itens de navegação
            score += 0.1
        
        return score
    
    def _create_design_system_match(self, 
                                  component_name: str, 
                                  component_info: Dict[str, Any], 
                                  confidence: float) -> DesignSystemMatch:
        """Cria objeto DesignSystemMatch com todas as informações."""
        
        # Extrair exemplos de código por linguagem
        code_examples = {}
        for example in component_info.get('examples', []):
            lang = example.get('language', 'html')
            code = example.get('code', '')
            if code:
                code_examples[lang] = code
        
        # Se não tem exemplos, usar padrões do main_docs
        if not code_examples:
            code_examples = self._get_fallback_examples(component_info['type'])
        
        return DesignSystemMatch(
            component_name=component_name,
            confidence=confidence,
            code_examples=code_examples,
            css_classes=component_info.get('css_classes', []),
            documentation=component_info.get('context', {}).get('content', ''),
            usage_patterns=component_info.get('usage_patterns', [])
        )
    
    def _get_fallback_examples(self, component_type: str) -> Dict[str, str]:
        """Retorna exemplos de fallback do main_docs."""
        fallback_examples = {}
        
        # Usar exemplos gerais do main_docs
        main_docs_examples = self.main_docs.get('code_examples', {})
        
        if 'general_html' in main_docs_examples:
            fallback_examples['html'] = main_docs_examples['general_html']['content']
        
        if 'general_typescript' in main_docs_examples:
            fallback_examples['typescript'] = main_docs_examples['general_typescript']['content']
        
        return fallback_examples
    
    def get_base_framework_setup(self) -> Dict[str, str]:
        """Retorna setup base do framework (CSS/JS includes, etc)."""
        setup = {}
        
        main_docs_examples = self.main_docs.get('code_examples', {})
        
        # HTML base setup
        if 'html_integration_html' in main_docs_examples:
            setup['html_base'] = main_docs_examples['html_integration_html']['content']
        
        # Angular setup
        if 'general_typescript' in main_docs_examples:
            setup['angular_base'] = main_docs_examples['general_typescript']['content']
        
        # CSS themes
        themes = []
        for section in self.main_docs.get('sections', {}).values():
            content = ' '.join(section.get('content', []))
            theme_matches = re.findall(r'brad-theme-[\w-]+', content)
            themes.extend(theme_matches)
        
        setup['available_themes'] = list(set(themes))
        
        return setup

def main():
    """Teste da integração com design system."""
    
    print("🔗 TESTE DE INTEGRAÇÃO COM DESIGN SYSTEM")
    print("=" * 60)
    
    # Inicializar integração
    # Gerar path a partir de configurações
    config = ConfigLoader("project_config.yaml").load_config()
    design_system_path = config.get("design_system", {}).get("path", "data/design_system")
    ds_slug = config.get("design_system", {}).get("slug", "")
    integration = DesignSystemIntegration(f"{design_system_path}/{ds_slug}")
    
    print(f"📚 Componentes registrados: {len(integration.component_registry)}")
    print(f"📖 Main docs carregado: {'✅' if integration.main_docs else '❌'}")
    
    # Testar match para tabela
    print("\n🔍 TESTANDO MATCH PARA TABELA:")
    table_data = {
        'texts': [
            {'content': 'Diretor'},
            {'content': 'Início Vigência'},
            {'content': 'Nome Diretor'},
            {'content': '00/00/0000'}
        ]
    }
    
    table_match = integration.find_best_match('table', table_data)
    if table_match:
        print(f"✅ Match encontrado: {table_match.component_name}")
        print(f"   Confidence: {table_match.confidence:.2f}")
        print(f"   Classes CSS: {table_match.css_classes[:3]}")  # Primeiras 3
        print(f"   Exemplos: {list(table_match.code_examples.keys())}")
    else:
        print("❌ Nenhum match encontrado para tabela")
    
    # Testar setup base
    print("\n⚙️  SETUP BASE DO FRAMEWORK:")
    setup = integration.get_base_framework_setup()
    print(f"   HTML base: {'✅' if 'html_base' in setup else '❌'}")
    print(f"   Angular base: {'✅' if 'angular_base' in setup else '❌'}")
    print(f"   Temas disponíveis: {len(setup.get('available_themes', []))}")

if __name__ == "__main__":
    main()
