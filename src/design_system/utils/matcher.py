"""
Matcher para conectar componentes Figma aos componentes do Storybook.
Usa técnicas de correspondência semântica e IA quando disponível.
"""

from typing import Dict, List, Any
import re
from difflib import SequenceMatcher
import logging

logger = logging.getLogger(__name__)

class Matcher:
    """Matcher para componentes Figma e Storybook."""
    
    def __init__(self):
        # Mapeamentos semânticos pré-definidos
        self.semantic_mappings = {
            "button icon": ["fab", "button", "icon-button", "btn-icon"],
            "table": ["table", "data-table", "grid"],
            "table header": ["table-header", "header", "th"],
            "table cell": ["table-cell", "cell", "td"],
            "pagination": ["pagination", "pager", "page-nav"],
            "form": ["form", "input", "field"],
            "card": ["card", "panel", "container"],
            "modal": ["modal", "dialog", "popup"],
            "dropdown": ["dropdown", "select", "combobox"],
            "nav": ["navigation", "nav", "menu"],
            "list": ["list", "ul", "ol", "item-list"]
        }
        
        # Palavras-chave para diferentes tipos de ações
        self.action_keywords = {
            "edit": ["edit", "editar", "modify", "update"],
            "delete": ["delete", "excluir", "remove", "trash"],
            "add": ["add", "adicionar", "create", "new", "novo"],
            "save": ["save", "salvar", "submit"],
            "cancel": ["cancel", "cancelar", "close"],
            "view": ["view", "ver", "show", "details"]
        }
    
    def find_best_matches(self, figma_component: Dict[str, Any], 
                         storybook_context: Dict[str, Any],
                         max_matches: int = 3) -> List[Dict[str, Any]]:
        """
        Encontra as melhores correspondências entre um componente Figma e o Storybook.
        """
        matches = []
        
        # Extrair informações do componente Figma
        figma_info = self._extract_figma_info(figma_component)
        
        # Procurar correspondências em todos os projetos do Storybook
        for project_name, ai_context in storybook_context.get("ai_context", {}).items():
            for component_name, component_data in ai_context.items():
                
                # Calcular pontuação de correspondência
                match_score = self._calculate_match_score(figma_info, component_name, component_data)
                
                if match_score > 0.1:  # Threshold mínimo
                    matches.append({
                        "project": project_name,
                        "component": component_name,
                        "score": match_score,
                        "match_reasons": self._get_match_reasons(figma_info, component_name, component_data),
                        "component_data": component_data
                    })
        
        # Ordenar por pontuação e retornar os melhores
        matches.sort(key=lambda x: x["score"], reverse=True)
        return matches[:max_matches]
    
    def _extract_figma_info(self, figma_component: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai informações relevantes do componente Figma para matching."""
        info = {
            "name": figma_component.get("name", "").lower(),
            "type": figma_component.get("component_type", "").lower(),
            "category": figma_component.get("category", "").lower(),
            "detected_type": "",
            "interactive_elements": [],
            "keywords": set()
        }
        
        # Extrair informações da análise estrutural se disponível
        structural_analysis = figma_component.get("structural_analysis", {})
        if structural_analysis:
            info["detected_type"] = structural_analysis.get("component_type", "").lower()
            
            # Extrair elementos interativos
            for elem in structural_analysis.get("interaction_elements", []):
                info["interactive_elements"].append({
                    "type": elem.get("type", ""),
                    "action": elem.get("action_type", ""),
                    "name": elem.get("name", "").lower()
                })
        
        # Extrair palavras-chave do nome
        info["keywords"] = self._extract_keywords(info["name"])
        
        return info
    
    def _extract_keywords(self, text: str) -> set:
        """Extrai palavras-chave relevantes de um texto."""
        # Normalizar texto
        text = re.sub(r'[^\w\s-]', ' ', text.lower())
        words = re.split(r'[\s\-_]+', text)
        
        # Filtrar palavras relevantes
        keywords = set()
        for word in words:
            if len(word) > 2 and word not in ['the', 'and', 'for', 'com', 'que']:
                keywords.add(word)
        
        return keywords
    
    def _calculate_match_score(self, figma_info: Dict[str, Any], 
                              component_name: str, 
                              component_data: Dict[str, Any]) -> float:
        """Calcula pontuação de correspondência entre Figma e Storybook."""
        score = 0.0
        component_name_lower = component_name.lower()
        
        # 1. Match exato de nome (peso alto)
        if figma_info["name"] == component_name_lower:
            score += 1.0
        
        # 2. Similaridade de string usando SequenceMatcher
        similarity = SequenceMatcher(None, figma_info["name"], component_name_lower).ratio()
        score += similarity * 0.8
        
        # 3. Match de palavras-chave
        component_keywords = self._extract_keywords(component_name)
        keyword_overlap = len(figma_info["keywords"].intersection(component_keywords))
        if keyword_overlap > 0:
            score += keyword_overlap * 0.3
        
        # 4. Match semântico usando mapeamentos pré-definidos
        semantic_score = self._calculate_semantic_score(figma_info, component_name_lower)
        score += semantic_score * 0.6
        
        # 5. Match por tipo detectado
        if figma_info["detected_type"]:
            if figma_info["detected_type"] in component_name_lower:
                score += 0.4
        
        # 6. Match por elementos interativos (limitado para evitar over-scoring)
        if figma_info["interactive_elements"]:
            interactive_score = self._calculate_interactive_score(
                figma_info["interactive_elements"], component_name_lower, component_data
            )
            score += min(interactive_score, 0.5) * 0.3  # Limitar impacto
        
        # 7. Boost para componentes específicos conhecidos
        score += self._apply_specific_boosts(figma_info, component_name_lower)
        
        return min(score, 1.0)  # Normalizar para máximo 1.0
    
    def _calculate_semantic_score(self, figma_info: Dict[str, Any], component_name: str) -> float:
        """Calcula pontuação baseada em mapeamentos semânticos."""
        score = 0.0
        
        for figma_concept, storybook_variants in self.semantic_mappings.items():
            # Verificar se o conceito está no Figma
            if any(word in figma_info["name"] for word in figma_concept.split()):
                # Verificar se alguma variante está no componente Storybook
                for variant in storybook_variants:
                    if variant in component_name:
                        score += 0.8
                        break
        
        return score
    
    def _calculate_interactive_score(self, interactive_elements: List[Dict[str, Any]], 
                                   component_name: str, 
                                   component_data: Dict[str, Any]) -> float:
        """Calcula pontuação baseada em elementos interativos (limitada para evitar over-scoring)."""
        score = 0.0
        unique_types = set()
        unique_actions = set()
        
        # Contar tipos únicos em vez de todos os elementos
        for element in interactive_elements:
            element_type = element.get("type", "")
            action_type = element.get("action", "")
            
            if element_type:
                unique_types.add(element_type)
            if action_type:
                unique_actions.add(action_type)
        
        # Score baseado em tipos únicos de elementos
        for element_type in unique_types:
            if element_type in component_name:
                score += 0.2  # Reduzido de 0.3
        
        # Score baseado em tipos únicos de ações
        for action_type in unique_actions:
            if action_type in self.action_keywords:
                for keyword in self.action_keywords[action_type]:
                    if keyword in component_name:
                        score += 0.3  # Reduzido de 0.4
                        break
        
        return min(score, 1.0)  # Máximo 1.0
    
    def _apply_specific_boosts(self, figma_info: Dict[str, Any], component_name: str) -> float:
        """Aplica boosts específicos para casos conhecidos."""
        boost = 0.0
        
        # PRIORIDADE ALTA: Componentes de tabela detectados estruturalmente
        if figma_info.get("detected_type") == "table":
            # Boost EXTREMO para componentes de tabela específicos
            if any(keyword in component_name.lower() for keyword in ["table", "data-table"]):
                boost += 5.0  # Boost extremo para tabelas
            elif any(keyword in component_name.lower() for keyword in ["grid", "list"]):
                boost += 3.0  # Boost alto para listas/grids
            
            # Penalidade SEVERA para componentes inadequados
            if any(keyword in component_name.lower() for keyword in ["card", "modal", "dialog", "alert", "button"]):
                boost -= 5.0  # Penalidade severa
            elif any(keyword in component_name.lower() for keyword in ["utilities", "classes", "form"]):
                boost -= 2.0  # Penalidade moderada
        
        # Boost para Button Icon -> FabIcon
        if "button" in figma_info["name"] and "icon" in figma_info["name"]:
            if "fab" in component_name or "icon" in component_name or "button" in component_name:
                boost += 0.8
        
        # Boost para Pagination
        if figma_info.get("detected_type") == "navigation" or "pagination" in figma_info["name"]:
            if any(keyword in component_name.lower() for keyword in ["pagination", "pager", "bullets", "nav"]):
                boost += 1.0
        
        # Boost para Form
        if figma_info.get("detected_type") == "form":
            if any(keyword in component_name.lower() for keyword in ["form", "input", "field"]):
                boost += 0.8
        
        # Penalizar componentes genéricos quando tipo específico foi detectado
        detected_type = figma_info.get("detected_type", "")
        if detected_type in ["table", "form", "navigation"]:
            if any(keyword in component_name.lower() for keyword in ["utilities", "classes", "general"]):
                boost -= 0.5
        
        return boost
    
    def _get_match_reasons(self, figma_info: Dict[str, Any], 
                          component_name: str, 
                          component_data: Dict[str, Any]) -> List[str]:
        """Gera lista de razões para o match."""
        reasons = []
        component_name_lower = component_name.lower()
        
        # Similaridade de nome
        similarity = SequenceMatcher(None, figma_info["name"], component_name_lower).ratio()
        if similarity > 0.7:
            reasons.append(f"Nome similar ({similarity:.1%})")
        
        # Palavras-chave em comum
        component_keywords = self._extract_keywords(component_name)
        common_keywords = figma_info["keywords"].intersection(component_keywords)
        if common_keywords:
            reasons.append(f"Palavras-chave: {', '.join(common_keywords)}")
        
        # Tipo detectado
        if figma_info["detected_type"] and figma_info["detected_type"] in component_name_lower:
            reasons.append(f"Tipo: {figma_info['detected_type']}")
        
        # Elementos interativos
        for element in figma_info["interactive_elements"]:
            if element["type"] in component_name_lower:
                reasons.append(f"Elemento interativo: {element['type']}")
        
        # Mapeamentos semânticos
        for figma_concept, storybook_variants in self.semantic_mappings.items():
            if any(word in figma_info["name"] for word in figma_concept.split()):
                for variant in storybook_variants:
                    if variant in component_name_lower:
                        reasons.append(f"Mapeamento semântico: {figma_concept} -> {variant}")
        
        return reasons
    
    def get_detailed_match_report(self, figma_component: Dict[str, Any], 
                                 storybook_context: Dict[str, Any]) -> Dict[str, Any]:
        """Gera relatório detalhado de correspondências."""
        matches = self.find_best_matches(figma_component, storybook_context, max_matches=5)
        
        figma_info = self._extract_figma_info(figma_component)
        
        report = {
            "figma_component": {
                "name": figma_component.get("name", ""),
                "detected_type": figma_info.get("detected_type", ""),
                "keywords": list(figma_info.get("keywords", [])),
                "interactive_elements": len(figma_info.get("interactive_elements", []))
            },
            "matches": matches,
            "total_storybook_components": sum(
                len(ai_context) for ai_context in storybook_context.get("ai_context", {}).values()
            ),
            "recommendation": self._generate_recommendation(matches)
        }
        
        return report
    
    def _generate_recommendation(self, matches: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Gera recomendação baseada nos matches encontrados."""
        if not matches:
            return {
                "status": "no_match",
                "message": "Nenhuma correspondência encontrada",
                "suggestion": "Verificar se o componente existe no Storybook ou criar novo"
            }
        
        best_match = matches[0]
        score = best_match["score"]
        
        if score >= 0.8:
            status = "excellent"
            message = f"Excelente correspondência encontrada: {best_match['component']}"
        elif score >= 0.6:
            status = "good"
            message = f"Boa correspondência encontrada: {best_match['component']}"
        elif score >= 0.4:
            status = "moderate"
            message = f"Correspondência moderada: {best_match['component']}"
        else:
            status = "weak"
            message = f"Correspondência fraca: {best_match['component']}"
        
        return {
            "status": status,
            "message": message,
            "confidence": score,
            "recommended_component": f"{best_match['project']}:{best_match['component']}",
            "match_reasons": best_match["match_reasons"]
        }
