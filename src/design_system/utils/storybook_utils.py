#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utilitários para extração do Storybook.

Este módulo contém funções auxiliares para o scraper do Storybook,
incluindo funções para extração de itens do bloco Images e outras
funcionalidades específicas.
"""

import re
import time
import logging
from typing import Dict, Any, Optional, List
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logger = logging.getLogger(__name__)


def extract_component_name_from_url(url: str) -> Optional[str]:
    """
    Extrai o nome do componente da URL.
    
    Args:
        url: URL do componente
        
    Returns:
        Nome do componente extraído ou None
    """
    try:
        # Extrair a parte da URL após /docs/ ou /story/
        if "/docs/" in url:
            component_part = url.split("/docs/")[-1]
        elif "/story/" in url:
            component_part = url.split("/story/")[-1]
        else:
            return None
        
        # Remover --docs ou --story do final
        if component_part.endswith("--docs"):
            component_part = component_part[:-6]
        elif component_part.endswith("--story"):
            component_part = component_part[:-7]
        
        # Remover parâmetros de query se houver
        if "?" in component_part:
            component_part = component_part.split("?")[0]
        
        return component_part if component_part else None
    except Exception as e:
        logger.warning(f"Erro ao extrair nome do componente de {url}: {e}")
        return None


def extract_images_items(driver, url: str, category: str, wait_time: int = 2, timeout: int = 30) -> Dict[str, Any]:
    """
    Extrai itens do bloco Images (Icons, Illustration, etc.) que têm estrutura especial.
    
    Args:
        driver: Selenium WebDriver
        url: URL da página de exemplos
        category: Categoria (icons, illustration, etc.)
        wait_time: Tempo de espera entre ações
        timeout: Timeout para carregamento da página
        
    Returns:
        Dicionário com os itens extraídos
    """
    if not driver:
        logger.error("Driver não inicializado")
        return {}
        
    try:
        logger.info(f"Navegando para página de {category}: {url}")
        driver.get(url)
        time.sleep(wait_time)
        
        # Aguardar carregamento da página
        WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "div.brad-flex.brad-flex-wrap"))
        )
        
        # Extrair informações do título
        title_element = driver.find_element(By.CSS_SELECTOR, "h1.brad-font-title-md")
        title_text = title_element.text.strip()
        logger.info(f"Título encontrado: {title_text}")
        
        # Extrair total de itens do título
        total_items = 0
        if "Total de" in title_text:
            match = re.search(r'Total de.*?(\d+)', title_text)
            if match:
                total_items = int(match.group(1))
                logger.info(f"Total de itens: {total_items}")
        
        # Encontrar todos os itens baseado na categoria
        items_container = driver.find_element(By.CSS_SELECTOR, "div.brad-flex.brad-flex-wrap")
        
        # Seletores específicos para cada categoria
        selectors = {
            "icons": "div[id^='icon-']",
            "illustration": "div[id^='brad-illustration__content--'], div[class*='brad-illustration__bg']",
            "animation": "div[id^='brad-animation-']",
            "flags": "div[id^='brad-flag-']",
            "logos": "div[id^='brad-logo__']"
        }
        
        selector = selectors.get(category, "div[id]")
        item_divs = items_container.find_elements(By.CSS_SELECTOR, selector)
        
        logger.info(f"Encontrados {len(item_divs)} itens de {category}")
        
        items = {}
        for item_div in item_divs:
            try:
                item_id = item_div.get_attribute("id")
                
                # Se não tem ID, tentar gerar um baseado na categoria e classes
                if not item_id:
                    if category == "illustration":
                        # Para backgrounds de ilustração, usar a classe como ID
                        bg_div = item_div.find_element(By.CSS_SELECTOR, "div[class*='brad-illustration__bg']")
                        classes = bg_div.get_attribute("class")
                        if classes:
                            # Extrair o nome da classe de background
                            bg_class = [cls for cls in classes.split() if "brad-illustration__bg--" in cls]
                            if bg_class:
                                item_id = bg_class[0]
                            else:
                                continue
                        else:
                            continue
                    else:
                        # Para outros tipos, pular se não tem ID
                        continue
                    
                # Extrair o nome/classes do item baseado na categoria
                if category == "icons":
                    # Para ícones, pegar o elemento <em> dentro do item
                    em_element = item_div.find_element(By.CSS_SELECTOR, "em")
                    item_classes = em_element.get_attribute("class")
                    item_name = item_id
                    
                    # Extrair o texto do parágrafo (nome do ícone)
                    p_element = item_div.find_element(By.CSS_SELECTOR, "p.brad-font-subtitle-xs")
                    display_name = p_element.text.strip()
                    
                elif category == "illustration":
                    # Para ilustrações, verificar se tem img ou div com background
                    try:
                        # Tentar pegar o elemento <img> primeiro
                        img_element = item_div.find_element(By.CSS_SELECTOR, "img")
                        item_classes = img_element.get_attribute("class")
                        item_name = item_id
                    except:
                        # Se não encontrar img, procurar por div com background
                        try:
                            bg_div = item_div.find_element(By.CSS_SELECTOR, "div[class*='brad-illustration__bg']")
                            item_classes = bg_div.get_attribute("class")
                            item_name = item_id
                        except:
                            # Se não encontrar background, usar a própria div
                            item_classes = item_div.get_attribute("class")
                            item_name = item_id
                    
                    # Extrair o texto do parágrafo (nome da ilustração)
                    p_element = item_div.find_element(By.CSS_SELECTOR, "p.brad-font-subtitle-xs")
                    display_name = p_element.text.strip()
                    
                elif category == "animation":
                    # Para animações, pegar a div interna
                    try:
                        inner_div = item_div.find_element(By.CSS_SELECTOR, "div[id^='brad-animation-']")
                        item_classes = inner_div.get_attribute("class")
                    except:
                        # Se não encontrar div interna, usar a própria div
                        item_classes = item_div.get_attribute("class")
                    
                    item_name = item_id
                    
                    # Extrair o texto do parágrafo (nome da animação)
                    p_element = item_div.find_element(By.CSS_SELECTOR, "p.brad-font-subtitle-xs")
                    display_name = p_element.text.strip()
                    
                elif category == "flags":
                    # Para flags, pegar o elemento <img>
                    img_element = item_div.find_element(By.CSS_SELECTOR, "img")
                    item_classes = img_element.get_attribute("class")
                    item_name = item_id
                    
                    # Extrair o texto do parágrafo (nome da flag)
                    p_element = item_div.find_element(By.CSS_SELECTOR, "p.brad-font-subtitle-xs")
                    display_name = p_element.text.strip()
                    
                elif category == "logos":
                    # Para logos, pegar o elemento <img>
                    img_element = item_div.find_element(By.CSS_SELECTOR, "img")
                    item_classes = img_element.get_attribute("class")
                    item_name = item_id
                    
                    # Extrair o texto do parágrafo (nome do logo)
                    p_element = item_div.find_element(By.CSS_SELECTOR, "p.brad-font-subtitle-xs")
                    display_name = p_element.text.strip()
                
                else:
                    # Para outros tipos, tentar extrair de forma genérica
                    item_classes = item_div.get_attribute("class")
                    item_name = item_id
                    display_name = item_id
                
                # Extrair código HTML copiável se disponível
                copyable_code = None
                try:
                    # Verificar se há um onclick que copia código
                    onclick = item_div.get_attribute("onclick")
                    if onclick and "clipboard.writeText" in onclick:
                        # Tentar extrair o código que seria copiado
                        if category == "icons":
                            em_element = item_div.find_element(By.CSS_SELECTOR, "em")
                            copyable_code = em_element.get_attribute("outerHTML")
                        elif category in ["illustration", "flags", "logos"]:
                            img_element = item_div.find_element(By.CSS_SELECTOR, "img")
                            copyable_code = img_element.get_attribute("outerHTML")
                        elif category == "animation":
                            inner_div = item_div.find_element(By.CSS_SELECTOR, "div[id^='brad-animation-']")
                            copyable_code = inner_div.get_attribute("outerHTML")
                except Exception as e:
                    logger.debug(f"Não foi possível extrair código copiável para {item_id}: {e}")
                
                items[item_name] = {
                    "id": item_id,
                    "name": item_name,
                    "display_name": display_name,
                    "classes": item_classes,
                    "category": category,
                    "type": "image_item",
                    "copyable_code": copyable_code
                }
                
            except Exception as e:
                logger.warning(f"Erro ao extrair item {item_div.get_attribute('id')}: {e}")
                continue
        
        return {
            "title": title_text,
            "total_items": total_items,
            "items": items,
            "category": category,
            "url": url
        }
        
    except Exception as e:
        logger.error(f"Erro ao extrair itens de {category}: {e}")
        return {}


def categorize_component(component_id: str, component_data: Dict[str, Any]) -> str:
    """
    Categoriza um componente baseado no seu ID e dados.
    
    Args:
        component_id: ID do componente
        component_data: Dados do componente
        
    Returns:
        Categoria do componente
    """
    # Verificar se é um item do bloco Images
    if component_data.get("type") == "image_item" or "images_" in component_id:
        category = component_data.get("category", "icons")
        return category
    
    # Mapeamento de categorias baseado no ID
    category_mapping = {
        'components': ['designsystem-components'],
        'services': ['designsystem-services'],
        'templates': ['designsystem-templates'],
        'animation': ['images-animation'],
        'flags': ['images-flags'],
        'icons': ['images-icons'],
        'illustration': ['images-illustration'],
        'logos': ['images-logos'],
        'classes': ['utilities-classes']
    }
    
    # Verificar categoria baseada no ID
    for category, patterns in category_mapping.items():
        for pattern in patterns:
            if pattern in component_id:
                return category
    
    # Fallback: verificar no nome do componente
    component_name = component_data.get('name', '').lower()
    if any(word in component_name for word in ['button', 'input', 'form', 'modal', 'card']):
        return 'components'
    elif any(word in component_name for word in ['service', 'toggle']):
        return 'services'
    elif any(word in component_name for word in ['template', 'layout']):
        return 'templates'
    elif any(word in component_name for word in ['icon', 'flag', 'logo', 'animation', 'illustration']):
        return 'images'
    elif any(word in component_name for word in ['class', 'utility']):
        return 'classes'
    
    # Default
    return 'components'


def filter_components_by_category(components: Dict[str, Any], target_category: Optional[str] = None) -> Dict[str, Any]:
    """
    Filtra componentes por categoria específica.
    
    Args:
        components: Dicionário de componentes
        target_category: Categoria específica para filtrar (opcional)
        
    Returns:
        Dicionário filtrado de componentes
    """
    if not target_category:
        return components
    
    filtered = {}
    for component_id, component_data in components.items():
        category = categorize_component(component_id, component_data)
        if category == target_category:
            filtered[component_id] = component_data
    
    logger.info(f"Filtrados {len(filtered)} componentes da categoria '{target_category}'")
    return filtered


def get_available_categories() -> List[str]:
    """
    Retorna lista de categorias disponíveis.
    
    Returns:
        Lista de categorias
    """
    return [
        'components',
        'services', 
        'templates',
        'animation',
        'flags',
        'icons',
        'illustration',
        'logos',
        'classes'
    ] 