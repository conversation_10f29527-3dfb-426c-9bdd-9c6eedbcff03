"""
Cliente para a API do Figma.

Este módulo é responsável por:
- Conectar com a API do Figma
- Fazer requisições autenticadas
- Baixar dados de arquivos e nodes específicos
- Gerenciar tokens de acesso
"""

import requests
import json
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin
from dataclasses import dataclass

from src.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class FigmaApiConfig:
    """Configuração para a API do Figma."""
    token: str
    base_url: str = "https://api.figma.com/v1/"
    timeout: int = 30


class FigmaApiClient:
    """Cliente para interagir com a API do Figma."""
    
    def __init__(self, config: FigmaApiConfig):
        """
        Inicializa o cliente da API.
        
        Args:
            config: Configuração da API do Figma
        """
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'X-Figma-Token': config.token,
            'User-Agent': 'figma-to-code-converter/1.0'
        })
    
    def get_file(self, file_key: str) -> Dict[str, Any]:
        """
        Obtém informações completas de um arquivo do Figma.
        
        Args:
            file_key: Chave do arquivo do Figma (extraída da URL)
            
        Returns:
            Dict com os dados do arquivo
            
        Raises:
            requests.RequestException: Se houver erro na requisição
        """
        url = urljoin(self.config.base_url, f"files/{file_key}")
        
        logger.info(f"Buscando arquivo: {file_key}")
        
        try:
            response = self.session.get(url, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Arquivo obtido com sucesso: {data.get('name', 'Sem nome')}")
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"Erro ao buscar arquivo {file_key}: {e}")
            raise
    
    def get_file_nodes(self, file_key: str, node_ids: List[str], depth: Optional[str] = None) -> Dict[str, Any]:
        """
        Obtém nodes específicos de um arquivo do Figma.
        
        Args:
            file_key: Chave do arquivo do Figma
            node_ids: Lista de IDs dos nodes a serem obtidos
            depth: (Opcional) Profundidade da árvore de nós a ser retornada.
            
        Returns:
            Dict com os dados dos nodes
            
        Raises:
            requests.RequestException: Se houver erro na requisição
        """
        url = urljoin(self.config.base_url, f"files/{file_key}/nodes")
        params = {
            'ids': ','.join(node_ids)
        }
        if depth is not None:
            params['depth'] = depth
        
        logger.debug(f"Buscando nodes: {node_ids} do arquivo: {file_key} com profundidade: {depth}")
        
        try:
            response = self.session.get(url, params=params, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            logger.debug(f"Nodes obtidos com sucesso: {len(data.get('nodes', {}))}")
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"Erro ao buscar nodes {node_ids} do arquivo {file_key}: {e}")
            raise
    
    def get_file_components(self, file_key: str) -> Dict[str, Any]:
        """
        Obtém componentes de um arquivo do Figma.
        
        Args:
            file_key: Chave do arquivo do Figma
            
        Returns:
            Dict com os componentes do arquivo
        """
        url = urljoin(self.config.base_url, f"files/{file_key}/components")
        
        logger.info(f"Buscando componentes do arquivo: {file_key}")
        
        try:
            response = self.session.get(url, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Componentes obtidos: {len(data.get('meta', {}).get('components', []))}")
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"Erro ao buscar componentes do arquivo {file_key}: {e}")
            raise
    
    def get_file_styles(self, file_key: str) -> Dict[str, Any]:
        """
        Obtém estilos de um arquivo do Figma.
        
        Args:
            file_key: Chave do arquivo do Figma
            
        Returns:
            Dict com os estilos do arquivo
        """
        url = urljoin(self.config.base_url, f"files/{file_key}/styles")
        
        logger.info(f"Buscando estilos do arquivo: {file_key}")
        
        try:
            response = self.session.get(url, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Estilos obtidos: {len(data.get('meta', {}).get('styles', []))}")
            
            return data
            
        except requests.RequestException as e:
            logger.error(f"Erro ao buscar estilos do arquivo {file_key}: {e}")
            raise
    
    def download_images(self, file_key: str, node_ids: List[str], 
                       format: str = 'png', scale: float = 1.0) -> Dict[str, str]:
        """
        Baixa imagens de nodes específicos.
        
        Args:
            file_key: Chave do arquivo do Figma
            node_ids: Lista de IDs dos nodes
            format: Formato da imagem (png, jpg, svg, pdf)
            scale: Escala da imagem (1.0 = 100%)
            
        Returns:
            Dict mapeando node_id para URL da imagem
        """
        url = urljoin(self.config.base_url, f"images/{file_key}")
        params = {
            'ids': ','.join(node_ids),
            'format': format,
            'scale': scale
        }
        
        logger.debug(f"Baixando imagens dos nodes: {node_ids}")
        logger.debug(f"Parâmetros: format={format}, scale={scale}")
        
        try:
            response = self.session.get(url, params=params, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            images = data.get('images', {})
            
            logger.debug(f"URLs de imagens obtidas: {len(images)}")
            
            # Log detalhado das imagens obtidas
            for node_id, image_url in images.items():
                if image_url:
                    logger.debug(f"✅ Imagem obtida para node {node_id}: {image_url[:50]}...")
                else:
                    logger.debug(f"⚠️ URL vazia para node {node_id}")
            
            return images
            
        except requests.RequestException as e:
            logger.error(f"Erro ao baixar imagens: {e}")
            raise
    
    @staticmethod
    def extract_file_key_from_url(figma_url: str) -> str:
        """
        Extrai a chave do arquivo de uma URL do Figma.
        
        Args:
            figma_url: URL completa do arquivo do Figma
            
        Returns:
            Chave do arquivo
            
        Example:
            URL: https://www.figma.com/file/EWVeXlNAwQlwOsej7XstyO/SAT-CONTABILIDADE
            Retorna: EWVeXlNAwQlwOsej7XstyO
        """
        # Formato típico: https://www.figma.com/file/{file_key}/{file_name}
        parts = figma_url.split('/')
        
        if 'file' in parts:
            file_index = parts.index('file')
            if file_index + 1 < len(parts):
                return parts[file_index + 1]
        
        raise ValueError(f"Não foi possível extrair a chave do arquivo da URL: {figma_url}")
    
    def save_data_to_file(self, data: Dict[str, Any], file_path: str) -> None:
        """
        Salva dados da API em um arquivo JSON.
        
        Args:
            data: Dados a serem salvos
            file_path: Caminho do arquivo de destino
        """
        logger.info(f"Salvando dados em: {file_path}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info("Dados salvos com sucesso")


def create_client_from_env() -> Optional[FigmaApiClient]:
    """
    Cria um cliente da API usando variáveis de ambiente.
    
    Returns:
        FigmaApiClient ou None se as variáveis não estiverem definidas
    """
    import os
    
    token = os.getenv('FIGMA_TOKEN')
    if not token:
        logger.warning("FIGMA_TOKEN não encontrado nas variáveis de ambiente")
        return None
    
    config = FigmaApiConfig(token=token)
    return FigmaApiClient(config)


if __name__ == "__main__":
    # Exemplo de uso
    import os
    
    # Configurar token (substitua pelo seu token)
    token = os.getenv('FIGMA_TOKEN', 'seu_token_aqui')
    
    if token == 'seu_token_aqui':
        print("Configure a variável de ambiente FIGMA_TOKEN ou substitua o token no código")
        exit(1)
    
    # Criar cliente
    config = FigmaApiConfig(token=token)
    client = FigmaApiClient(config)
    
    # Exemplo de uso com a URL fornecida
    file_key = "EWVeXlNAwQlwOsej7XstyO"
    node_ids = ["1:15527"]
    
    try:
        # Buscar nodes específicos
        data = client.get_file_nodes(file_key, node_ids)
        
        # Salvar dados
        client.save_data_to_file(data, "../../data/raw/figma_api_response_new.json")
        
        print("Dados baixados e salvos com sucesso!")
        
    except Exception as e:
        print(f"Erro: {e}")
