"""
Mapeador de tipos de nós do Figma e funções auxiliares.

Este módulo contém a enumeração de tipos de nós suportados e funções para classificar
e descrever nós do Figma com base em seus dados brutos.
"""
from enum import Enum
from typing import Dict, Any

class NodeType(str, Enum):
    """
    Tipos de nodes descobertos, baseados na API oficial do Figma.
    
    Referência: https://www.figma.com/plugin-docs/api/nodes/
    """
    # Nós principais
    DOCUMENT = "document"  # Nó raiz do documento
    CANVAS = "canvas"      # Canvas (antigo PAGE) dentro do documento
    SECTION = "section"    # Seção para organização
    FRAME = "frame"        # Container retangular que pode conter outros nós
    GROUP = "group"        # Agrupamento de nós
    
    # Componentes e instâncias
    COMPONENT = "component"       # Componente principal
    INSTANCE = "instance"         # Instância de um componente
    COMPONENT_SET = "component_set"  # Conjunto de variantes de componentes
    
    # Elementos de interface
    SLICE = "slice"               # Área de exportação
    STICKY = "sticky"             # Nota adesiva
    WIDGET = "widget"             # Widget incorporado
    
    # Elementos de texto
    TEXT = "text"                 # Texto
    TEXT_PATH = "text_path"       # Texto ao longo de um caminho
    CODE_BLOCK = "code_block"     # Bloco de código
    
    # Formas básicas
    RECTANGLE = "rectangle"       # Retângulo
    ELLIPSE = "ellipse"           # Elipse/círculo
    POLYGON = "polygon"           # Polígono
    STAR = "star"                 # Estrela
    LINE = "line"                 # Linha
    VECTOR = "vector"             # Vetor/path
    BOOLEAN_OPERATION = "boolean_operation"  # Operação booleana entre formas
    
    # Mídia e conteúdo incorporado
    EMBED = "embed"               # Conteúdo incorporado
    MEDIA = "media"               # Mídia (imagem/vídeo)
    LINK_UNFURL = "link_unfurl"   # Visualização de link
    
    # Tabelas
    TABLE = "table"               # Tabela
    TABLE_CELL = "table_cell"     # Célula de tabela
    
    # Elementos de desenho
    SHAPE_WITH_TEXT = "shape_with_text"  # Forma com texto
    STAMP = "stamp"              # Carimbo
    
    # Conectores
    CONNECTOR = "connector"       # Conector entre nós
    
    # Outros
    TRANSFORM_GROUP = "transform_group"  # Grupo de transformação
    OTHER = "other"               # Outros tipos não especificados

    @classmethod
    def from_string(cls, s: str) -> 'NodeType':
        """Converts a string to a NodeType enum member."""
        try:
            return cls(s.lower())
        except ValueError:
            return cls.OTHER
    

def get_node_type_icon(node_type: 'NodeType') -> str:
    """
    Returns an emoji/icon representing the Figma node type.
    Covers all main types; uses a default icon for unknown types.
    """
    # Main node type icons mapping
    icon_map = {
        NodeType.DOCUMENT: "📚",       # Document root
        NodeType.CANVAS: "📄",         # Canvas
        NodeType.SECTION: "📂",        # Section
        NodeType.FRAME: "➕",          # Frame/container
        NodeType.GROUP: "📁",          # Group
        NodeType.COMPONENT: "📦",      # Component
        NodeType.INSTANCE: "📋",       # Instance
        NodeType.COMPONENT_SET: "🗂️", # Component set/variants
        NodeType.SLICE: "✂️",         # Slice/export area
        NodeType.STICKY: "🗒️",        # Sticky note
        NodeType.WIDGET: "🧩",         # Widget
        NodeType.TEXT: "📝",           # Text
        NodeType.TEXT_PATH: "🔤",      # Text on path
        NodeType.CODE_BLOCK: "💻",     # Code block
        NodeType.RECTANGLE: "⬛",      # Rectangle
        NodeType.ELLIPSE: "⚪",        # Ellipse/circle
        NodeType.POLYGON: "🔺",        # Polygon
        NodeType.STAR: "⭐",           # Star
        NodeType.LINE: "📏",           # Line
        NodeType.VECTOR: "✏️",        # Vector/path
        NodeType.BOOLEAN_OPERATION: "", # Boolean operation
        NodeType.EMBED: "🔗",          # Embedded content
        NodeType.MEDIA: "🖼️",          # Media (image/video)
        NodeType.LINK_UNFURL: "🔗",    # Link unfurl
        NodeType.TABLE: "📊",          # Table
        NodeType.TABLE_CELL: "🔲",      # Table cell
        NodeType.SHAPE_WITH_TEXT: "🔶", # Shape with text
        NodeType.STAMP: "🛑",           # Stamp
        NodeType.CONNECTOR: "🔀",      # Connector
        NodeType.TRANSFORM_GROUP: "🔄", # Transform group
        NodeType.OTHER: "❓",           # Other/unknown
    }
    # Return mapped icon or default
    return icon_map.get(node_type, "❔")

def classify_node_type(node_data: Dict[str, Any]) -> NodeType:
    """Classifica o tipo de um node com base nos tipos oficiais da API do Figma."""
    node_type = node_data.get('type', '').upper()
    type_mapping = {
        'DOCUMENT': NodeType.DOCUMENT,
        'CANVAS': NodeType.CANVAS,
        'FRAME': NodeType.FRAME,
        'GROUP': NodeType.GROUP,
        'COMPONENT': NodeType.COMPONENT,
        'INSTANCE': NodeType.INSTANCE,
        'COMPONENT_SET': NodeType.COMPONENT_SET,
        'SECTION': NodeType.SECTION,
        'SLICE': NodeType.SLICE,
        'STICKY': NodeType.STICKY,
        'WIDGET': NodeType.WIDGET,
        'TEXT': NodeType.TEXT,
        'TEXT_PATH': NodeType.TEXT_PATH,
        'CODE_BLOCK': NodeType.CODE_BLOCK,
        'RECTANGLE': NodeType.RECTANGLE,
        'ELLIPSE': NodeType.ELLIPSE,
        'POLYGON': NodeType.POLYGON,
        'STAR': NodeType.STAR,
        'LINE': NodeType.LINE,
        'VECTOR': NodeType.VECTOR,
        'BOOLEAN_OPERATION': NodeType.BOOLEAN_OPERATION,
        'EMBED': NodeType.EMBED,
        'MEDIA': NodeType.MEDIA,
        'LINK_UNFURL': NodeType.LINK_UNFURL,
        'TABLE': NodeType.TABLE,
        'TABLE_CELL': NodeType.TABLE_CELL,
        'SHAPE_WITH_TEXT': NodeType.SHAPE_WITH_TEXT,
        'STAMP': NodeType.STAMP,
        'CONNECTOR': NodeType.CONNECTOR,
        'TRANSFORM_GROUP': NodeType.TRANSFORM_GROUP,
    }
    return type_mapping.get(node_type, NodeType.OTHER)

def has_layout_properties(node_data: Dict[str, Any]) -> bool:
    """Verifica se um node tem propriedades de layout."""
    layout_props = [
        'layoutMode', 'primaryAxisAlignItems', 'counterAxisAlignItems',
        'layoutSizingHorizontal', 'layoutSizingVertical'
    ]
    return any(prop in node_data for prop in layout_props)

def has_content(node_data: Dict[str, Any]) -> bool:
    """Verifica se um node tem conteúdo relevante."""
    return any([
        'characters' in node_data,
        'fills' in node_data and node_data['fills'],
        'strokes' in node_data and node_data['strokes'],
        'effects' in node_data and node_data['effects'],
    ])

def generate_node_description(node_data: Dict[str, Any], node_type: NodeType) -> str:
    """Gera uma descrição detalhada para um nó com base em seu tipo e propriedades."""
    parts = []
    type_descriptions = {
        NodeType.DOCUMENT: "Documento",
        NodeType.CANVAS: "Canvas",
        NodeType.FRAME: "Container/Frame",
        NodeType.GROUP: "Grupo de elementos",
        NodeType.COMPONENT: "Componente reutilizável",
        NodeType.INSTANCE: "Instância de componente",
        NodeType.COMPONENT_SET: "Conjunto de variantes de componente",
        NodeType.SECTION: "Seção de organização",
        NodeType.SLICE: "Área de exportação",
        NodeType.STICKY: "Nota adesiva",
        NodeType.WIDGET: "Widget incorporado",
        NodeType.TEXT: "Texto",
        NodeType.TEXT_PATH: "Texto ao longo de um caminho",
        NodeType.CODE_BLOCK: "Bloco de código",
        NodeType.RECTANGLE: "Retângulo",
        NodeType.ELLIPSE: "Elipse/Círculo",
        NodeType.POLYGON: "Polígono",
        NodeType.STAR: "Estrela",
        NodeType.LINE: "Linha",
        NodeType.VECTOR: "Vetor/Path",
        NodeType.BOOLEAN_OPERATION: "Operação booleana",
        NodeType.EMBED: "Conteúdo incorporado",
        NodeType.MEDIA: "Mídia (imagem/vídeo)",
        NodeType.LINK_UNFURL: "Visualização de link",
        NodeType.TABLE: "Tabela",
        NodeType.TABLE_CELL: "Célula de tabela",
        NodeType.SHAPE_WITH_TEXT: "Forma com texto",
        NodeType.STAMP: "Carimbo",
        NodeType.CONNECTOR: "Conector entre elementos",
        NodeType.TRANSFORM_GROUP: "Grupo de transformação",
        NodeType.OTHER: "Elemento"
    }
    parts.append(type_descriptions.get(node_type, "Elemento"))
    if has_layout_properties(node_data):
        layout_mode = node_data.get('layoutMode', '')
        if layout_mode:
            layout_direction = "horizontal" if layout_mode == "HORIZONTAL" else "vertical"
            parts.append(f"layout {layout_direction}")
            primary_align = node_data.get('primaryAxisAlignItems', '').lower()
            counter_align = node_data.get('counterAxisAlignItems', '').lower()
            if primary_align and primary_align != 'min':
                parts.append(f"alinhamento principal: {primary_align}")
            if counter_align and counter_align != 'min':
                parts.append(f"alinhamento secundário: {counter_align}")
    if 'characters' in node_data:
        text_preview = node_data['characters'][:30].replace('\n', ' ')
        if len(node_data['characters']) > 30:
            text_preview += "..."
        parts.append(f'"{text_preview}"')
    if 'children' in node_data and node_data['children']:
        child_count = len(node_data['children'])
        parts.append(f"{child_count} filho{'s' if child_count != 1 else ''}")
    style_parts = []
    if 'fills' in node_data and node_data['fills'] and isinstance(node_data['fills'], list):
        fill = node_data['fills'][0]
        if fill.get('visible', True) and fill.get('color'):
            color = fill['color']
            hex_color = f"#{int(color.get('r', 0)*255):02x}{int(color.get('g', 0)*255):02x}{int(color.get('b', 0)*255):02x}"
            style_parts.append(f"preenchimento: {hex_color}")
    if 'strokes' in node_data and node_data['strokes'] and isinstance(node_data['strokes'], list):
        stroke = node_data['strokes'][0]
        if stroke.get('visible', True) and stroke.get('color'):
            color = stroke['color']
            hex_color = f"#{int(color.get('r', 0)*255):02x}{int(color.get('g', 0)*255):02x}{int(color.get('b', 0)*255):02x}"
            style_parts.append(f"borda: {hex_color}")
    if style_parts:
        parts.append("(" + ", ".join(style_parts) + ")")
    return " | ".join(parts)
