"""
Component Schema Data Models

This module provides data models for design system components, focusing on
capturing raw, structured data from design tools like Figma.
"""

import json
from dataclasses import dataclass, field, asdict
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Literal
from enum import Enum

try:
    from pydantic import BaseModel, Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    BaseModel = object
    Field = None


class ComponentType(Enum):
    """Types of components."""
    ATOM = "atom"
    MOLECULE = "molecule"
    ORGANISM = "organism"
    TEMPLATE = "template"
    PAGE = "page"


class PropType(Enum):
    """Property types for component props."""
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"
    FUNCTION = "function"
    NODE = "node"
    ELEMENT = "element"
    ENUM = "enum"


class ControlType(Enum):
    """Control types for Storybook-like controls."""
    TEXT = "text"
    NUMBER = "number"
    BOOLEAN = "boolean"
    SELECT = "select"


@dataclass
class FigmaNode:
    """Represents a single node from the Figma API, containing detailed design attributes."""
    id: str
    name: str
    type: str
    children: List['FigmaNode'] = field(default_factory=list)
    
    # Geometric properties
    absolute_bounding_box: Optional[Dict[str, float]] = None
    
    # Layout properties
    layout_mode: Optional[str] = None
    primary_axis_align_items: Optional[str] = None
    counter_axis_align_items: Optional[str] = None
    item_spacing: Optional[float] = None
    padding_left: Optional[float] = None
    padding_right: Optional[float] = None
    padding_top: Optional[float] = None
    padding_bottom: Optional[float] = None

    # Style properties
    fills: Optional[List[Dict[str, Any]]] = None
    strokes: Optional[List[Dict[str, Any]]] = None
    stroke_weight: Optional[float] = None
    corner_radius: Optional[float] = None
    effects: Optional[List[Dict[str, Any]]] = None
    
    # Text properties
    characters: Optional[str] = None
    style: Optional[Dict[str, Any]] = None
    
    # Other properties
    opacity: Optional[float] = None
    component_properties: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        data = asdict(self)
        # Keep None values (which become 'null' in JSON) as they can be meaningful,
        # but remove empty lists or dicts which are just noise.
        return {k: v for k, v in data.items() if v != [] and v != {}}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FigmaNode':
        """Create instance from dictionary."""
        children = [cls.from_dict(child) for child in data.get('children', [])]
        return cls(
            id=data.get('id'),
            name=data.get('name'),
            type=data.get('type'),
            children=children,
            absolute_bounding_box=data.get('absoluteBoundingBox'),
            layout_mode=data.get('layoutMode'),
            primary_axis_align_items=data.get('primaryAxisAlignItems'),
            counter_axis_align_items=data.get('counterAxisAlignItems'),
            item_spacing=data.get('itemSpacing'),
            padding_left=data.get('paddingLeft'),
            padding_right=data.get('paddingRight'),
            padding_top=data.get('paddingTop'),
            padding_bottom=data.get('paddingBottom'),
            fills=data.get('fills'),
            strokes=data.get('strokes'),
            stroke_weight=data.get('strokeWeight'),
            corner_radius=data.get('cornerRadius'),
            effects=data.get('effects'),
            characters=data.get('characters'),
            style=data.get('style'),
            opacity=data.get('opacity'),
            component_properties=data.get('componentProperties')
        )


@dataclass
class PropSchema:
    """Schema definition for a component property."""
    name: str
    type: Union[PropType, str]
    description: str
    default: Any = None
    required: bool = False
    enum: Optional[List[Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        data = asdict(self)
        if isinstance(data.get('type'), PropType):
            data['type'] = data['type'].value
        return {k: v for k, v in data.items() if v is not None}


@dataclass
class ControlSchema:
    """Schema definition for component controls."""
    name: str
    type: Union[ControlType, str]
    options: Optional[List[Any]] = None
    default: Any = None
    description: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        data = asdict(self)
        if isinstance(data.get('type'), ControlType):
            data['type'] = data['type'].value
        return {k: v for k, v in data.items() if v is not None}


@dataclass
class ComponentMetadata:
    """Metadata about the component."""
    version: str = "1.0.0"
    created_date: Optional[datetime] = None
    last_modified: Optional[datetime] = None
    author: Optional[str] = None
    status: Literal["stable", "beta", "alpha", "deprecated"] = "stable"

    def __post_init__(self):
        """Set default dates if not provided."""
        if self.created_date is None:
            self.created_date = datetime.now()
        if self.last_modified is None:
            self.last_modified = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        data = asdict(self)
        if data.get('created_date'):
            data['created_date'] = data['created_date'].isoformat()
        if data.get('last_modified'):
            data['last_modified'] = data['last_modified'].isoformat()
        return {k: v for k, v in data.items() if v is not None}


@dataclass
class ComponentDoc:
    """
    Component documentation containing structured data extracted from a design tool.
    """
    id: str
    name: str
    category: str
    component_type: Union[ComponentType, str] = ComponentType.ATOM
    description: str = ""
    
    props_schema: List[PropSchema] = field(default_factory=list)
    controls: List[ControlSchema] = field(default_factory=list)
    
    # Hierarchical structure of Figma nodes
    figma_nodes: Optional[List[FigmaNode]] = None
    
    metadata: ComponentMetadata = field(default_factory=ComponentMetadata)
    tags: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation for JSON serialization."""
        data = {
            "id": self.id,
            "name": self.name,
            "category": self.category,
            "component_type": self.component_type.value if isinstance(self.component_type, ComponentType) else self.component_type,
            "description": self.description,
            "props_schema": [prop.to_dict() for prop in self.props_schema],
            "controls": [control.to_dict() for control in self.controls],
            "figma_nodes": [node.to_dict() for node in self.figma_nodes] if self.figma_nodes else None,
            "metadata": self.metadata.to_dict(),
            "tags": self.tags
        }
        return {k: v for k, v in data.items() if v is not None and v != []}

    def to_json(self, indent: int = 2) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ComponentDoc':
        """Create instance from dictionary."""
        props_schema = [PropSchema(**p) for p in data.get('props_schema', [])]
        controls = [ControlSchema(**c) for c in data.get('controls', [])]
        figma_nodes = [FigmaNode.from_dict(n) for n in data.get('figma_nodes', [])] if data.get('figma_nodes') else None
        
        metadata_data = data.get('metadata', {})
        if metadata_data.get('created_date') and isinstance(metadata_data['created_date'], str):
            metadata_data['created_date'] = datetime.fromisoformat(metadata_data['created_date'])
        if metadata_data.get('last_modified') and isinstance(metadata_data['last_modified'], str):
            metadata_data['last_modified'] = datetime.fromisoformat(metadata_data['last_modified'])
        metadata = ComponentMetadata(**metadata_data)

        return cls(
            id=data['id'],
            name=data['name'],
            category=data['category'],
            component_type=data.get('component_type', ComponentType.ATOM),
            description=data.get('description', ''),
            props_schema=props_schema,
            controls=controls,
            figma_nodes=figma_nodes,
            metadata=metadata,
            tags=data.get('tags', [])
        )

    @classmethod
    def from_json(cls, json_str: str) -> 'ComponentDoc':
        """Create instance from JSON string."""
        data = json.loads(json_str)
        return cls.from_dict(data)

# Pydantic support (if available)
if PYDANTIC_AVAILABLE:
    class PydanticFigmaNode(BaseModel):
        id: str
        name: str
        type: str
        children: List['PydanticFigmaNode'] = Field(default_factory=list)
        absolute_bounding_box: Optional[Dict[str, float]] = None
        layout_mode: Optional[str] = None
        fills: Optional[List[Dict[str, Any]]] = None
        strokes: Optional[List[Dict[str, Any]]] = None
        characters: Optional[str] = None
        style: Optional[Dict[str, Any]] = None

    class PydanticComponentDoc(BaseModel):
        id: str
        name: str
        category: str
        component_type: Union[ComponentType, str] = ComponentType.ATOM
        description: str = ""
        props_schema: List[PropSchema] = Field(default_factory=list)
        figma_nodes: Optional[List[PydanticFigmaNode]] = None
        metadata: ComponentMetadata = Field(default_factory=ComponentMetadata)
        tags: List[str] = Field(default_factory=list)

        class Config:
            use_enum_values = True
            
# Export main classes
__all__ = [
    'ComponentDoc',
    'FigmaNode',
    'PropSchema', 
    'ControlSchema',
    'ComponentMetadata',
    'ComponentType',
    'PropType',
    'ControlType'
]
