"""
Funções para download de imagens do Figma.

Este módulo contém funções para:
- Download de imagens de componentes
"""

from pathlib import Path
import requests

from src.utils.logging import get_logger
from src.utils.file.file_utils import sanitize_name

logger = get_logger(__name__)


def download_component_image(figma_client, file_key: str, node_id: str, node_name: str, output_path: Path) -> None:
    """
    Baixa a imagem do componente do Figma e salva junto com os dados extraídos.

    Args:
        figma_client: Cliente da API do Figma
        file_key: Chave do arquivo Figma
        node_id: ID do node no Figma
        node_name: Nome do componente
        output_path: Caminho de saída
    """
    try:
        logger.debug(f"📸 Iniciando download de imagem para {node_name} (ID: {node_id})")
        logger.debug(f"📁 Caminho de saída: {output_path}")

        # Baixar imagem usando a API do Figma
        image_urls = figma_client.download_images(
            file_key=file_key,
            node_ids=[node_id],
            format='png',
            scale=1.0
        )

        if not image_urls or node_id not in image_urls:
            logger.debug(f"⚠️ URL da imagem não retornada para {node_name}")
            return

        image_url = image_urls[node_id]
        if not image_url:
            logger.debug(f"⚠️ URL da imagem está vazia para {node_name}")
            return

        # Baixar a imagem da URL
        logger.debug(f"📥 Baixando imagem de: {image_url[:50]}...")
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()

        # Determinar caminho de saída (mesmo local dos dados JSON)
        # Se output_path é um arquivo, usar o diretório pai
        if output_path.is_file():
            image_path = output_path.parent / f"{sanitize_name(node_name)}.png"
        else:
            image_path = output_path / f"{sanitize_name(node_name)}.png"

        logger.debug(f"📸 Caminho da imagem: {image_path}")

        # Criar diretório se não existir
        image_path.parent.mkdir(parents=True, exist_ok=True)

        # Salvar imagem
        with open(image_path, 'wb') as f:
            f.write(response.content)

        logger.info(f"📸 Imagem do componente {node_name} salva")
        logger.debug(f"✅ Imagem do componente salva em: {image_path}")

    except Exception as e:
        logger.debug(f"⚠️ Erro ao baixar imagem para {node_name}: {e}")
        logger.warning(f"⚠️ Erro ao baixar imagem para {node_name}: {e}")
        # Não falhar a extração por causa da imagem 