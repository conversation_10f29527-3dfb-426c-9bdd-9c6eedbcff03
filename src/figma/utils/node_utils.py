"""
Utilitários para manipulação de nodes do Figma.

Este módulo contém funções para:
- Extração de node IDs de URLs
- Validação de node IDs
- Geração de caminhos de saída para componentes
"""

import re
from pathlib import Path
from typing import Optional
from urllib.parse import urlparse

from src.utils.file.file_utils import node_id_pattern, sanitize_name
from src.utils.logging import get_logger

logger = get_logger(__name__)


def extract_node_id_from_url(url: str) -> Optional[str]:
    """
    Extrai o node ID de uma URL do Figma.
    
    Args:
        url: URL do Figma
        
    Returns:
        Node ID extraído ou None se não encontrado
    """
    try:
        # Padrão para extrair node-id da URL (ex: 123:456 ou 123-456)
        match = re.search(r'node-id=([0-9%A-F:-]+)', url)
        if match:
            return match.group(1).replace('%3A', ':')

        # Se não encontrar, tentar extrair da URL diretamente
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        
        # Procurar por padrões de node ID na URL
        for part in path_parts:
            if validate_node_id(part):
                return part
        
        return None
    except Exception as e:
        logger.warning(f"Erro ao extrair node ID da URL: {e}")
        return None


def validate_node_id(node_id: str) -> bool:
    """
    Valida se o node ID tem formato correto.
    
    Args:
        node_id: ID do node para validar
        
    Returns:
        True se válido, False caso contrário
    """
    return node_id_pattern.match(node_id) is not None


def get_component_output_path(project_name: str, node_name: str, output_config: dict) -> Path:
    """
    Determina o caminho de saída para um componente.

    Args:
        project_name: Nome do projeto
        node_name: Nome do componente
        output_config: Configuração de saída

    Returns:
        Path: Caminho do diretório de saída
    """
    # Usar o caminho de extração do Figma
    base_path = Path(output_config.get('figma_extraction_path', 'data/figma_extraction'))
    sanitized_project = sanitize_name(project_name)
    sanitized_node = sanitize_name(node_name)

    return base_path / sanitized_project / sanitized_node 