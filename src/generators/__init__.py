"""
Geradores de código

Este módulo contém os geradores principais:
- figma_reader: Leitura e processamento de dados do Figma
- design_system_mapper: Mapeamento com Design System
- component_generator: Geração final de código Angular
"""

from .figma_reader import FigmaReader
from .design_system_mapper import DesignSystemMapper, DesignSystemMapping
from .component_generator import ComponentGenerator

__all__ = [
    'FigmaReader',
    'DesignSystemMapper',
    'DesignSystemMapping',
    'ComponentGenerator'
]
