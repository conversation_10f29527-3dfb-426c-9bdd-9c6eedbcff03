"""
Figma Reader - Leitura fiel de dados do Figma para geração de componentes Angular.
Responsável pela primeira etapa: extrair estrutura HTML fiel ao JSON do Figma.
Segue as melhores práticas do Angular para divisão de componentes.
"""

import json
from typing import Dict, List, Any
from pathlib import Path
from dataclasses import dataclass
import re

from src.utils.angular_utils import snake_to_kebab_case
from src.utils.config import ConfigLoader
from src.utils.logging import get_logger
from src.utils.component_analyzer import ComponentAnalyzer

logger = get_logger(__name__)

@dataclass
class FigmaComponentData:
    """Dados extraídos do Figma para um componente."""
    component_name: str  # Nome original do componente
    normalized_name: str  # Nome normalizado para arquivos
    file_name: str  # Nome do arquivo de saída kebab-case
    figma_id: str
    html_structure: str
    webcomponents: List[Dict[str, Any]]
    css_styles: Dict[str, Any]
    metadata: Dict[str, Any]

class FigmaReader:
    """
    Leitor de dados do Figma que gera estrutura HTML sem adivinhações.
    Segue as melhores práticas do Angular para divisão de componentes.
    """
    
    def __init__(self, config_path: str = "project_config.yaml"):
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.load_config()
        
        # Mapeamento de tipos do Figma para elementos HTML
        self.figma_type_mapping = {
            'FRAME': 'div',
            'TEXT': 'div',
            'INSTANCE': 'div',  # Webcomponent placeholder
            'ICON': 'span',
            'RECTANGLE': 'div',
            'ELLIPSE': 'div',
            'VECTOR': 'div',
            'GROUP': 'div'
        }
    
    def read_figma_component(self, figma_json_path: str) -> FigmaComponentData:
        """
        Lê um componente do Figma e gera estrutura HTML fiel.
        
        Args:
            figma_json_path: Caminho para o arquivo JSON do Figma
            
        Returns:
            FigmaComponentData com estrutura HTML fiel
        """
        logger.info("\n\n🔍 Iniciando leitura do Figma do componente...")
        
        # Carregar JSON do Figma
        figma_data = self._load_figma_json(figma_json_path)
        
        # Extrair dados do componente principal
        component_name = self._extract_component_name(figma_data)
        normalized_name = ComponentAnalyzer.normalize_component_name_for_figma(component_name)
        figma_id = figma_data.get('id', '')
        
        # Identificar webcomponents (type: "INSTANCE")
        webcomponents = self._identify_webcomponents(figma_data)
        
        # Gerar estrutura HTML fiel
        html_structure = self._generate_raw_html(figma_data)
        
        # Extrair estilos CSS
        css_styles = self._extract_css_styles(figma_data)
        
        # Preparar metadados
        metadata = {
            'figma_file': figma_json_path,
            'component_type': figma_data.get('type', ''),
            'webcomponents_count': len(webcomponents),
            'has_children': bool(figma_data.get('children')),
            'generation_method': 'figma_reader'
        }
        
        component_data = FigmaComponentData(
            component_name=component_name,
            normalized_name=normalized_name,
            file_name=snake_to_kebab_case(normalized_name),
            figma_id=figma_id,
            html_structure=html_structure,
            webcomponents=webcomponents,
            css_styles=css_styles,
            metadata=metadata
        )
        
        logger.info(f"✅ Leitura do Figma concluída: {component_name}")
        return component_data
    
    def _load_figma_json(self, json_path: str) -> Dict[str, Any]:
        """Carrega arquivo JSON do Figma."""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.debug(f"📄 JSON do Figma carregado: {json_path}")
                return data
        except Exception as e:
            logger.error(f"❌ Erro ao carregar JSON do Figma: {e}")
            raise RuntimeError(f"Falha ao carregar JSON do Figma: {e}")
    
    def _extract_component_name(self, figma_data: Dict[str, Any]) -> str:
        """Extrai nome do componente do Figma."""
        name = figma_data.get('name', 'unnamed_component')
        
        # Retornar nome original, normalização será feita separadamente
        logger.debug(f"📝 Nome do componente: {name}")
        return name
    

    
    def _identify_webcomponents(self, figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identifica webcomponents (type: 'INSTANCE') e possíveis webcomponents (GROUPs) no Figma."""
        webcomponents = []
        
        def find_webcomponents(node: Dict[str, Any], path: str = ""):
            node_type = node.get('type', '')
            
            # Webcomponent confirmado (INSTANCE)
            if node_type == 'INSTANCE':
                webcomponent = {
                    'id': node.get('id', ''),
                    'name': node.get('name', ''),
                    'componentId': node.get('componentId', ''),
                    'path': path,
                    'props': node.get('props', {}),
                    'css': node.get('css', {}),
                    'type': 'confirmed'
                }
                webcomponents.append(webcomponent)
                logger.debug(f"🔍 Webcomponent confirmado: {webcomponent['name']} (ID: {webcomponent['componentId']})")
            
            # Possível webcomponent (GROUP com indicadores)
            elif node_type == 'GROUP' and self._is_possible_webcomponent(node):
                webcomponent = {
                    'id': node.get('id', ''),
                    'name': node.get('name', ''),
                    'componentId': '',  # Não tem componentId
                    'path': path,
                    'props': node.get('props', {}),
                    'css': node.get('css', {}),
                    'type': 'possible'
                }
                webcomponents.append(webcomponent)
                logger.debug(f"🔍 Possível webcomponent: {webcomponent['name']} (análise necessária)")
            
            # Recursivamente procurar em children
            children = node.get('children', [])
            for i, child in enumerate(children):
                child_path = f"{path}.children[{i}]" if path else f"children[{i}]"
                find_webcomponents(child, child_path)
        
        find_webcomponents(figma_data)
        
        confirmed_count = len([w for w in webcomponents if w['type'] == 'confirmed'])
        possible_count = len([w for w in webcomponents if w['type'] == 'possible'])
        
        logger.info(f"🔍 {confirmed_count} webcomponents confirmados, {possible_count} possíveis webcomponents identificados")
        return webcomponents

    def _is_possible_webcomponent(self, node: Dict[str, Any]) -> bool:
        """Verifica se um GROUP pode ser um webcomponent baseado em indicadores."""
        children = node.get('children', [])
        if not children:
            return False
        
        first_child_name = children[0].get('name', '').lower()
        
        # Verificar se é clip path usando regex
        clip_path_pattern = r'clip\s*path|clip-path'
        if re.search(clip_path_pattern, first_child_name, re.IGNORECASE):
            return True
        
        # Outros indicadores de possível webcomponent
        webcomponent_indicators = [
            'button',
            'close',
            'delete',
            'edit',
            'add',
            'search',
            'menu',
            'hamburger',
            'arrow',
            'chevron',
            'plus',
            'minus',
            'check',
            'x',
            'star',
            'heart',
            'share',
            'download',
            'upload',
            'play',
            'pause',
            'stop',
            'next',
            'previous',
            'home',
            'settings',
            'user',
            'profile',
            'notification',
            'message',
            'mail',
            'phone',
            'location',
            'calendar',
            'clock',
            'lock',
            'unlock',
            'eye',
            'eye-off',
            'filter',
            'sort',
            'refresh',
            'reload',
            'save',
            'print',
            'export',
            'import'
        ]
        
        return any(indicator in first_child_name for indicator in webcomponent_indicators)
    
    def _generate_raw_html(self, figma_data: Dict[str, Any]) -> str:
        """Gera HTML a partir do JSON do Figma, sem adivinhações."""
        logger.debug("🏗️ Gerando estrutura HTML fiel...")
        
        html_lines = []
        html_lines.append(f"<!-- Componente: {figma_data.get('name', 'unnamed')} -->")
        html_lines.append(f"<!-- Figma ID: {figma_data.get('id', '')} -->")
        html_lines.append("")
        
        # Gerar HTML recursivamente
        html_content = self._generate_node_html(figma_data, 0)
        html_lines.append(html_content)
        
        return "\n".join(html_lines)
    
    def _generate_node_html(self, node: Dict[str, Any], indent_level: int = 0) -> str:
        """Gera HTML para um nó específico do Figma."""
        indent = "  " * indent_level
        
        # Obter tipo e nome
        node_type = node.get('type', 'UNKNOWN')
        node_name = node.get('name', 'unnamed')
        node_id = node.get('id', '')
        
        # Determinar tag HTML
        html_tag = self.figma_type_mapping.get(node_type, 'div')
        
        # Preparar atributos
        attributes = []
        
        # ID do Figma
        if node_id:
            attributes.append(f'data-figma-id="{node_id}"')
        
        # Nome do componente
        if node_name:
            attributes.append(f'data-figma-name="{node_name}"')
        
        # Tipo do Figma
        attributes.append(f'data-figma-type="{node_type}"')
        
        # Para webcomponents (INSTANCE)
        if node_type == 'INSTANCE':
            component_id = node.get('componentId', '')
            if component_id:
                attributes.append(f'data-component-id="{component_id}"')
            attributes.append('data-webcomponent="true"')
        
        # Para possíveis webcomponents (GROUP)
        elif node_type == 'GROUP' and self._is_possible_webcomponent(node):
            attributes.append('data-webcomponent="possible"')
        
        # Estilos CSS inline (fiel ao Figma)
        css_styles = self._extract_node_css(node)
        if css_styles:
            attributes.append(f'style="{css_styles}"')
        
        # Classes baseadas no tipo
        css_class = f"figma-{node_type.lower()}"
        if node_type == 'INSTANCE':
            css_class += " webcomponent-placeholder"
        attributes.append(f'class="{css_class}"')
        
        # Montar tag de abertura
        attrs_str = " ".join(attributes)
        opening_tag = f"{indent}<{html_tag} {attrs_str}>"
        
        # Conteúdo do nó
        content_lines = []
        
        # Texto se for elemento TEXT
        if node_type == 'TEXT':
            text_content = node.get('props', {}).get('text', '')
            if text_content:
                content_lines.append(f"{indent}  {text_content}")
        
        # Ícone se for elemento ICON
        elif node_type == 'ICON':
            icon_name = node.get('name', 'icon')
            content_lines.append(f"{indent}  <!-- {icon_name} icon -->")
        
        # Children recursivamente
        children = node.get('children', [])
        for child in children:
            child_html = self._generate_node_html(child, indent_level + 1)
            content_lines.append(child_html)
        
        # Tag de fechamento
        closing_tag = f"{indent}</{html_tag}>"
        
        # Montar HTML completo
        html_parts = [opening_tag]
        if content_lines:
            html_parts.extend(content_lines)
        html_parts.append(closing_tag)
        
        return "\n".join(html_parts)
    
    def _extract_node_css(self, node: Dict[str, Any]) -> str:
        """Extrai estilos CSS de um nó do Figma."""
        css_data = node.get('css', {})
        if not css_data:
            return ""
        
        css_parts = []
        
        # Propriedades CSS básicas
        css_mapping = {
            'width': 'width',
            'height': 'height',
            'display': 'display',
            'flexDirection': 'flex-direction',
            'flexWrap': 'flex-wrap',
            'alignItems': 'align-items',
            'alignSelf': 'align-self',
            'justifyContent': 'justify-content',
            'justifySelf': 'justify-self',
            'gap': 'gap',
            'padding': 'padding',
            'backgroundColor': 'background-color',
            'borderRadius': 'border-radius',
            'color': 'color',
            'fontSize': 'font-size',
            'fontWeight': 'font-weight'
        }
        
        for figma_prop, css_prop in css_mapping.items():
            if figma_prop in css_data:
                value = css_data[figma_prop]
                css_parts.append(f"{css_prop}: {value}")
        
        return "; ".join(css_parts)
    
    def _extract_css_styles(self, figma_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai todos os estilos CSS do componente Figma."""
        styles = {}
        
        def extract_node_styles(node: Dict[str, Any], path: str = ""):
            node_id = node.get('id', '')
            if node_id:
                styles[node_id] = {
                    'css': node.get('css', {}),
                    'path': path,
                    'type': node.get('type', ''),
                    'name': node.get('name', '')
                }
            
            children = node.get('children', [])
            for i, child in enumerate(children):
                child_path = f"{path}.children[{i}]" if path else f"children[{i}]"
                extract_node_styles(child, child_path)
        
        extract_node_styles(figma_data)
        return styles
    
    def save_raw_html(self, component_data: FigmaComponentData, output_path: str):
        """
        Salva HTML na pasta de saída.
        
        Args:
            component_data: Dados do componente extraído
            output_path: Caminho para salvar o arquivo
        """
        try:
            # Criar diretório se não existir
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Salvar HTML fiel
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(component_data.html_structure)
            
            logger.info(f"💾 HTML salvo: {output_path}")
            
            # Salvar metadados em JSON separado
            metadata_path = output_path.replace('.html', '.metadata.json')
            metadata = {
                'component_name': component_data.component_name,
                'normalized_name': component_data.normalized_name,
                'figma_id': component_data.figma_id,
                'webcomponents': component_data.webcomponents,
                'css_styles': component_data.css_styles,
                'metadata': component_data.metadata
            }
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Metadados salvos: {metadata_path}")
            
        except Exception as e:
            logger.error(f"❌ Erro ao salvar HTML fiel: {e}")
            raise RuntimeError(f"Falha ao salvar HTML fiel: {e}") 