"""
Utilitário para reorganizar estrutura de arquivos Angular gerados.

Este módulo contém funções específicas para organizar a estrutura de componentes Angular
de forma que facilite a integração com projetos existentes.
"""

import json
import glob
import shutil
from pathlib import Path
from typing import Dict, Any
from src.utils.logging import get_logger

logger = get_logger(__name__)


class AngularStructureOrganizer:
    """
    Organizador de estrutura de arquivos Angular.
    
    Reorganiza componentes gerados para facilitar integração com projetos existentes.
    """
    
    def reorganize_angular_structure(self, processed_path: Path):
        """
        Reorganiza a estrutura Angular para facilitar integração com projetos existentes.
        
        Estrutura final:
        angular/
        └── [component_name]/
            ├── [component_name].component.html
            ├── [component_name].component.ts
            ├── [component_name].component.scss
            └── components/
                ├── [child1]/
                ├── [child2]/
                └── ...
        
        Args:
            processed_path: Caminho para a pasta figma_processed
        """
        logger.info("🔄 Reorganizando estrutura Angular...")
        
        # Encontrar o wrapper correto baseado nos metadados salvos
        wrapper_dir, wrapper_name, angular_path = self._find_wrapper_directory(processed_path)
        
        if not wrapper_name:
            logger.warning(f"⚠️ Wrapper não encontrado em {processed_path}, mantendo estrutura atual")
            return
        
        # Converter para Path
        wrapper_dir = Path(wrapper_dir)
        angular_path = Path(angular_path)
        
        # Encontrar todos os componentes filhos (exceto o wrapper)
        child_components = []
        for item in angular_path.iterdir():
            if item.is_dir() and item.name != wrapper_name:
                child_components.append(item)
        
        if not child_components:
            logger.info("ℹ️ Nenhum componente filho encontrado para reorganizar")
            return
        
        # Criar pasta components dentro do wrapper
        components_dir = wrapper_dir / "components"
        components_dir.mkdir(exist_ok=True)
        
        # Mover componentes filhos para pasta components
        for child_dir in child_components:
            # Mover pasta do componente filho para components/
            new_path = components_dir / child_dir.name
            if new_path.exists():
                # Se já existe, remover primeiro
                shutil.rmtree(new_path)

            # Usar copytree em vez de rename para evitar problemas de corrupção
            shutil.copytree(child_dir, new_path)
            # Remover diretório original
            shutil.rmtree(child_dir)
            logger.debug(f"📦 Movido {child_dir.name} para components/")
        
        # Atualizar imports no wrapper TypeScript
        self._update_wrapper_imports(wrapper_dir, components_dir)
        
        logger.info(f"✅ Estrutura reorganizada: {wrapper_dir.name}/components/")


    def _find_wrapper_directory(self, processed_path: Path) -> Dict[str, Any]:
        """
        Encontra o diretório do wrapper correto baseado nos metadados salvos.
        
        Args:
            processed_path: Caminho para a pasta figma_processed
            
        Returns:
            Path do diretório do wrapper ou None se não encontrado
        """
        # Procurar por arquivos de metadados de wrapper na pasta figma_processed
        metadata_pattern = f"{processed_path}/**/*_wrapper_metadata.json"
        metadata_files = glob.glob(metadata_pattern, recursive=True)
        
        if not metadata_files:
            logger.debug("🔍 Nenhum arquivo de metadados de wrapper encontrado.")
            return {'wrapper_dir': None, 'wrapper_name': None, 'angular_path': None}
        
        # Carregar metadados do wrapper para obter o file_name
        wrapper_name = ''
        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    wrapper_name = metadata['file_name']
                    logger.debug(f"📋 Wrapper encontrado nos metadados: {metadata['file_name']}")
            except Exception as e:
                logger.warning(f"⚠️ Erro ao ler metadados {metadata_file}: {e}")
        
        # Procurar pelo diretório do wrapper na pasta angular
        angular_path = str(processed_path).replace('/figma_processed', '/angular')
        logger.debug(f"🔍 Procurando wrapper `{wrapper_name}` em `{angular_path}`")
        if wrapper_name:
            wrapper_dir = f"{angular_path}/{wrapper_name}"
            if Path(wrapper_dir).exists():
                logger.debug(f"🎯 Wrapper encontrado: {wrapper_dir}")
            else:
                logger.debug(f"❌ Wrapper não encontrado: {wrapper_dir}")
                wrapper_dir = None
        
        return {'wrapper_dir': wrapper_dir, 'wrapper_name': wrapper_name, 'angular_path': angular_path}
    
    
    def _update_wrapper_imports(self, wrapper_dir: Path, components_dir: Path):
        """
        Atualiza imports no arquivo TypeScript do wrapper.
        
        Args:
            wrapper_dir: Diretório do wrapper
            components_dir: Diretório dos componentes filhos
        """
        ts_file = next(wrapper_dir.glob("*.component.ts"), None)
        if not ts_file:
            return
        
        # Ler conteúdo atual
        with open(ts_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Atualizar imports para usar caminho relativo components/
        updated_content = content
        for child_dir in components_dir.iterdir():
            if child_dir.is_dir():
                child_name = child_dir.name
                old_import = f"import {{ {self._to_pascal_case(child_name)}Component }} from '../{child_name}/{child_name}.component';"
                new_import = f"import {{ {self._to_pascal_case(child_name)}Component }} from './components/{child_name}/{child_name}.component';"
                updated_content = updated_content.replace(old_import, new_import)
        
        # Salvar arquivo atualizado
        with open(ts_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.debug(f"📝 Imports atualizados em {ts_file.name}")
    
    def _to_pascal_case(self, name: str) -> str:
        """
        Converte nome para PascalCase.
        
        Args:
            name: Nome em kebab-case ou snake_case
            
        Returns:
            Nome em PascalCase
        """
        # Substituir hífens e underscores por espaços, depois capitalizar cada palavra
        words = name.replace('-', ' ').replace('_', ' ').split()
        return ''.join(word.capitalize() for word in words)


def reorganize_angular_structure(angular_path: Path):
    """
    Função de conveniência para reorganizar estrutura Angular.
    
    Args:
        angular_path: Caminho para a pasta angular
    """
    organizer = AngularStructureOrganizer()
    organizer.reorganize_angular_structure(angular_path)
