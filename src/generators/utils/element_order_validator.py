"""
Utilitário para validar e corrigir a ordem dos elementos baseada nos dados do Figma.
"""

import logging
from typing import Dict, List, Any, Optional
import json

logger = logging.getLogger(__name__)


class ElementOrderValidator:
    """Valida e corrige a ordem dos elementos conforme dados do Figma."""
    
    def __init__(self):
        self.logger = logger
    
    def validate_element_order(self, figma_data: Dict[str, Any], generated_elements: List[Dict]) -> List[Dict]:
        """
        Valida e corrige a ordem dos elementos baseada nos dados do Figma.
        
        Args:
            figma_data: Dados originais do Figma
            generated_elements: Elementos gerados pela IA
            
        Returns:
            Lista de elementos na ordem correta
        """
        try:
            # Extrair ordem original do Figma
            original_order = self._extract_original_order(figma_data)
            
            if not original_order:
                logger.warning("⚠️ Não foi possível extrair ordem original do Figma")
                return generated_elements
            
            # Reordenar elementos gerados
            reordered_elements = self._reorder_elements(generated_elements, original_order)
            
            logger.info(f"✅ Ordem dos elementos validada: {len(reordered_elements)} elementos")
            return reordered_elements
            
        except Exception as e:
            logger.error(f"❌ Erro ao validar ordem dos elementos: {e}")
            return generated_elements
    
    def _extract_original_order(self, figma_data: Dict[str, Any]) -> List[Dict]:
        """Extrai a ordem original dos elementos do Figma."""
        order = []
        
        def extract_order_recursive(node: Dict, depth: int = 0) -> None:
            if not isinstance(node, dict):
                return
            
            node_type = node.get('type', '')
            node_name = node.get('name', '')
            node_id = node.get('id', '')
            
            # Adicionar elemento à ordem
            if node_type and node_name:
                order.append({
                    'id': node_id,
                    'name': node_name,
                    'type': node_type,
                    'depth': depth,
                    'order_index': len(order)
                })
            
            # Processar children na ordem
            children = node.get('children', [])
            for child in children:
                extract_order_recursive(child, depth + 1)
        
        # Extrair ordem do metadata
        if 'metadata' in figma_data:
            extract_order_recursive(figma_data['metadata'])
        elif isinstance(figma_data, dict):
            extract_order_recursive(figma_data)
        
        return order
    
    def _reorder_elements(self, generated_elements: List[Dict], original_order: List[Dict]) -> List[Dict]:
        """Reordena elementos gerados baseado na ordem original."""
        # Criar mapeamento de nomes para ordem
        name_to_order = {}
        for i, item in enumerate(original_order):
            name = item.get('name', '').lower()
            name_to_order[name] = i
        
        # Função para obter ordem de um elemento
        def get_element_order(element: Dict) -> int:
            element_name = element.get('name', '').lower()
            element_content = element.get('content', '').lower()
            
            # Tentar encontrar por nome exato
            if element_name in name_to_order:
                return name_to_order[element_name]
            
            # Tentar encontrar por conteúdo
            for name, order in name_to_order.items():
                if element_content in name or name in element_content:
                    return order
            
            # Se não encontrar, colocar no final
            return len(original_order)
        
        # Ordenar elementos
        try:
            sorted_elements = sorted(generated_elements, key=get_element_order)
            return sorted_elements
        except Exception as e:
            logger.warning(f"⚠️ Erro ao reordenar elementos: {e}")
            return generated_elements
    
    def validate_no_duplicates(self, wrapper_elements: List[Dict], child_elements: List[List[Dict]]) -> Dict[str, List[Dict]]:
        """
        Valida que não há elementos duplicados entre wrapper e filhos.
        
        Args:
            wrapper_elements: Elementos do wrapper
            child_elements: Lista de elementos de cada componente filho
            
        Returns:
            Dict com elementos únicos para wrapper e filhos
        """
        try:
            # Coletar todos os elementos dos filhos
            all_child_elements = []
            for child_list in child_elements:
                all_child_elements.extend(child_list)
            
            # Identificar duplicatas
            child_contents = set()
            for element in all_child_elements:
                content = element.get('content', '').strip().lower()
                if content:
                    child_contents.add(content)
            
            # Filtrar wrapper para remover duplicatas
            filtered_wrapper = []
            for element in wrapper_elements:
                content = element.get('content', '').strip().lower()
                if content not in child_contents:
                    filtered_wrapper.append(element)
                else:
                    logger.info(f"🔄 Removido elemento duplicado do wrapper: {content}")
            
            return {
                'wrapper': filtered_wrapper,
                'children': child_elements
            }
            
        except Exception as e:
            logger.error(f"❌ Erro ao validar duplicatas: {e}")
            return {
                'wrapper': wrapper_elements,
                'children': child_elements
            }
    
    def extract_button_order_from_figma(self, figma_data: Dict[str, Any]) -> List[str]:
        """Extrai a ordem correta dos botões dos dados do Figma."""
        button_order = []
        
        def find_buttons_recursive(node: Dict) -> None:
            if not isinstance(node, dict):
                return
            
            node_type = node.get('type', '')
            node_name = node.get('name', '').lower()
            
            # Identificar botões
            if (node_type == 'INSTANCE' and 'button' in node_name) or 'button' in node_name:
                # Extrair texto do botão
                props = node.get('props', {})
                button_text = props.get('✏️ Label#211:2', '') or props.get('✏️ Label#212:147', '')
                
                if button_text:
                    button_order.append(button_text.strip())
                    logger.debug(f"🔘 Botão encontrado: {button_text}")
            
            # Processar children
            children = node.get('children', [])
            for child in children:
                find_buttons_recursive(child)
        
        # Buscar em toda a estrutura
        if 'metadata' in figma_data:
            find_buttons_recursive(figma_data['metadata'])
        elif isinstance(figma_data, dict):
            find_buttons_recursive(figma_data)
        
        logger.info(f"📋 Ordem dos botões extraída: {button_order}")
        return button_order
