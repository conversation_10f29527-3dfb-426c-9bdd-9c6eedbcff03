# -*- coding: utf-8 -*-
"""
Flow API Initializer - Utilitário para inicialização compartilhada do Flow API.

Este módulo centraliza a lógica de inicialização do Flow API para evitar duplicação
de código entre os diferentes geradores.
"""

from typing import Optional
from src.utils.logging import get_logger

logger = get_logger(__name__)


class FlowAPIInitializer:
    """
    Utilitário para inicialização compartilhada do Flow API.
    
    Centraliza a lógica de inicialização e verificação de conexão
    para evitar duplicação de código entre geradores.
    """
    
    @staticmethod
    def initialize_flow_api() -> Optional[object]:
        """
        Inicializa Flow API uma vez para compartilhar entre classes.
        
        Returns:
            Cliente Flow API inicializado ou None se falhar
        """
        try:
            from flow_api import FlowAPIClient
            
            print("🔄 Inicializando Flow API...")
            logger.debug("🔄 Inicializando checagem de token e conexão com Flow API...")
            client = FlowAPIClient()
            
            connection = client.check_connection()
            
            if connection['status'] == 'connected':
                print("✅ Flow API conectada com sucesso!")
                logger.debug("🤖 Flow API inicializada para busca e geração")
                return client
            else:
                print(f"❌ Falha na conexão: {connection['message']}")
                logger.debug(f"⚠️ Flow API não disponível - status: {connection['status']}")
                return None
        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Flow API: {e}")
            return None
    
    @staticmethod
    def ensure_flow_api_available(flow_client: Optional[object]) -> Optional[object]:
        """
        Garante que o Flow API está disponível, inicializando se necessário.
        
        Args:
            flow_client: Cliente Flow API existente (pode ser None)
            
        Returns:
            Cliente Flow API inicializado ou None se falhar
        """
        if flow_client is None:
            return FlowAPIInitializer.initialize_flow_api()
        return flow_client
