# -*- coding: utf-8 -*-
"""
Generator Config Loader - Utilitário para carregamento de configurações dos geradores.

Este módulo centraliza a lógica de carregamento de configurações e prompts
para evitar duplicação de código entre os diferentes geradores.
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from src.utils.config import ConfigLoader
from src.utils.logging import get_logger

logger = get_logger(__name__)


class GeneratorConfigLoader:
    """
    Utilitário para carregamento compartilhado de configurações dos geradores.
    
    Centraliza a lógica de carregamento de configurações, prompts e variáveis
    de ambiente para evitar duplicação de código entre geradores.
    """
    
    def __init__(self, config_path: str = "project_config.yaml"):
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.load_config()
        self.ai_config = self.config_loader.get_ai_config()
        self._prompts_cache: Optional[Dict[str, Any]] = None
        
        # Carregar variáveis de ambiente
        self._load_environment_variables()
    
    def _load_environment_variables(self):
        """Carrega variáveis de ambiente do arquivo .env."""
        try:
            from dotenv import load_dotenv
            load_dotenv()
        except ImportError:
            print("⚠️ python-dotenv não instalado. Tentando carregar .env manualmente...")
            import os
            env_path = Path(__file__).parent.parent.parent.parent / '.env'
            if env_path.exists():
                with open(env_path, 'r') as f:
                    for line in f:
                        if line.strip() and not line.startswith('#'):
                            key, value = line.strip().split('=', 1)
                            os.environ[key] = value
    
    def load_prompts(self, prompts_file: str = "prompts.yaml") -> Dict[str, Any]:
        """
        Carrega prompts do arquivo YAML.
        
        Args:
            prompts_file: Nome do arquivo de prompts (padrão: "prompts.yaml")
            
        Returns:
            Dicionário com os prompts carregados
        """
        if self._prompts_cache is not None:
            return self._prompts_cache
            
        try:
            prompts_path = Path(__file__).parent.parent / prompts_file
            if prompts_path.exists():
                with open(prompts_path, 'r', encoding='utf-8') as f:
                    self._prompts_cache = yaml.safe_load(f)
                    logger.debug(f"📝 Prompts carregados de {prompts_file}")
                    return self._prompts_cache
            else:
                logger.warning(f"Arquivo de prompts não encontrado: {prompts_path}")
        except Exception as e:
            logger.warning(f"Erro ao carregar prompts: {e}")
        
        return {}
    
    def get_config(self) -> Dict[str, Any]:
        """
        Retorna a configuração carregada.
        
        Returns:
            Dicionário com a configuração
        """
        return self.config
    
    def get_ai_config(self) -> Dict[str, Any]:
        """
        Retorna a configuração de IA.
        
        Returns:
            Dicionário com a configuração de IA
        """
        return self.ai_config
    
    def get_config_loader(self) -> ConfigLoader:
        """
        Retorna o ConfigLoader para acesso direto.
        
        Returns:
            Instância do ConfigLoader
        """
        return self.config_loader
