# -*- coding: utf-8 -*-
"""
Motor de validação que detecta e corrige automaticamente erros comuns
em código Angular gerado (TypeScript, HTML, SCSS).
"""

import re
from typing import Dict, Any, List, Tuple

from src.utils.logging import get_logger

logger = get_logger(__name__)


class ValidationEngine:
    """
    Valida e corrige automaticamente problemas comuns em código Angular.
    """
    
    def __init__(self):
        self.corrections_applied = []
    
    def validate_and_fix_all(self, html: str, typescript: str, scss: str) -> Tuple[str, str, str]:
        """
        Valida e corrige todos os arquivos com validação cruzada.
        
        Args:
            html: Código HTML
            typescript: Código TypeScript  
            scss: Código SCSS
            
        Returns:
            Tuple[html_corrigido, typescript_corrigido, scss_corrigido]
        """
        logger.info("🔍 Iniciando validação e correção automática...")
        self.corrections_applied = []
        
        # 1. Validar e corrigir TypeScript
        fixed_typescript = self.validate_and_fix_typescript(typescript, html)
        
        # 2. Validar e corrigir HTML (baseado no TypeScript corrigido)
        fixed_html = self.validate_and_fix_html(html, fixed_typescript)
        
        # 3. Validar e corrigir SCSS (baseado no HTML corrigido)
        fixed_scss = self.validate_and_fix_scss(scss, fixed_html)
        
        # 4. Validação cruzada final
        fixed_html, fixed_typescript, fixed_scss = self._cross_validate_files(
            fixed_html, fixed_typescript, fixed_scss
        )
        
        # Log das correções aplicadas
        if self.corrections_applied:
            logger.info(f"✅ Aplicadas {len(self.corrections_applied)} correções:")
            for correction in self.corrections_applied:
                logger.info(f"   - {correction}")
        else:
            logger.info("✅ Nenhuma correção necessária")
        
        return fixed_html, fixed_typescript, fixed_scss
    
    def validate_and_fix_typescript(self, typescript: str, html_context: str = "") -> str:
        """
        Valida e corrige problemas comuns no TypeScript.
        
        Args:
            typescript: Código TypeScript
            html_context: HTML para análise de dependências
            
        Returns:
            TypeScript corrigido
        """
        fixed_code = typescript
        
        # 1. Corrigir imports para standalone components
        fixed_code = self._fix_angular_imports(fixed_code, html_context)
        
        # 2. Corrigir nomes de métodos (remover prefixo "on")
        fixed_code = self._fix_method_names(fixed_code)
        
        # 3. Corrigir tipos comuns
        fixed_code = self._fix_common_types(fixed_code)
        
        # 4. Adicionar métodos faltando (baseado no HTML)
        if html_context:
            fixed_code = self._add_missing_methods(fixed_code, html_context)
        
        # 5. Corrigir sintaxe básica
        fixed_code = self._fix_typescript_syntax(fixed_code)
        
        return fixed_code
    
    def validate_and_fix_html(self, html: str, typescript_context: str = "") -> str:
        """
        Valida e corrige problemas comuns no HTML.
        
        Args:
            html: Código HTML
            typescript_context: TypeScript para validação de métodos
            
        Returns:
            HTML corrigido
        """
        fixed_html = html
        
        # 1. Corrigir nomes de métodos em eventos
        fixed_html = self._fix_html_method_names(fixed_html)
        
        # 2. Corrigir estrutura de classes do design system
        fixed_html = self._fix_design_system_structure(fixed_html)
        
        # 3. Validar métodos existem no TypeScript
        if typescript_context:
            fixed_html = self._validate_html_methods_exist(fixed_html, typescript_context)
        
        return fixed_html
    
    def validate_and_fix_scss(self, scss: str, html_context: str = "") -> str:
        """
        Valida e corrige problemas comuns no SCSS.
        
        Args:
            scss: Código SCSS
            html_context: HTML para validação de seletores
            
        Returns:
            SCSS corrigido
        """
        fixed_scss = scss
        
        # 1. Remover seletores que não existem no HTML
        if html_context:
            fixed_scss = self._remove_unused_selectors(fixed_scss, html_context)
        
        # 2. Priorizar classes do design system
        fixed_scss = self._prioritize_design_system_classes(fixed_scss)
        
        # 3. Corrigir sintaxe SCSS
        fixed_scss = self._fix_scss_syntax(fixed_scss)
        
        return fixed_scss
    
    def _fix_angular_imports(self, typescript: str, html_context: str) -> str:
        """Corrige imports do Angular baseado no uso no HTML."""
        fixed_code = typescript
        required_imports = []
        
        # Detectar necessidade de CommonModule
        if '*ngFor' in html_context or '*ngIf' in html_context:
            if 'CommonModule' not in fixed_code:
                required_imports.append('CommonModule')
        
        # Detectar necessidade de FormsModule
        if '[(ngModel)]' in html_context or 'ngModel' in html_context:
            if 'FormsModule' not in fixed_code:
                required_imports.append('FormsModule')
        
        # Adicionar imports necessários
        if required_imports:
            # Adicionar imports no topo
            import_lines = []
            if 'CommonModule' in required_imports:
                import_lines.append("import { CommonModule } from '@angular/common';")
            if 'FormsModule' in required_imports:
                import_lines.append("import { FormsModule } from '@angular/forms';")
            
            # Inserir após imports existentes
            lines = fixed_code.split('\n')
            insert_index = 0
            for i, line in enumerate(lines):
                if line.startswith('import '):
                    insert_index = i + 1
            
            for import_line in reversed(import_lines):
                lines.insert(insert_index, import_line)
            
            fixed_code = '\n'.join(lines)
            
            # Adicionar ao decorator @Component
            if 'imports: [' not in fixed_code:
                imports_str = ', '.join(required_imports)
                fixed_code = fixed_code.replace(
                    '@Component({',
                    f'@Component({{\n  imports: [{imports_str}],'
                )
            else:
                # Adicionar aos imports existentes
                for imp in required_imports:
                    if imp not in fixed_code:
                        fixed_code = fixed_code.replace(
                            'imports: [',
                            f'imports: [{imp}, '
                        )
            
            self.corrections_applied.append(f"Adicionados imports: {', '.join(required_imports)}")
        
        return fixed_code
    
    def _fix_method_names(self, typescript: str) -> str:
        """Remove prefixo 'on' de métodos para seguir convenção Angular."""
        fixed_code = typescript
        
        # Encontrar métodos com prefixo "on"
        method_pattern = r'(\s+)(on[A-Z]\w*)\(\)'
        matches = re.findall(method_pattern, typescript)
        
        for indent, method_name in matches:
            if method_name.startswith('on') and len(method_name) > 2:
                new_name = method_name[2].lower() + method_name[3:]
                fixed_code = fixed_code.replace(f'{method_name}()', f'{new_name}()')
                self.corrections_applied.append(f"Método renomeado: {method_name} → {new_name}")
        
        return fixed_code
    
    def _fix_common_types(self, typescript: str) -> str:
        """Corrige tipos comuns que causam erros."""
        fixed_code = typescript
        
        # Corrigir vertAlign
        replacements = {
            "vertAlign: 'center'": "vertAlign: 'middle'",
            'vertAlign: "center"': 'vertAlign: "middle"'
        }
        
        for old, new in replacements.items():
            if old in fixed_code:
                fixed_code = fixed_code.replace(old, new)
                self.corrections_applied.append(f"Tipo corrigido: {old} → {new}")
        
        return fixed_code
    
    def _add_missing_methods(self, typescript: str, html: str) -> str:
        """Adiciona métodos que são chamados no HTML mas não existem no TypeScript."""
        # Extrair métodos chamados no HTML
        html_methods = set(re.findall(r'\(click\)="(\w+)\(\)"', html))
        
        # Extrair métodos existentes no TypeScript
        ts_methods = set(re.findall(r'(\w+)\(\)\s*{', typescript))
        
        # Encontrar métodos faltando
        missing_methods = html_methods - ts_methods
        
        if missing_methods:
            fixed_code = typescript
            
            # Adicionar métodos faltando antes do último }
            for method in missing_methods:
                method_stub = f"\n\n  {method}() {{\n    // TODO: Implementar {method}\n  }}"
                # Inserir antes do último }
                fixed_code = fixed_code.rstrip().rstrip('}') + method_stub + '\n}'
                self.corrections_applied.append(f"Método adicionado: {method}()")
            
            return fixed_code
        
        return typescript
    
    def _fix_typescript_syntax(self, typescript: str) -> str:
        """Corrige problemas básicos de sintaxe TypeScript."""
        fixed_code = typescript
        
        # Corrigir vírgulas faltando em objetos
        # Corrigir ponto e vírgula faltando
        # Outras correções básicas podem ser adicionadas aqui
        
        return fixed_code
    
    def _fix_html_method_names(self, html: str) -> str:
        """Corrige nomes de métodos no HTML removendo prefixo 'on'."""
        fixed_html = html
        
        # Encontrar métodos com prefixo "on" em eventos
        method_pattern = r'\(click\)="(on[A-Z]\w*)\(\)"'
        matches = re.findall(method_pattern, html)
        
        for method_name in matches:
            if method_name.startswith('on') and len(method_name) > 2:
                new_name = method_name[2].lower() + method_name[3:]
                fixed_html = fixed_html.replace(
                    f'(click)="{method_name}()"',
                    f'(click)="{new_name}()"'
                )
                self.corrections_applied.append(f"Método HTML corrigido: {method_name} → {new_name}")
        
        return fixed_html
    
    def _fix_design_system_structure(self, html: str) -> str:
        """Corrige estrutura para seguir padrões do design system."""
        fixed_html = html
        
        # Aqui podem ser adicionadas correções específicas do design system
        # Por exemplo, estrutura correta de brad-text-field, brad-button, etc.
        
        return fixed_html
    
    def _validate_html_methods_exist(self, html: str, typescript: str) -> str:
        """Valida se métodos chamados no HTML existem no TypeScript."""
        # Esta validação é informativa - a correção é feita no TypeScript
        html_methods = set(re.findall(r'\(click\)="(\w+)\(\)"', html))
        ts_methods = set(re.findall(r'(\w+)\(\)\s*{', typescript))
        
        missing_methods = html_methods - ts_methods
        if missing_methods:
            logger.warning(f"⚠️ Métodos chamados no HTML mas não encontrados no TS: {missing_methods}")
        
        return html
    
    def _remove_unused_selectors(self, scss: str, html: str) -> str:
        """Remove seletores SCSS que não são usados no HTML."""
        # Implementação básica - pode ser expandida
        return scss
    
    def _prioritize_design_system_classes(self, scss: str) -> str:
        """Prioriza uso de classes do design system em vez de estilos customizados."""
        # Implementação básica - pode ser expandida
        return scss
    
    def _fix_scss_syntax(self, scss: str) -> str:
        """Corrige problemas básicos de sintaxe SCSS."""
        # Implementação básica - pode ser expandida
        return scss
    
    def _cross_validate_files(self, html: str, typescript: str, scss: str) -> Tuple[str, str, str]:
        """Validação cruzada final entre todos os arquivos."""
        # Validação final para garantir consistência
        # Por enquanto, retorna os arquivos como estão
        # Pode ser expandida com validações mais complexas
        
        return html, typescript, scss
