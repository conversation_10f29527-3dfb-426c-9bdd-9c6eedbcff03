#!/usr/bin/env python3
"""
Script para extrair componentes do Storybook.

Este script extrai componentes do Storybook usando configurações do YAML
e salva os componentes extraídos em formato JSON.
"""

import argparse
import sys
from pathlib import Path
import json
from typing import Dict, Any, Optional

# Adicionar o diretório raiz do projeto ao sys.path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.design_system.scrapers import StorybookScraper, StorybookConfig, StorybookExtractor, StorybookExtractorConfig
from src.design_system.scrapers import StorybookVersionDetector
from src.utils.config import ConfigLoader
from src.utils.logging import setup_script_logging

# Setup logging será configurado na função main
logger = None

def extract_storybook_from_config(config_file: Optional[str] = None, limit: Optional[int] = None, no_markdown: bool = False, save_json: bool = False, force_update: bool = False, category_filter: Optional[str] = None, debug_urls: bool = False, skip_menu_expansion: bool = False) -> Dict[str, Any]:
    """
    Extrai componentes do Storybook usando configurações do YAML.
    
    Args:
        config_file: Arquivo de configuração (opcional).
        limit: Número máximo de componentes a serem extraídos.
        force_update: Forçar a atualização mesmo que a versão seja a mesma.
        debug_urls: Se deve mostrar URLs encontradas para debug.
        skip_menu_expansion: Se deve pular a expansão de menus (para testes rápidos).
        
    Returns:
        Relatório da extração.
    """
    # Configurar logger se não estiver configurado
    global logger
    if logger is None:
        logger = setup_script_logging("storybook_extract", verbose=True)
    
    config_loader = ConfigLoader(config_file)
    ds_config = config_loader.get_design_system_config()
    
    # Obter a URL base do Storybook
    base_storybook_url = config_loader.get_storybook_url()
    logger.info(f"URL base do Storybook: {base_storybook_url}")

    ds_slug = ds_config.get('slug', 'meu_design_system')
    ds_name = ds_config.get("name", "Meu Design System")

    base_cache_dir = Path(ds_config['cache']['cache_dir'])
    client_cache_dir = base_cache_dir / ds_slug
    client_cache_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Usando diretório do cliente: {client_cache_dir}")

    # --- Lógica de verificação de versão ---
    report_file = client_cache_dir / "extraction_report.json"
    
    detector = StorybookVersionDetector(base_storybook_url)
    latest_version = detector.detect_latest_version()
    
    final_storybook_url = base_storybook_url # Fallback para a URL base
    if latest_version:
        logger.info(f"Versão mais recente detectada: {latest_version}")
        final_storybook_url = f"{base_storybook_url}/storybook-{latest_version}/"
        
        if report_file.exists() and not force_update:
            try:
                with open(report_file, 'r', encoding='utf-8') as f:
                    cached_report = json.load(f)
                cached_version = cached_report.get('version')
                
                if cached_version:
                    logger.info(f"Versão em cache encontrada no relatório: {cached_version}")
                    if cached_version == latest_version:
                        logger.info("A versão do Storybook não mudou. Nenhuma extração necessária. Use --force-update para refazer mesmo assim.")
                        return {
                            "status": "skipped",
                            "reason": "A versão do Storybook não mudou.",
                            "cached_version": cached_version,
                            "latest_version": latest_version
                        }
                else:
                    logger.warning("Relatório em cache encontrado, mas não contém uma chave de versão.")
            except (json.JSONDecodeError, IOError) as e:
                logger.warning(f"Não foi possível ler o arquivo de relatório em cache. A extração continuará. Erro: {e}")
    else:
        logger.warning("Não foi possível detectar a versão mais recente do Storybook. A extração continuará, mas o cache de versão não será usado.")

    logger.info(f"URL final do Storybook para extração: {final_storybook_url}")

    # Configuração para o scraper (apenas leitura de URLs)
    scraper_config = StorybookConfig(
        base_url=final_storybook_url,
        output_dir=client_cache_dir,
        headless=ds_config['extraction']['headless'],
        timeout=ds_config['extraction']['timeout'],
        wait_time=ds_config['extraction']['wait_time'],
        max_retries=ds_config['extraction']['max_retries']
    )
    
    # Configuração para o extrator (extração de conteúdo)
    extractor_config = StorybookExtractorConfig(
        base_url=final_storybook_url,
        output_dir=client_cache_dir,
        headless=ds_config['extraction']['headless'],
        timeout=ds_config['extraction']['timeout'],
        wait_time=ds_config['extraction']['wait_time'],
        max_retries=ds_config['extraction']['max_retries']
    )
    
    # FASE 1: Extrair URLs dos componentes usando o scraper
    with StorybookScraper(scraper_config) as scraper:
        # Adicionar flags de debug ao scraper
        scraper.debug_urls = debug_urls
        scraper.skip_menu_expansion = skip_menu_expansion
        
        # Extrair URLs dos componentes
        scraper_result = scraper.extract_component_urls(limit=limit, category_filter=category_filter)
        
        if not scraper_result:
            logger.warning("Nenhuma URL de componente encontrada.")
            return {
                "status": "error",
                "reason": "Nenhuma URL de componente encontrada."
            }
        
        # Extrair URLs dos componentes e URL da documentação principal
        component_urls = scraper_result.get("component_urls", {})
        main_docs_url = scraper_result.get("main_docs_url")
        
        if not component_urls:
            logger.warning("Nenhuma URL de componente encontrada.")
            return {
                "status": "error",
                "reason": "Nenhuma URL de componente encontrada."
            }
        
        # Log da documentação principal se encontrada
        if main_docs_url:
            logger.info(f"Documentação principal encontrada: {main_docs_url}")
        else:
            logger.info("Documentação principal não encontrada - será usado fallback")
    
    # FASE 2: Extrair conteúdo dos componentes usando o extrator
    with StorybookExtractor(extractor_config) as extractor:
        # Passar URL da documentação principal para o extractor
        if main_docs_url:
            extractor.main_docs_url = main_docs_url
            logger.info(f"URL da documentação principal passada para o extractor: {main_docs_url}")
        
        # Salvar componentes categorizados apenas se solicitado
        saved_files = {}
        if save_json:
            # Extrair componentes e salvar em JSON
            components = extractor.extract_components(component_urls, limit=limit)
            saved_files = extractor.save_categorized_components()
            # Manter compatibilidade com arquivo único apenas se solicitado
            extractor.save_components("storybook_components.json")
        
        ai_context_dir = None
        markdown_dir = None
        
        # Por padrão, salvar em formato Markdown + JSON Index
        if not no_markdown:
            logger.info("Salvando componentes em formato Markdown + JSON Index...")
            
            # Usar nova extração Markdown com estrutura categorizada
            markdown_files = extractor.extract_markdown_components(component_urls, limit=limit)
            
            # A estrutura já é criada dentro do extractor com pastas por categoria
            markdown_dir = Path("data/design_system") / ds_slug
            logger.info(f"Arquivos Markdown gerados em: {markdown_dir}")
            logger.info(f"Total de arquivos Markdown: {len(markdown_files)}")
        
        if save_json:
            logger.info("Salvando componentes em formato JSON estruturado...")
            # Salvar componentes categorizados (formato anterior)
            saved_files = extractor.save_categorized_components()
            logger.info("Arquivos JSON estruturados salvos")
            logger.info(f"Arquivos gerados: {list(saved_files.keys())}")
        
        report = extractor.generate_report()
        report['categorized_files'] = saved_files
        report['storybook_url'] = base_storybook_url
        report['client_name'] = ds_name
        report['config_used'] = {
            'headless': ds_config['extraction']['headless'],
            'timeout': ds_config['extraction']['timeout']
        }
        
        if ai_context_dir:
            report['ai_context_dir'] = ai_context_dir
        
        if markdown_dir:
            report['markdown_dir'] = str(markdown_dir)
        
        # Adicionar a versão detectada ao relatório
        if latest_version:
            report['version'] = latest_version
        
        # Adicionar ds_slug ao relatório
        report['ds_slug'] = ds_slug

        return report


def main():
    """Função principal do script."""
    parser = argparse.ArgumentParser(description="Extrair componentes do Storybook")
    parser.add_argument(
        "--config-file", 
        help="Arquivo de configuração (project_config.yaml)"
    )
    parser.add_argument(
        "--output-dir", 
        default="data/design_system",
        help="Diretório de saída base para os arquivos extraídos"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limitar o número de componentes a serem extraídos para teste."
    )
    parser.add_argument(
        "--force-update", 
        action="store_true",
        help="Forçar atualização do Design System"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--save-json",
        action="store_true", 
        help="Salvar componentes em formato JSON estruturado (padrão anterior)"
    )
    parser.add_argument(
        "--no-markdown",
        action="store_true", 
        help="NÃO salvar componentes em formato Markdown + JSON Index (padrão é salvar)"
    )
    parser.add_argument(
        "--category",
        type=str,
        choices=["components", "services", "templates", "animation", "flags", "icons", "illustration", "logos", "classes"],
        help="Extrair apenas componentes de uma categoria específica"
    )
    parser.add_argument(
        "--debug-urls",
        action="store_true",
        help="Mostrar URLs encontradas para debug"
    )
    parser.add_argument(
        "--skip-menu-expansion",
        action="store_true",
        help="Pular expansão de menus (para testes rápidos)"
    )
    
    args = parser.parse_args()
    
    # Configurar logging personalizado para o script
    global logger
    logger = setup_script_logging("storybook_extract", verbose=args.verbose)
    
    logger.info("Iniciando extração do Storybook...")
    
    try:
        report = extract_storybook_from_config(
            config_file=args.config_file, 
            limit=args.limit, 
            no_markdown=args.no_markdown,
            save_json=args.save_json,
            force_update=args.force_update,
            category_filter=args.category,
            debug_urls=args.debug_urls,
            skip_menu_expansion=args.skip_menu_expansion
        )
        
        if report.get("status") == "skipped":
            print("\n" + "="*50)
            print("EXTRAÇÃO IGNORADA")
            print("="*50)
            print(f"Motivo: {report.get('reason')}")
            print(f"Versão em cache: {report.get('cached_version')}")
            print(f"Versão mais recente: {report.get('latest_version')}")
            print("="*50)
            return
        
        print("\n" + "="*50)
        print("EXTRAÇÃO CONCLUÍDA")
        print("="*50)
        print(f"URLs visitadas: {report.get('urls_visited', 0)}")
        print(f"Tempo de extração: {report.get('extraction_time', 0):.2f}s")
        
        if 'markdown_dir' in report:
            print(f"Arquivos Markdown: {report['markdown_dir']}")
        
        if 'categorized_files' in report:
            print(f"Arquivos JSON: {len(report['categorized_files'])}")
        
        print("="*50)
        
    except Exception as e:
        logger.error(f"Erro durante extração: {e}", exc_info=True)
        print(f"\n❌ Erro durante extração: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()