# -*- coding: utf-8 -*-
"""
Utilitários específicos para Angular.

Este módulo contém funções auxiliares para geração de código Angular,
incluindo normalização de nomes de componentes, classes e arquivos.
"""

import re
from pathlib import Path

def normalize_to_pascal_case(name: str) -> str:
    """
    Converte nome para PascalCase para classes Angular.
    
    Args:
        name: Nome original
        
    Returns:
        Nome em PascalCase
    """
    # Remover caracteres especiais e normalizar
    clean_name = re.sub(r'[<>:"/\\|?*]', '', name)
    
    # Converter espaços e hífens para underscores temporariamente
    normalized = re.sub(r'[\s\-]+', '_', clean_name)
    
    # Converter para PascalCase
    words = normalized.split('_')
    pascal_case = ''.join(word.capitalize() for word in words if word)
    
    return pascal_case or 'Component'

def normalize_to_kebab_case(name: str) -> str:
    """
    Converte nome para kebab-case para nomes de arquivo Angular.
    
    Args:
        name: Nome original
        
    Returns:
        Nome em kebab-case
    """
    # Remover caracteres especiais e normalizar
    clean_name = re.sub(r'[<>:"/\\|?*]', '', name)
    
    # Converter espaços para hífens
    kebab_name = re.sub(r'\s+', '-', clean_name)
    
    # Remover hífens duplicados
    kebab_name = re.sub(r'-+', '-', kebab_name)
    
    # Remover hífens no início e fim
    kebab_name = kebab_name.strip('-')
    
    # Converter para minúsculo
    kebab_name = kebab_name.lower()
    
    return kebab_name or 'component'

def snake_to_kebab_case(snake_name: str) -> str:
    """
    Converte snake_case para kebab-case para arquivos Angular.
    
    Args:
        snake_name: Nome em snake_case (com underscores)
        
    Returns:
        Nome em kebab-case (com hífens)
    """
    # Converter underscores para hífens
    kebab_name = snake_name.replace('_', '-')
    
    # Remover hífens duplicados
    kebab_name = re.sub(r'-+', '-', kebab_name)
    
    # Remover hífens no início e fim
    kebab_name = kebab_name.strip('-')
    
    return kebab_name or 'component'

def generate_angular_component_files(component_name: str, html: str, ts: str, scss: str, output_dir: str) -> Path:
    """
    Salva arquivos do componente Angular em pasta específica.

    Args:
        component_name: Nome normalizado do componente (snake_case)
        html: Conteúdo HTML
        ts: Conteúdo TypeScript
        scss: Conteúdo SCSS
        output_dir: Diretório de saída

    Returns:
        Path do diretório do componente criado
    """
    from pathlib import Path


    # Converter snake_case para kebab-case para arquivos Angular
    kebab_name = snake_to_kebab_case(component_name)

    out_dir = Path(output_dir)

    # Criar pasta específica para o componente (kebab-case)
    component_dir = out_dir / kebab_name
    component_dir.mkdir(parents=True, exist_ok=True)

    # Salvar HTML
    html_path = component_dir / f"{kebab_name}.component.html"
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(html)
    print(f"✅ HTML salvo em {html_path}")

    # Salvar TS
    ts_path = component_dir / f"{kebab_name}.component.ts"
    with open(ts_path, 'w', encoding='utf-8') as f:
        f.write(ts)
    print(f"✅ TS salvo em {ts_path}")

    # Salvar SCSS
    scss_path = component_dir / f"{kebab_name}.component.scss"
    with open(scss_path, 'w', encoding='utf-8') as f:
        f.write(scss)
    print(f"✅ SCSS salvo em {scss_path}")

    # Debug: Verificar conteúdos após salvar
    with open(html_path, 'r', encoding='utf-8') as f:
        saved_html = f.read()
    with open(ts_path, 'r', encoding='utf-8') as f:
        saved_ts = f.read()
    with open(scss_path, 'r', encoding='utf-8') as f:
        saved_scss = f.read()

    return component_dir

 