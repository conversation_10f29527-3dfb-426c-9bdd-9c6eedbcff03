# -*- coding: utf-8 -*-
"""
Component Analyzer - Utilitários para análise de componentes Figma.

Este módulo contém funções para detectar tipos de componentes,
identificar wrappers e analisar estruturas de componentes.
"""

import re
from typing import List, Dict, Any
from src.utils.logging import get_logger

logger = get_logger(__name__)


class ComponentAnalyzer:
    """
    Utilitário para análise de componentes Figma.
    
    Contém métodos para detectar tipos, identificar wrappers
    e analisar estruturas de componentes.
    """
    
    @staticmethod
    def detect_component_type(component_name: str) -> str:
        """
        Detecta o tipo do componente baseado no nome.
        
        Args:
            component_name: Nome do componente
            
        Returns:
            Tipo do componente detectado
        """
        name = component_name.lower()
        
        if 'modal' in name or 'dialog' in name:
            return 'modal'
        elif 'form' in name or 'input' in name:
            return 'form'
        elif 'button' in name:
            return 'button'
        elif 'card' in name:
            return 'card'
        elif 'table' in name:
            return 'table'
        else:
            return 'component'
    
    @staticmethod
    def is_wrapper_component(component_name: str, all_components: List[str]) -> bool:
        """
        Detecta se o componente é um wrapper (arquivo principal).
        
        Args:
            component_name: Nome do componente atual
            all_components: Lista de todos os componentes
            
        Returns:
            True se for um wrapper, False caso contrário
        """
        # Se há outros componentes além do atual, este pode ser um wrapper
        other_components = [comp for comp in all_components if comp != component_name]
        
        # O wrapper é sempre o componente principal (primeiro na lista)
        is_main_component = bool(all_components) and component_name == all_components[0]

        return is_main_component and len(other_components) > 0
    
    @staticmethod
    def get_child_components(component_name: str, all_components: List[str]) -> List[str]:
        """
        Obtém a lista de componentes filhos.
        
        Args:
            component_name: Nome do componente atual
            all_components: Lista de todos os componentes
            
        Returns:
            Lista de componentes filhos
        """
        # Retornar todos os componentes exceto o atual
        return [comp for comp in all_components if comp != component_name]
    
    @staticmethod
    def sanitize_ai_response(response: str) -> str:
        """
        Sanitiza a resposta da IA removendo blocos de código desnecessários.

        Args:
            response: Resposta da IA para sanitizar

        Returns:
            Resposta sanitizada
        """
        if not response:
            return ""

        # Remover apenas os marcadores de início e fim de blocos de código
        # Preservar o conteúdo dentro dos blocos
        sanitized = response

        # Remover ```typescript, ```html, ```scss no início de linhas
        sanitized = re.sub(r'^```(?:typescript|html|scss|css|javascript|js|ts|angular|component)\s*$',
                          '', sanitized, flags=re.MULTILINE)

        # Remover ``` no final de linhas
        sanitized = re.sub(r'^\s*```\s*$', '', sanitized, flags=re.MULTILINE)

        # Remover linhas vazias extras
        sanitized = re.sub(r'\n\s*\n\s*\n', '\n\n', sanitized)

        # Remover espaços em branco no início e fim
        sanitized = sanitized.strip()

        # Se a resposta estiver vazia após sanitização, retornar fallback
        if not sanitized:
            return "<!-- Conteúdo gerado pela IA -->"

        return sanitized

    @staticmethod
    def normalize_component_name_for_figma(name: str) -> str:
        """
        Normaliza nome de componente para padrão Figma (snake_case).

        Mantém consistência com a extração do Figma que usa underscores.
        Baseado na função sanitize_name do file_utils.

        Args:
            name: Nome original do componente

        Returns:
            Nome normalizado em snake_case
        """
        # Remover caracteres especiais problemáticos para nomes de arquivo
        clean_name = re.sub(r'[<>:"/\\|?*]', '', name)

        # Converter espaços para underscores (como na sanitize_name)
        normalized = re.sub(r'\s+', '_', clean_name)

        # Converter hífens para underscores (como na sanitize_name)
        normalized = re.sub(r'-', '_', normalized)

        # Remover acentos (como na sanitize_name)
        char_map = {
            'á': 'a', 'à': 'a', 'ã': 'a', 'â': 'a', 'ä': 'a',
            'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e',
            'í': 'i', 'ì': 'i', 'î': 'i', 'ï': 'i',
            'ó': 'o', 'ò': 'o', 'õ': 'o', 'ô': 'o', 'ö': 'o',
            'ú': 'u', 'ù': 'u', 'û': 'u', 'ü': 'u',
            'ý': 'y', 'ÿ': 'y',
            'ñ': 'n',
            'ç': 'c'
        }

        for special, simple in char_map.items():
            normalized = normalized.replace(special, simple)
            normalized = normalized.replace(special.upper(), simple.upper())

        # Remover underscores duplicados
        normalized = re.sub(r'_+', '_', normalized)

        # Remover underscores no início e fim
        normalized = normalized.strip('_')

        # Converter para minúsculo para consistência
        normalized = normalized.lower()

        return normalized or 'unnamed_component'
