"""
Utilitário para resolver visibilidade de INSTANCE/COMPONENT via componentId e arquivos em /components.
"""
import json
from pathlib import Path

def is_component_instance_visible(node: dict, components_dir: Path) -> bool:
    """
    Dado um node INSTANCE/COMPONENT, busca o arquivo do componentId em components_dir,
    aplica o filtro de visibilidade e retorna True se o root for visível, False caso contrário.
    Se não encontrar o arquivo, assume visível (por segurança).
    """
    component_id = node.get('componentId')
    if not component_id:
        return True  # Não é INSTANCE/COMPONENT ou não tem referência
    # Procura arquivo pelo id (pode ser necessário mapear id para nome de arquivo)
    # Exemplo: arquivos salvos como <component_id>.json ou <component_name>.json
    # Aqui, tentamos por id
    comp_file = components_dir / f"{component_id}.json"
    if not comp_file.exists():
        # Tenta buscar por nome, se disponível
        comp_name = node.get('name')
        if comp_name:
            comp_file = components_dir / f"{comp_name}.json"
    if not comp_file.exists():
        return True  # Não encontrado, assume visível
    with open(comp_file, 'r', encoding='utf-8') as f:
        comp_data = json.load(f)
    # Espera-se que o root esteja em comp_data['figma_nodes'][0] ou similar
    root = comp_data.get('figma_nodes', [{}])[0]
    visible_root = filter_visible_nodes(root)
    return visible_root is not None

# Exemplo de uso:
# from pathlib import Path
# is_visible = is_component_instance_visible(node, Path('caminho/components'))

"""
Funções utilitárias para filtrar nodes Figma com visible: false em árvores de componentes extraídos.
"""

def filter_visible_nodes(node: dict) -> dict | None:
    """
    Retorna uma cópia do node (dict) sem nodes com 'visible': false em qualquer nível.
    Se o node em si for invisible, retorna None.
    """
    if not node.get('visible', True):
        return None
    node_copy = node.copy()
    if 'children' in node_copy and isinstance(node_copy['children'], list):
        filtered_children = []
        for child in node_copy['children']:
            filtered = filter_visible_nodes(child)
            if filtered is not None:
                filtered_children.append(filtered)
        node_copy['children'] = filtered_children
    return node_copy