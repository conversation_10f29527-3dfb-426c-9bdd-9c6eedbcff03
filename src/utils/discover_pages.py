#!/usr/bin/env python3
"""
Script para descoberta de páginas do Figma.

Este script analisa um arquivo Figma e descobre todas as páginas disponíveis,
fornecendo informações detalhadas sobre cada página.
"""

import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.figma.figma_discovery import FigmaDiscovery
from src.utils.config import load_config
from src.utils.logging import setup_logging

logger = logging.getLogger(__name__)


def discover_pages(file_key: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Descobre páginas de um arquivo Figma.
    
    Args:
        file_key: Chave do arquivo Figma
        config: Configuração da API
        
    Returns:
        Dicionário com informações das páginas
    """
    logger.info(f"Iniciando descoberta de páginas para arquivo: {file_key}")
    
    # Inicializar discovery
    discovery = FigmaDiscovery(config)
    
    try:
        # Descobrir páginas
        pages = discovery.discover_pages(file_key)
        
        logger.info(f"Descobertas {len(pages)} páginas")
        
        # Preparar resultado
        result = {
            "file_key": file_key,
            "total_pages": len(pages),
            "pages": []
        }
        
        for page in pages:
            page_info = {
                "id": page.id,
                "name": page.name,
                "type": page.type,
                "node_type": page.node_type.value,
                "level": page.level,
                "children_count": page.children_count,
                "has_layout": page.has_layout,
                "has_content": page.has_content,
                "estimated_complexity": page.estimated_complexity,
                "description": page.description
            }
            result["pages"].append(page_info)
            
            logger.info(f"📄 Página: {page.name} ({page.children_count} elementos)")
        
        return result
        
    except Exception as e:
        logger.error(f"Erro na descoberta de páginas: {e}")
        raise


def main():
    """Função principal."""
    # Setup logging
    setup_logging()
    
    # Carregar configuração
    config = load_config()
    
    if not config:
        logger.error("Configuração não encontrada")
        sys.exit(1)
    
    # Verificar argumentos
    if len(sys.argv) < 2:
        print("Uso: python discover_pages.py <file_key>")
        print("Exemplo: python discover_pages.py abc123def456")
        sys.exit(1)
    
    file_key = sys.argv[1]
    
    try:
        # Executar descoberta
        result = discover_pages(file_key, config)
        
        # Salvar resultado
        output_file = f"discovered_pages_{file_key}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print("\n✅ Descoberta concluída!")
        print(f"📁 Resultado salvo em: {output_file}")
        print(f"📊 Total de páginas: {result['total_pages']}")
        
        # Mostrar resumo
        print("\n📋 RESUMO DAS PÁGINAS:")
        print("-" * 60)
        for page in result["pages"]:
            complexity_icon = {
                "low": "🟢",
                "medium": "🟡",
                "high": "🔴"
            }.get(page["estimated_complexity"], "⚪")
            
            print(f"{complexity_icon} {page['name']}")
            print(f"   └─ {page['children_count']} elementos")
            print(f"   └─ Complexidade: {page['estimated_complexity']}")
            print()
        
    except Exception as e:
        logger.error(f"Erro na execução: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
