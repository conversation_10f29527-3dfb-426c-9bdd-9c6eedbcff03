# -*- coding: utf-8 -*-
"""
Utilitários específicos para processamento de dados do Figma.

Este módulo contém funções auxiliares para processamento de dados do Figma,
incluindo extração de CSS, mapeamento de cores e análise de estrutura.
"""

import json
from typing import Dict, Any, List
from pathlib import Path

def extract_css_from_figma(figma_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extrai propriedades CSS de um componente do Figma.
    
    Args:
        figma_data: Dados do componente do Figma
        
    Returns:
        Dicionário com propriedades CSS
    """
    css_properties = {}
    
    # Extrair CSS direto se disponível
    if 'css' in figma_data:
        css_properties.update(figma_data['css'])
    
    # Extrair propriedades de layout
    if 'absoluteBoundingBox' in figma_data:
        bbox = figma_data['absoluteBoundingBox']
        css_properties.update({
            'width': f"{bbox.get('width', 0)}px",
            'height': f"{bbox.get('height', 0)}px",
            'x': f"{bbox.get('x', 0)}px",
            'y': f"{bbox.get('y', 0)}px"
        })
    
    # Extrair propriedades de estilo
    if 'fills' in figma_data and figma_data['fills']:
        fill = figma_data['fills'][0]
        if fill.get('type') == 'SOLID':
            color = fill.get('color', {})
            if color:
                r = int(color.get('r', 0) * 255)
                g = int(color.get('g', 0) * 255)
                b = int(color.get('b', 0) * 255)
                a = color.get('a', 1)
                css_properties['background-color'] = f"rgba({r}, {g}, {b}, {a})"
    
    # Extrair propriedades de texto
    if 'style' in figma_data:
        style = figma_data['style']
        if 'fontSize' in style:
            css_properties['font-size'] = f"{style['fontSize']}px"
        if 'fontFamily' in style:
            css_properties['font-family'] = style['fontFamily']
        if 'fontWeight' in style:
            css_properties['font-weight'] = style['fontWeight']
    
    return css_properties

def extract_component_sets(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extrai componentSets de um componente do Figma.
    
    Args:
        figma_data: Dados do componente do Figma
        
    Returns:
        Lista de componentSets encontrados
    """
    component_sets = []
    
    if 'componentSets' in figma_data:
        for set_id, set_data in figma_data['componentSets'].items():
            component_sets.append({
                'id': set_id,
                'name': set_data.get('name', ''),
                'description': set_data.get('description', ''),
                'components': set_data.get('components', {})
            })
    
    return component_sets

def extract_components(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extrai components de um componente do Figma.
    
    Args:
        figma_data: Dados do componente do Figma
        
    Returns:
        Lista de components encontrados
    """
    components = []
    
    if 'components' in figma_data:
        for comp_id, comp_data in figma_data['components'].items():
            components.append({
                'id': comp_id,
                'name': comp_data.get('name', ''),
                'description': comp_data.get('description', ''),
                'key': comp_data.get('key', '')
            })
    
    return components

def extract_children_recursive(figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extrai children recursivamente de um componente do Figma.
    
    Args:
        figma_data: Dados do componente do Figma
        
    Returns:
        Lista de children encontrados
    """
    children = []
    
    if 'children' in figma_data:
        for child in figma_data['children']:
            child_data = {
                'id': child.get('id', ''),
                'name': child.get('name', ''),
                'type': child.get('type', ''),
                'visible': child.get('visible', True),
                'css': extract_css_from_figma(child)
            }
            
            # Recursivamente extrair children dos children
            if 'children' in child:
                child_data['children'] = extract_children_recursive(child)
            
            children.append(child_data)
    
    return children

def analyze_figma_structure(figma_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analisa a estrutura de um componente do Figma.
    
    Args:
        figma_data: Dados do componente do Figma
        
    Returns:
        Dicionário com análise da estrutura
    """
    structure_info = {
        'has_children': False,
        'children_count': 0,
        'has_components': False,
        'components_count': 0,
        'has_component_sets': False,
        'component_sets_count': 0,
        'types_found': set(),
        'structure_type': 'unknown'
    }
    
    # Verificar children
    if 'children' in figma_data and figma_data['children']:
        structure_info['has_children'] = True
        structure_info['children_count'] = len(figma_data['children'])
        
        # Analisar tipos dos children
        for child in figma_data['children']:
            child_type = child.get('type', '')
            structure_info['types_found'].add(child_type)
    
    # Verificar components
    if 'components' in figma_data and figma_data['components']:
        structure_info['has_components'] = True
        structure_info['components_count'] = len(figma_data['components'])
    
    # Verificar componentSets
    if 'componentSets' in figma_data and figma_data['componentSets']:
        structure_info['has_component_sets'] = True
        structure_info['component_sets_count'] = len(figma_data['componentSets'])
    
    # Determinar tipo de estrutura
    if structure_info['has_component_sets']:
        structure_info['structure_type'] = 'component_set'
    elif structure_info['has_components']:
        structure_info['structure_type'] = 'component'
    elif structure_info['has_children']:
        structure_info['structure_type'] = 'container'
    else:
        structure_info['structure_type'] = 'element'
    
    # Converter set para lista para serialização
    structure_info['types_found'] = list(structure_info['types_found'])
    
    return structure_info

def load_design_system_colors() -> Dict[str, Any]:
    """
    Carrega dados de cores do Design System.
    
    Returns:
        Dicionário com dados de cores do Design System
    """
    try:
        # Tentar carregar dados de cores do Design System
        ds_path = Path("data/design_system")
        if ds_path.exists():
            # Buscar por arquivos de cores
            color_files = list(ds_path.rglob("*colors*.json")) + list(ds_path.rglob("*cores*.json"))
            
            if color_files:
                # Carregar primeiro arquivo encontrado
                color_file = color_files[0]
                with open(color_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        
        # Fallback: dados básicos de cores
        return {
            "examples": {
                "colors": [
                    {
                        "name": "Primary",
                        "class": "brad-color-primary",
                        "hex": "#cc092f",
                        "rgb": "rgb(204, 9, 47)"
                    },
                    {
                        "name": "Secondary",
                        "class": "brad-color-secondary", 
                        "hex": "#666666",
                        "rgb": "rgb(102, 102, 102)"
                    },
                    {
                        "name": "Background",
                        "class": "brad-color-background",
                        "hex": "#ffffff",
                        "rgb": "rgb(255, 255, 255)"
                    }
                ]
            }
        }
        
    except Exception:
        # Retornar dados básicos em caso de erro
        return {
            "examples": {
                "colors": [
                    {
                        "name": "Primary",
                        "class": "brad-color-primary",
                        "hex": "#cc092f",
                        "rgb": "rgb(204, 9, 47)"
                    }
                ]
            }
        }

def extract_structure_info(figma_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extrai informações de estrutura do Figma analisando elementos HTML.
    
    Args:
        figma_data: Dados do Figma
        
    Returns:
        Dict com informações de estrutura
    """
    structure_info = {
        "has_header": False,
        "has_form": False,
        "has_actions": False,
        "sections": [],
        "layout_type": "unknown",
        "header_content": None,
        "actions_content": None
    }
    
    try:
        # Analisar HTML para identificar elementos
        html_content = figma_data.get('html_structure', '').lower()
        
        # Detectar formulários baseado em elementos de input
        form_elements = [
            'input', 'select', 'textarea', 'form', 'checkbox', 'radio',
            'figma-input', 'figma-select', 'figma-textarea'
        ]
        
        has_form_elements = any(element in html_content for element in form_elements)
        if has_form_elements:
            structure_info["has_form"] = True
            structure_info["sections"].append({
                "type": "form",
                "name": "Form Section",
                "content": {"has_form_elements": True}
            })
        
        # Detectar ações baseado em botões
        action_elements = [
            'button', 'figma-button', 'figma-instance', 'click', 'action'
        ]
        
        has_action_elements = any(element in html_content for element in action_elements)
        if has_action_elements:
            structure_info["has_actions"] = True
            structure_info["sections"].append({
                "type": "actions",
                "name": "Actions Section",
                "content": {"has_action_elements": True}
            })
        
        # Detectar headers baseado em elementos de título ou cabeçalho
        header_elements = [
            'header', 'title', 'h1', 'h2', 'h3', 'figma-text',
            'modal-title', 'dialog-title'
        ]
        
        has_header_elements = any(element in html_content for element in header_elements)
        if has_header_elements:
            structure_info["has_header"] = True
            structure_info["sections"].append({
                "type": "header",
                "name": "Header Section",
                "content": {"has_header_elements": True}
            })
        
        # Determinar tipo de layout baseado na combinação de elementos
        if structure_info["has_header"] and structure_info["has_form"] and structure_info["has_actions"]:
            structure_info["layout_type"] = "modal_with_header"
        elif structure_info["has_form"] and structure_info["has_actions"]:
            structure_info["layout_type"] = "modal_simple"
        elif structure_info["has_form"]:
            structure_info["layout_type"] = "form_only"
        elif structure_info["has_actions"]:
            structure_info["layout_type"] = "actions_only"
        else:
            structure_info["layout_type"] = "component"
        
        return structure_info
        
    except Exception:
        # Retornar estrutura básica em caso de erro
        return structure_info 