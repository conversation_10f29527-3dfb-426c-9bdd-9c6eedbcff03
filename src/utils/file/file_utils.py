import re
import unicodedata

def sanitize_name(name: str) -> str:
    """
    Sanitizes a string to be used as a file name.
    - Trims whitespace
    - Removes text after a /
    - Replaces " - " with "_"
    - Replaces special characters with simple versions
    - Keeps only lowercase letters, numbers, and underscores
    """
    # Trim whitespace first
    name = name.strip()
    
    if '/' in name:
        name = name.split('/')[0]

    # Replace " - " pattern with "_"
    name = re.sub(r'\s*-\s*', '_', name)

    # Replace "-" pattern with "_"
    name = re.sub(r'-', '_', name)

    # Normalize to remove accents and special chars
    char_map = {
        'á': 'a', 'à': 'a', 'ã': 'a', 'â': 'a', 'ä': 'a',
        'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e',
        'í': 'i', 'ì': 'i', 'î': 'i', 'ï': 'i',
        'ó': 'o', 'ò': 'o', 'õ': 'o', 'ô': 'o', 'ö': 'o',
        'ú': 'u', 'ù': 'u', 'û': 'u', 'ü': 'u',
        'ý': 'y', 'ÿ': 'y',
        'ñ': 'n',
        'ç': 'c'
    }
    
    for special, simple in char_map.items():
        name = name.replace(special, simple)
        name = name.replace(special.upper(), simple.upper())

    # Replace remaining spaces and invalid chars with underscore
    sanitized_name = re.sub(r'\s+', '_', name)
    sanitized_name = re.sub(r'[^a-zA-Z0-9_]', '', sanitized_name)
    
    return sanitized_name.lower()

def sanitize_for_filename(name: str) -> str:
    """
    Sanitizes a string to be a valid filename.
    """
    # Normalize unicode characters
    name = unicodedata.normalize('NFKD', name).encode('ascii', 'ignore').decode('ascii')
    # Replace spaces and slashes with underscores
    name = re.sub(r'[\s/]+', '_', name)
    # Remove any characters that are not alphanumeric, underscore, or hyphen
    name = re.sub(r'[^a-zA-Z0-9_-]', '', name)
    return name.lower()

# Pattern for validating Figma node IDs
node_id_pattern = re.compile(r'^\d+(-\d+)*(:\d+(-\d+)*)?$')
