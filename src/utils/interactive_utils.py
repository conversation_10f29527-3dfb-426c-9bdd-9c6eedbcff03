#!/usr/bin/env python3
"""
Utilitários para modo interativo do gerador de código.
"""

import sys
from src.utils.logging import get_logger

logger = get_logger(__name__)

def interactive_mode(generator=None):
    """Modo interativo para facilitar o uso."""
    
    print("\n🚀 GERADOR DE CÓDIGO ANGULAR")
    print("=" * 60)
    print("Fluxo: Leitura → Mapeamento Design System → Geração com IA")
    print("")
    
    try:
        # Usar gerador existente ou inicializar um novo
        if generator is None:
            print("⚙️  Inicializando...")
            from src.scripts.code_generator import CodeGeneratorScript
            generator = CodeGeneratorScript()
        
        # Listar extrações disponíveis
        extractions = generator.list_available_extractions()
        
        if not extractions:
            print("\n❌ Nenhuma extração do Figma encontrada!")
            print("💡 Execute primeiro o script de extração do Figma para criar dados.")
            return
        
        print(f"\n📋 EXTRAÇÕES DISPONÍVEIS ({len(extractions)}):")
        for i, extraction in enumerate(extractions, 1):
            # Extrair informações da extração
            parts = extraction.split("/")
            project = parts[-2] if len(parts) >= 2 else "unknown"
            component = parts[-1] if len(parts) >= 1 else "unknown"
            print(f"  {i}. {project} / {component}")
            print(f"     📁 {extraction}")
        
        # Menu de seleção
        print("\n🎯 OPÇÕES:")
        print("  0. Sair")
        print("  1-{}: Processar extração específica".format(len(extractions)))
        
        choice = input("\n👉 Escolha uma opção: ").strip()
        
        if choice == "0":
            print("👋 Encerrando...")
            return
        
        elif choice.isdigit() and 1 <= int(choice) <= len(extractions):
            extraction_path = extractions[int(choice) - 1]
            
            process_single_extraction(generator, extraction_path, raw_only=False)
        
        else:
            print("❌ Opção inválida!")
    
    except Exception as e:
        error_message = str(e)
        if "Índice do Design System não encontrado" in error_message:
            sys.exit(1)  # Encerrar aplicação com código de erro
        else:
            logger.error(f"❌ Erro no modo interativo: {e}")
            print(f"❌ Erro: {e}")

def process_single_extraction(generator, extraction_path: str, raw_only: bool = False):
    """Processa uma extração específica."""
    try:
        print(f"\n🚀 Processando: {extraction_path}")
        
        if raw_only:
            print("📝 Modo: Apenas processamento do Figma (raw_only)")
        
        # Processar extração
        result = generator.process_extraction(extraction_path, raw_only=raw_only)
        
        print("✅ Processamento concluído!")
        
        if raw_only:
            print(f"📁 Arquivos gerados em: {generator.output_dir}/figma_processed/")
        else:
            print(f"📁 Arquivos gerados em: {generator.output_dir}/angular/")
        
    except Exception as e:
        error_message = str(e)
        if "Índice do Design System não encontrado" in error_message:
            sys.exit(1)  # Encerrar aplicação com código de erro
        else:
            logger.error(f"❌ Erro ao processar extração: {e}")

def process_all_extractions(generator, extractions: list, raw_only: bool = False):
    """Processa todas as extrações disponíveis."""
    print(f"\n🚀 Processando {len(extractions)} extrações...")
    
    if raw_only:
        print("📝 Modo: Apenas figma_processed (raw_only)")
    else:
        print("📝 Modo: Completo (HTML + TS + SCSS)")
    
    for i, extraction_path in enumerate(extractions, 1):
        try:
            print(f"\n📦 [{i}/{len(extractions)}] Processando: {extraction_path}")
            result = generator.process_extraction(extraction_path, raw_only=raw_only)
            print(f"✅ Concluído: {extraction_path}")
            
        except Exception as e:
            error_message = str(e)
            if "Índice do Design System não encontrado" in error_message:
                sys.exit(1)  # Encerrar aplicação com código de erro
            else:
                logger.error(f"❌ Erro ao processar {extraction_path}: {e}")
                print(f"❌ Erro em {extraction_path}: {e}")
                continue
    
    print("\n✅ Processamento de todas as extrações concluído!") 