"""
Detector de padrões genérico que identifica estruturas sem depender de palavras específicas.
Usa análise estatística e estrutural para identificar componentes.
"""

import re
from typing import Dict, List, Any
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class PatternMatch:
    """Representa um padrão identificado."""
    type: str
    confidence: float
    evidence: List[str]
    metadata: Dict[str, Any]

class GenericPatternDetector:
    """
    Detector de padrões que identifica estruturas sem depender de palavras específicas.
    """
    
    def __init__(self):
        # Padrões estruturais genéricos
        self.structure_patterns = {
            'table': self._detect_table_pattern,
            'form': self._detect_form_pattern,
            'navigation': self._detect_navigation_pattern,
            'list': self._detect_list_pattern,
            'card': self._detect_card_pattern,
            'header': self._detect_header_pattern,
            'footer': self._detect_footer_pattern
        }
        
        # Padrões de texto genéricos (independente de idioma)
        self.text_patterns = {
            'date': r'\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}',
            'time': r'\d{1,2}:\d{2}(:\d{2})?',
            'number': r'\d+([.,]\d+)?',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'currency': r'[R$€£¥$]\s*\d+([.,]\d{2})?',
            'percentage': r'\d+([.,]\d+)?%',
            'placeholder': r'(xxx|000|###|\.\.\.|placeholder)',
        }
    
    def detect_patterns(self, figma_data: Dict[str, Any]) -> List[PatternMatch]:
        """
        Detecta padrões na estrutura do Figma sem depender de palavras específicas.
        """
        patterns = []
        
        # Extrair dados estruturais
        structure_info = self._extract_structure_info(figma_data)
        
        # Testar cada tipo de padrão
        for pattern_type, detector_func in self.structure_patterns.items():
            match = detector_func(structure_info)
            if match:
                patterns.append(match)
        
        # Ordenar por confiança
        patterns.sort(key=lambda x: x.confidence, reverse=True)
        
        return patterns
    
    def _extract_structure_info(self, figma_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai informações estruturais do componente Figma."""
        info = {
            'texts': [],
            'nodes': [],
            'layout_info': {},
            'hierarchy': [],
            'repeated_structures': [],
            'interaction_elements': []
        }
        
        def process_node(node, level=0):
            node_info = {
                'type': node.get('type', ''),
                'name': node.get('name', ''),
                'level': level,
                'has_children': len(node.get('children', [])) > 0,
                'layout_mode': node.get('layoutMode'),
                'absolute_bounding_box': node.get('absoluteBoundingBox', {}),
                'fills': node.get('fills', []),
                'strokes': node.get('strokes', [])
            }
            
            info['nodes'].append(node_info)
            
            # Extrair textos
            if node.get('type') == 'TEXT':
                text_content = node.get('characters', '').strip()
                if text_content:
                    text_info = {
                        'text': text_content,
                        'name': node.get('name', ''),
                        'level': level,
                        'style': node.get('style', {}),
                        'patterns': self._analyze_text_patterns(text_content)
                    }
                    info['texts'].append(text_info)
            
            # Detectar elementos de interação
            if any(keyword in node.get('name', '').lower() for keyword in ['button', 'btn', 'click', 'action']):
                info['interaction_elements'].append(node_info)
            
            # Processar filhos
            for child in node.get('children', []):
                process_node(child, level + 1)
        
        # Processar todos os nós do Figma
        for figma_node in figma_data.get('figma_nodes', []):
            process_node(figma_node)
        
        # Detectar estruturas repetidas
        info['repeated_structures'] = self._detect_repeated_structures(info['nodes'])
        
        return info
    
    def _analyze_text_patterns(self, text: str) -> List[str]:
        """Analisa padrões em um texto específico."""
        patterns = []
        
        for pattern_name, pattern_regex in self.text_patterns.items():
            if re.search(pattern_regex, text, re.IGNORECASE):
                patterns.append(pattern_name)
        
        return patterns
    
    def _detect_repeated_structures(self, nodes: List[Dict]) -> List[Dict]:
        """Detecta estruturas que se repetem (indicativo de listas/tabelas)."""
        repeated = []
        
        # Agrupar nós por tipo e nome similar
        groups = defaultdict(list)
        for node in nodes:
            # Criar chave baseada no tipo e estrutura do nome
            key = (
                node['type'],
                self._normalize_name(node['name']),
                node['has_children']
            )
            groups[key].append(node)
        
        # Identificar grupos com repetições significativas
        for key, group_nodes in groups.items():
            if len(group_nodes) >= 3:  # 3+ repetições indicam padrão
                repeated.append({
                    'pattern_key': key,
                    'count': len(group_nodes),
                    'nodes': group_nodes,
                    'confidence': min(1.0, len(group_nodes) / 10.0)  # Máximo 1.0 com 10+ repetições
                })
        
        return repeated
    
    def _normalize_name(self, name: str) -> str:
        """Normaliza nomes para detectar padrões (remove números, etc)."""
        # Remove números no final (Frame 1, Frame 2, etc)
        normalized = re.sub(r'\s*\d+$', '', name)
        # Remove caracteres especiais
        normalized = re.sub(r'[^\w\s]', '', normalized)
        return normalized.strip().lower()
    
    def _detect_table_pattern(self, structure_info: Dict[str, Any]) -> PatternMatch:
        """Detecta padrão de tabela baseado na estrutura."""
        evidence = []
        confidence = 0.0
        
        # 1. Verificar estruturas repetidas (linhas)
        repeated_structures = structure_info['repeated_structures']
        max_repetitions = 0
        for repeated in repeated_structures:
            if repeated['count'] >= 3:
                max_repetitions = max(max_repetitions, repeated['count'])
                evidence.append(f"Estrutura repetida {repeated['count']} vezes")
                confidence += 0.3
        
        # 2. Verificar layout em grid (nós alinhados)
        nodes_with_bounds = [n for n in structure_info['nodes'] if n.get('absolute_bounding_box')]
        if len(nodes_with_bounds) >= 6:  # Mínimo para uma tabela pequena
            grid_score = self._calculate_grid_alignment(nodes_with_bounds)
            if grid_score > 0.7:
                evidence.append(f"Alinhamento em grid detectado (score: {grid_score:.2f})")
                confidence += 0.4
        
        # 3. Verificar padrões de texto tabulares
        texts = structure_info['texts']
        data_patterns = [t for t in texts if any(p in t['patterns'] for p in ['date', 'number', 'placeholder'])]
        if len(data_patterns) >= 5:
            evidence.append(f"{len(data_patterns)} textos com padrões de dados")
            confidence += 0.2
        
        # 4. Verificar elementos de ação (botões em células)
        action_elements = structure_info['interaction_elements']
        if len(action_elements) >= 2:
            evidence.append(f"{len(action_elements)} elementos de ação")
            confidence += 0.1
        
        if confidence >= 0.5:
            return PatternMatch(
                type='table',
                confidence=min(confidence, 1.0),
                evidence=evidence,
                metadata={
                    'estimated_rows': max_repetitions,
                    'estimated_columns': self._estimate_columns(structure_info),
                    'has_actions': len(action_elements) > 0,
                    'data_types': self._identify_data_types(texts)
                }
            )
        
        return None
    
    def _detect_form_pattern(self, structure_info: Dict[str, Any]) -> PatternMatch:
        """Detecta padrão de formulário."""
        evidence = []
        confidence = 0.0
        
        # Buscar por elementos típicos de formulário
        nodes = structure_info['nodes']
        
        # Input-like elements
        input_nodes = [n for n in nodes if any(keyword in n['name'].lower() 
                      for keyword in ['input', 'field', 'text', 'select', 'dropdown'])]
        
        if len(input_nodes) >= 2:
            evidence.append(f"{len(input_nodes)} elementos de entrada")
            confidence += 0.4
        
        # Labels
        label_texts = [t for t in structure_info['texts'] if len(t['text'].split()) <= 3]
        if len(label_texts) >= 2:
            evidence.append(f"{len(label_texts)} possíveis labels")
            confidence += 0.2
        
        # Botões de ação
        action_elements = structure_info['interaction_elements']
        if len(action_elements) >= 1:
            evidence.append(f"{len(action_elements)} botões de ação")
            confidence += 0.3
        
        # Layout vertical (típico de formulários)
        vertical_layout = any(n.get('layout_mode') == 'VERTICAL' for n in nodes)
        if vertical_layout:
            evidence.append("Layout vertical detectado")
            confidence += 0.1
        
        if confidence >= 0.5:
            return PatternMatch(
                type='form',
                confidence=min(confidence, 1.0),
                evidence=evidence,
                metadata={
                    'field_count': len(input_nodes),
                    'has_validation': False,  # Seria detectado por outros padrões
                    'layout': 'vertical' if vertical_layout else 'horizontal'
                }
            )
        
        return None
    
    def _detect_navigation_pattern(self, structure_info: Dict[str, Any]) -> PatternMatch:
        """Detecta padrão de navegação."""
        evidence = []
        confidence = 0.0
        
        # Buscar elementos horizontais repetidos
        repeated = structure_info['repeated_structures']
        horizontal_repeated = [r for r in repeated if r['count'] >= 2 and r['count'] <= 10]
        
        if horizontal_repeated:
            evidence.append("Elementos horizontais repetidos")
            confidence += 0.4
        
        # Textos curtos (típicos de menus)
        short_texts = [t for t in structure_info['texts'] if len(t['text'].split()) <= 2]
        if len(short_texts) >= 3:
            evidence.append(f"{len(short_texts)} textos curtos (menu items)")
            confidence += 0.3
        
        # Layout horizontal
        horizontal_layout = any(n.get('layout_mode') == 'HORIZONTAL' for n in structure_info['nodes'])
        if horizontal_layout:
            evidence.append("Layout horizontal detectado")
            confidence += 0.2
        
        # Elementos com indicação de estado ativo
        active_indicators = [n for n in structure_info['nodes'] 
                           if any(keyword in n['name'].lower() for keyword in ['active', 'selected', 'current'])]
        if active_indicators:
            evidence.append("Indicadores de estado ativo")
            confidence += 0.1
        
        if confidence >= 0.5:
            return PatternMatch(
                type='navigation',
                confidence=min(confidence, 1.0),
                evidence=evidence,
                metadata={
                    'item_count': len(short_texts),
                    'orientation': 'horizontal' if horizontal_layout else 'vertical'
                }
            )
        
        return None
    
    def _detect_list_pattern(self, structure_info: Dict[str, Any]) -> PatternMatch:
        """Detecta padrão de lista."""
        evidence = []
        confidence = 0.0
        
        # Estruturas repetidas em layout vertical
        repeated = structure_info['repeated_structures']
        vertical_repeated = [r for r in repeated if r['count'] >= 3]
        
        if vertical_repeated:
            max_count = max(r['count'] for r in vertical_repeated)
            evidence.append(f"Lista com {max_count} itens repetidos")
            confidence += 0.5
        
        # Layout vertical
        vertical_layout = any(n.get('layout_mode') == 'VERTICAL' for n in structure_info['nodes'])
        if vertical_layout:
            evidence.append("Layout vertical")
            confidence += 0.2
        
        # Textos diversos (conteúdo da lista)
        texts = structure_info['texts']
        if len(texts) >= 3:
            evidence.append(f"{len(texts)} itens de texto")
            confidence += 0.2
        
        if confidence >= 0.5:
            return PatternMatch(
                type='list',
                confidence=min(confidence, 1.0),
                evidence=evidence,
                metadata={
                    'item_count': max(r['count'] for r in vertical_repeated) if vertical_repeated else len(texts),
                    'orientation': 'vertical'
                }
            )
        
        return None
    
    def _detect_card_pattern(self, structure_info: Dict[str, Any]) -> PatternMatch:
        """Detecta padrão de card."""
        # Implementação simplificada - pode ser expandida
        return None
    
    def _detect_header_pattern(self, structure_info: Dict[str, Any]) -> PatternMatch:
        """Detecta padrão de cabeçalho."""
        # Implementação simplificada - pode ser expandida
        return None
    
    def _detect_footer_pattern(self, structure_info: Dict[str, Any]) -> PatternMatch:
        """Detecta padrão de rodapé."""
        # Implementação simplificada - pode ser expandida
        return None
    
    def _calculate_grid_alignment(self, nodes: List[Dict]) -> float:
        """Calcula score de alinhamento em grid."""
        if len(nodes) < 4:
            return 0.0
        
        # Agrupar por coordenadas Y (linhas) e X (colunas)
        y_positions = []
        x_positions = []
        
        for node in nodes:
            bounds = node.get('absolute_bounding_box', {})
            if bounds:
                y_positions.append(bounds.get('y', 0))
                x_positions.append(bounds.get('x', 0))
        
        if not y_positions or not x_positions:
            return 0.0
        
        # Calcular variação nas posições
        y_groups = self._group_similar_values(y_positions, tolerance=10)
        x_groups = self._group_similar_values(x_positions, tolerance=10)
        
        # Score baseado no número de linhas e colunas distintas
        grid_score = min(len(y_groups) / 5.0, 1.0) * min(len(x_groups) / 3.0, 1.0)
        
        return grid_score
    
    def _group_similar_values(self, values: List[float], tolerance: float = 10) -> List[List[float]]:
        """Agrupa valores similares dentro de uma tolerância."""
        if not values:
            return []
        
        sorted_values = sorted(values)
        groups = []
        current_group = [sorted_values[0]]
        
        for value in sorted_values[1:]:
            if abs(value - current_group[-1]) <= tolerance:
                current_group.append(value)
            else:
                groups.append(current_group)
                current_group = [value]
        
        groups.append(current_group)
        return groups
    
    def _estimate_columns(self, structure_info: Dict[str, Any]) -> int:
        """Estima o número de colunas em uma tabela."""
        # Implementação simplificada baseada em elementos repetidos
        repeated = structure_info['repeated_structures']
        if repeated:
            # Assumir que o maior grupo repetido representa as linhas
            max_repeated = max(repeated, key=lambda x: x['count'])
            # Estimar colunas baseado no total de elementos / linhas
            total_elements = len(structure_info['texts'])
            estimated_cols = total_elements // max_repeated['count'] if max_repeated['count'] > 0 else 1
            return max(1, min(estimated_cols, 10))  # Entre 1 e 10 colunas
        
        return 3  # Default
    
    def _identify_data_types(self, texts: List[Dict]) -> List[str]:
        """Identifica os tipos de dados presentes nos textos."""
        data_types = set()
        
        for text_info in texts:
            patterns = text_info.get('patterns', [])
            data_types.update(patterns)
        
        return list(data_types)

def main():
    """Teste do detector de padrões genérico."""
    import json
    
    # Testar com dados reais
    figma_file = "data/figma_discovery/sat-contabilidade/base/components/table.json"
    
    try:
        with open(figma_file, 'r', encoding='utf-8') as f:
            figma_data = json.load(f)
        
        detector = GenericPatternDetector()
        patterns = detector.detect_patterns(figma_data)
        
        print("🔍 PADRÕES DETECTADOS (Análise Genérica):")
        print("=" * 50)
        
        for pattern in patterns:
            print(f"\n🎯 {pattern.type.upper()}")
            print(f"   Confiança: {pattern.confidence:.2f}")
            print("   Evidências:")
            for evidence in pattern.evidence:
                print(f"     • {evidence}")
            print(f"   Metadados: {pattern.metadata}")
    
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    main()
