"""
Smart Pattern Detector - Detecção inteligente de padrões baseada em:
1. Nome do componente
2. Estrutura interna
3. Conteúdo textual
4. Contexto do design system
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from src.utils.config import Config<PERSON>oader

@dataclass
class SmartPattern:
    pattern_type: str
    confidence: float
    component_classes: List[str]
    evidence: List[str]
    ds_component: Optional[str] = None

class SmartPatternDetector:
    """
    Detector aprimorado que combina análise de nome, estrutura e design system.
    """
    
    def __init__(self, config_path: str = "project_config.yaml"):
        self.config = ConfigLoader(config_path).load_config()
        self.ds_slug = self.config.get("design_system", {}).get("slug", "")
        self.ds_path = f"data/design_system/{self.ds_slug}"
        self.ds_mapping = self._load_design_system_mapping()
    
    def _load_design_system_mapping(self) -> Dict[str, Any]:
        """Carrega mapeamento do design system."""
        mapping = {}
        
        # Carregar storybook_components.json
        storybook_path = Path(f"{self.ds_path}/storybook_components.json")
        if storybook_path.exists():
            try:
                with open(storybook_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    mapping['storybook'] = self._extract_component_mapping(data)
            except Exception:
                pass
        
        # Carregar main_docs.json
        docs_path = Path(f"{self.ds_path}/main_docs.json")
        if docs_path.exists():
            try:
                with open(docs_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    mapping['docs'] = data
            except Exception:
                pass
        
        return mapping
    
    def _extract_component_mapping(self, storybook_data: Dict) -> Dict[str, Any]:
        """Extrai mapeamento de componentes do Storybook."""
        component_mapping = {}
        
        if 'components' not in storybook_data:
            return component_mapping
        
        for component in storybook_data['components']:
            name = component.get('name', '').lower()
            classes = component.get('classes', [])
            
            # Mapear componentes conhecidos
            if 'dropdown' in name:
                component_mapping['dropdown'] = {
                    'classes': [c for c in classes if 'brad-dropdown' in c],
                    'base_class': 'brad-dropdown'
                }
            elif 'button' in name:
                component_mapping['button'] = {
                    'classes': [c for c in classes if 'brad-btn' in c],
                    'base_class': 'brad-btn'
                }
            elif 'table' in name:
                component_mapping['table'] = {
                    'classes': [c for c in classes if 'brad-table' in c],
                    'base_class': 'brad-table'
                }
            elif 'form' in name:
                component_mapping['form'] = {
                    'classes': [c for c in classes if 'brad-form' in c],
                    'base_class': 'brad-form'
                }
            elif 'card' in name:
                component_mapping['card'] = {
                    'classes': [c for c in classes if 'brad-card' in c],
                    'base_class': 'brad-card'
                }
        
        return component_mapping
    
    def detect_smart_pattern(self, component_name: str, figma_data: Dict[str, Any]) -> SmartPattern:
        """
        Detecta padrão usando múltiplas heurísticas inteligentes.
        """
        # 1. Análise do nome
        name_pattern = self._analyze_component_name(component_name)
        
        # 2. Análise estrutural
        structure_pattern = self._analyze_component_structure(figma_data)
        
        # 3. Análise de conteúdo
        content_pattern = self._analyze_component_content(figma_data)
        
        # 4. Combinar evidências
        combined_pattern = self._combine_patterns(name_pattern, structure_pattern, content_pattern)
        
        # 5. Mapear para design system
        ds_mapping = self._map_to_design_system(combined_pattern)
        
        return SmartPattern(
            pattern_type=combined_pattern['type'],
            confidence=combined_pattern['confidence'],
            component_classes=ds_mapping.get('classes', []),
            evidence=combined_pattern['evidence'],
            ds_component=ds_mapping.get('component')
        )
    
    def _analyze_component_name(self, name: str) -> Dict[str, Any]:
        """Analisa o nome do componente para identificar padrões."""
        name_lower = name.lower()
        evidence = []
        confidence = 0.0
        pattern_type = 'unknown'
        
        # Padrões baseados em nome
        name_patterns = {
            'dropdown': ['dropdown', 'select', 'picker', 'chooser'],
            'button': ['button', 'btn', 'action', 'submit'],
            'table': ['table', 'grid', 'list', 'data'],
            'form': ['form', 'input', 'field', 'search'],
            'card': ['card', 'item', 'tile', 'box'],
            'navigation': ['nav', 'menu', 'breadcrumb', 'tab'],
            'modal': ['modal', 'dialog', 'popup', 'overlay'],
            'header': ['header', 'title', 'heading'],
            'footer': ['footer', 'bottom']
        }
        
        for pattern, keywords in name_patterns.items():
            for keyword in keywords:
                if keyword in name_lower:
                    pattern_type = pattern
                    confidence = 0.8  # Alta confiança para nome
                    evidence.append(f"Nome contém '{keyword}'")
                    break
            if pattern_type != 'unknown':
                break
        
        return {
            'type': pattern_type,
            'confidence': confidence,
            'evidence': evidence
        }
    
    def _analyze_component_structure(self, figma_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analisa a estrutura do componente."""
        evidence = []
        confidence = 0.0
        pattern_type = 'unknown'
        
        # Obter nós do figma
        figma_nodes = figma_data.get('figma_nodes', [])
        if not figma_nodes:
            return {'type': pattern_type, 'confidence': confidence, 'evidence': evidence}
        
        # Analisar primeiro nó
        main_node = figma_nodes[0]
        children = main_node.get('children', [])
        
        # Análise baseada em quantidade de filhos e tipos
        if len(children) >= 3:
            # Verificar se parece tabela (múltiplas linhas similares)
            if self._looks_like_table_structure(children):
                pattern_type = 'table'
                confidence = 0.7
                evidence.append(f"Estrutura tabular com {len(children)} linhas")
            
            # Verificar se parece lista
            elif self._looks_like_list_structure(children):
                pattern_type = 'list'
                confidence = 0.6
                evidence.append(f"Lista com {len(children)} itens")
        
        elif len(children) == 2:
            # Possível dropdown (botão + conteúdo)
            if self._looks_like_dropdown_structure(children):
                pattern_type = 'dropdown'
                confidence = 0.6
                evidence.append("Estrutura de dropdown (trigger + content)")
        
        elif len(children) == 1:
            # Possível botão ou item simples
            single_child = children[0]
            if single_child.get('type') == 'TEXT':
                pattern_type = 'button'
                confidence = 0.4
                evidence.append("Item simples com texto")
        
        return {
            'type': pattern_type,
            'confidence': confidence,
            'evidence': evidence
        }
    
    def _analyze_component_content(self, figma_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analisa o conteúdo textual para identificar padrões."""
        evidence = []
        confidence = 0.0
        pattern_type = 'unknown'
        
        # Extrair todos os textos
        all_texts = self._extract_all_texts_from_figma(figma_data)
        all_text_content = ' '.join(all_texts).lower()
        
        # Padrões de conteúdo
        content_patterns = {
            'dropdown': ['selecionar', 'escolha', 'opcoes', 'dropdown', 'chevron', 'seta'],
            'button': ['clique', 'enviar', 'confirmar', 'cancelar', 'acao'],
            'table': ['tabela', 'dados', 'linha', 'coluna', 'header'],
            'form': ['formulario', 'input', 'campo', 'busca', 'pesquisa'],
            'navigation': ['navegacao', 'menu', 'home', 'voltar'],
            'contact': ['telefone', 'fone', 'contato', 'email'],
            'financial': ['financeiro', 'valor', 'moeda', 'rs', '$']
        }
        
        for pattern, keywords in content_patterns.items():
            matches = [kw for kw in keywords if kw in all_text_content]
            if matches:
                pattern_type = pattern
                confidence = min(0.5 + len(matches) * 0.1, 0.8)
                evidence.append(f"Conteúdo sugere {pattern}: {matches}")
                break
        
        return {
            'type': pattern_type,
            'confidence': confidence,
            'evidence': evidence
        }
    
    def _combine_patterns(self, name_analysis: Dict, structure_analysis: Dict, content_analysis: Dict) -> Dict[str, Any]:
        """Combina análises para determinar padrão final."""
        analyses = [name_analysis, structure_analysis, content_analysis]
        
        # Priorizar análise de nome se tiver alta confiança
        if name_analysis['confidence'] >= 0.7:
            return {
                'type': name_analysis['type'],
                'confidence': name_analysis['confidence'],
                'evidence': name_analysis['evidence'] + structure_analysis['evidence'] + content_analysis['evidence']
            }
        
        # Buscar consenso entre análises
        pattern_votes = {}
        evidence_combined = []
        
        for analysis in analyses:
            if analysis['type'] != 'unknown':
                pattern_votes[analysis['type']] = pattern_votes.get(analysis['type'], 0) + analysis['confidence']
                evidence_combined.extend(analysis['evidence'])
        
        if pattern_votes:
            best_pattern = max(pattern_votes.items(), key=lambda x: x[1])
            return {
                'type': best_pattern[0],
                'confidence': min(best_pattern[1], 0.9),
                'evidence': evidence_combined
            }
        
        return {
            'type': 'generic',
            'confidence': 0.3,
            'evidence': ['Padrão não identificado claramente']
        }
    
    def _map_to_design_system(self, pattern: Dict[str, Any]) -> Dict[str, Any]:
        """Mapeia padrão detectado para componentes do design system."""
        pattern_type = pattern['type']
        
        # Mapeamento padrão
        ds_mapping = {
            'dropdown': {
                'component': 'brad-dropdown',
                'classes': ['brad-dropdown', 'brad-dropdown--down-center']
            },
            'button': {
                'component': 'brad-btn',
                'classes': ['brad-btn']
            },
            'table': {
                'component': 'brad-table',
                'classes': ['brad-table']
            },
            'form': {
                'component': 'brad-form',
                'classes': ['brad-form']
            },
            'card': {
                'component': 'brad-card',
                'classes': ['brad-card']
            },
            'list': {
                'component': 'brad-list',
                'classes': ['brad-list']
            },
            'navigation': {
                'component': 'brad-nav',
                'classes': ['brad-nav']
            },
            'contact': {
                'component': 'brad-table',  # Informações de contato frequentemente em tabela
                'classes': ['brad-table', 'contact-info']
            },
            'financial': {
                'component': 'brad-table',
                'classes': ['brad-table', 'financial-data']
            }
        }
        
        # Tentar usar dados do storybook se disponível
        storybook_mapping = self.ds_mapping.get('storybook', {})
        if pattern_type in storybook_mapping:
            return {
                'component': storybook_mapping[pattern_type]['base_class'],
                'classes': storybook_mapping[pattern_type]['classes'][:3]  # Limitar classes
            }
        
        return ds_mapping.get(pattern_type, {
            'component': 'generic',
            'classes': []
        })
    
    # Métodos auxiliares para análise estrutural
    def _looks_like_table_structure(self, children: List[Dict]) -> bool:
        """Verifica se a estrutura parece uma tabela."""
        if len(children) < 2:
            return False
        
        # Verificar se há padrão similar entre filhos
        text_counts = []
        for child in children[:3]:
            text_count = self._count_text_nodes_in_node(child)
            text_counts.append(text_count)
        
        # Se há padrão similar de textos, provavelmente é tabela
        unique_counts = set(text_counts)
        return len(unique_counts) <= 2 and max(text_counts) > 1
    
    def _looks_like_list_structure(self, children: List[Dict]) -> bool:
        """Verifica se a estrutura parece uma lista."""
        if len(children) < 2:
            return False
        
        # Listas tendem a ter itens com estrutura similar mas menos complexa que tabelas
        for child in children[:3]:
            if self._count_text_nodes_in_node(child) > 3:
                return False  # Muito complexo para lista simples
        
        return True
    
    def _looks_like_dropdown_structure(self, children: List[Dict]) -> bool:
        """Verifica se a estrutura parece um dropdown."""
        if len(children) != 2:
            return False
        
        # Dropdown típico: trigger (botão/texto) + content (lista/opções)
        first_texts = self._count_text_nodes_in_node(children[0])
        second_texts = self._count_text_nodes_in_node(children[1])
        
        # Primeiro deve ser simples (trigger), segundo mais complexo (content)
        return first_texts <= 2 and second_texts > first_texts
    
    def _count_text_nodes_in_node(self, node: Dict) -> int:
        """Conta nós de texto em um nó."""
        count = 0
        if node.get('type') == 'TEXT':
            count = 1
        
        for child in node.get('children', []):
            count += self._count_text_nodes_in_node(child)
        
        return count
    
    def _extract_all_texts_from_figma(self, figma_data: Dict[str, Any]) -> List[str]:
        """Extrai todos os textos dos dados do Figma."""
        texts = []
        
        def extract_from_node(node):
            if node.get('type') == 'TEXT':
                text = node.get('characters', '').strip()
                if text:
                    texts.append(text)
            
            for child in node.get('children', []):
                extract_from_node(child)
        
        # Processar nós do figma
        figma_nodes = figma_data.get('figma_nodes', [])
        for node in figma_nodes:
            extract_from_node(node)
        
        return texts
