"""
Analisador estrutural para componentes Figma.
Detecta padrões como tabelas, listas, grids, formulários, etc.
"""

from typing import Dict, List, Any, Optional
import re
from collections import Counter, defaultdict

class StructuralAnalyzer:
    """Analisa a estrutura de componentes Figma para detectar padrões."""
    
    def __init__(self):
        self.table_keywords = [
            "table", "header", "cell", "row", "column", "grid",
            "cadastro", "lista", "diretor", "dados"
        ]
        self.button_keywords = [
            "button", "btn", "botão", "cadastrar", "editar", "excluir", 
            "salvar", "cancelar", "action", "cta"
        ]
        self.navigation_keywords = [
            "pagination", "paginação", "page", "next", "previous", 
            "anterior", "próximo", "bullets"
        ]
        
    def analyze_component_structure(self, figma_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analisa a estrutura completa do componente e detecta padrões.
        """
        analysis = {
            "component_type": "unknown",
            "detected_patterns": [],
            "structure_insights": {},
            "content_analysis": {},
            "interaction_elements": []
        }
        
        # Extrair todos os nós do componente
        all_nodes = self._extract_all_nodes(figma_data)
        
        # Detectar padrões estruturais
        analysis["detected_patterns"] = self._detect_patterns(all_nodes)
        
        # Analisar conteúdo
        analysis["content_analysis"] = self._analyze_content(all_nodes)
        
        # Detectar elementos interativos
        analysis["interaction_elements"] = self._detect_interactive_elements(all_nodes)
        
        # Determinar tipo principal do componente
        analysis["component_type"] = self._determine_component_type(analysis)
        
        # Insights estruturais específicos
        analysis["structure_insights"] = self._generate_structure_insights(
            all_nodes, analysis["detected_patterns"]
        )
        
        return analysis
    
    def _extract_all_nodes(self, figma_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extrai todos os nós recursivamente da estrutura Figma."""
        all_nodes = []
        
        def extract_recursive(nodes):
            for node in nodes:
                all_nodes.append(node)
                if node.get("children"):
                    extract_recursive(node["children"])
        
        figma_nodes = figma_data.get("figma_nodes", [])
        if figma_nodes:
            extract_recursive(figma_nodes)
            
        return all_nodes
    
    def _detect_patterns(self, nodes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detecta padrões estruturais nos nós."""
        patterns = []
        
        # Detectar tabela
        table_pattern = self._detect_table_pattern(nodes)
        if table_pattern:
            patterns.append(table_pattern)
        
        # Detectar navegação/paginação
        nav_pattern = self._detect_navigation_pattern(nodes)
        if nav_pattern:
            patterns.append(nav_pattern)
        
        # Detectar formulário
        form_pattern = self._detect_form_pattern(nodes)
        if form_pattern:
            patterns.append(form_pattern)
        
        # Detectar lista repetitiva
        list_pattern = self._detect_list_pattern(nodes)
        if list_pattern:
            patterns.append(list_pattern)
            
        return patterns
    
    def _detect_table_pattern(self, nodes: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Detecta se o componente representa uma tabela."""
        # Contar elementos relacionados a tabela
        table_elements = {
            "headers": [],
            "cells": [],
            "rows": [],
            "actions": []
        }
        
        for node in nodes:
            name = node.get("name", "").lower()
            node_type = node.get("type", "")
            
            # Detectar headers
            if any(keyword in name for keyword in ["header", "cabeçalho"]):
                table_elements["headers"].append(node)
            
            # Detectar células
            elif any(keyword in name for keyword in ["cell", "célula", "static"]):
                table_elements["cells"].append(node)
            
            # Detectar linhas
            elif any(keyword in name for keyword in ["row", "linha"]) and node_type == "FRAME":
                table_elements["rows"].append(node)
            
            # Detectar ações (editar, excluir)
            elif any(keyword in name for keyword in ["edit", "delete", "editar", "excluir", "action"]):
                table_elements["actions"].append(node)
        
        # Heurística para determinar se é uma tabela
        has_headers = len(table_elements["headers"]) >= 2
        has_cells = len(table_elements["cells"]) >= 4
        has_actions = len(table_elements["actions"]) >= 2
        
        # Detectar padrão de repetição (múltiplas linhas similares)
        repetition_score = self._calculate_repetition_score(nodes)
        
        if (has_headers and has_cells) or (has_actions and repetition_score > 3):
            return {
                "type": "table",
                "confidence": 0.9 if has_headers and has_cells and has_actions else 0.7,
                "elements": table_elements,
                "estimated_columns": len(table_elements["headers"]) or self._estimate_columns(nodes),
                "estimated_rows": repetition_score,
                "has_actions": has_actions,
                "has_pagination": self._has_pagination_elements(nodes)
            }
        
        return None
    
    def _detect_navigation_pattern(self, nodes: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Detecta elementos de navegação/paginação."""
        nav_elements = []
        
        for node in nodes:
            name = node.get("name", "").lower()
            
            if any(keyword in name for keyword in self.navigation_keywords):
                nav_elements.append(node)
        
        if len(nav_elements) >= 2:
            return {
                "type": "navigation",
                "confidence": 0.8,
                "elements": nav_elements,
                "nav_type": "pagination" if any("page" in n.get("name", "").lower() 
                                              for n in nav_elements) else "navigation"
            }
        
        return None
    
    def _detect_form_pattern(self, nodes: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Detecta padrões de formulário."""
        form_elements = {
            "inputs": [],
            "labels": [],
            "buttons": []
        }
        
        for node in nodes:
            name = node.get("name", "").lower()
            node_type = node.get("type", "")
            
            if node_type == "TEXT" and any(keyword in name for keyword in ["label", "campo"]):
                form_elements["labels"].append(node)
            elif "input" in name or "campo" in name:
                form_elements["inputs"].append(node)
            elif any(keyword in name for keyword in self.button_keywords):
                form_elements["buttons"].append(node)
        
        total_form_elements = sum(len(elements) for elements in form_elements.values())
        
        if total_form_elements >= 3:
            return {
                "type": "form",
                "confidence": 0.7,
                "elements": form_elements
            }
        
        return None
    
    def _detect_list_pattern(self, nodes: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Detecta padrões de lista repetitiva."""
        # Agrupar nós por similaridade de nome
        similar_groups = defaultdict(list)
        
        for node in nodes:
            # Extrair padrão base do nome (remover números, índices)
            base_name = re.sub(r'\d+', '', node.get("name", "")).strip()
            if base_name:
                similar_groups[base_name].append(node)
        
        # Encontrar grupos com repetições significativas
        repetitive_groups = {k: v for k, v in similar_groups.items() if len(v) >= 3}
        
        if repetitive_groups:
            return {
                "type": "list",
                "confidence": 0.6,
                "repetitive_groups": repetitive_groups,
                "max_repetitions": max(len(group) for group in repetitive_groups.values())
            }
        
        return None
    
    def _calculate_repetition_score(self, nodes: List[Dict[str, Any]]) -> int:
        """Calcula pontuação de repetição baseada em nomes similares."""
        name_counts = Counter()
        
        for node in nodes:
            # Normalizar nome removendo números e caracteres especiais
            base_name = re.sub(r'[\d\-_\s]+', '', node.get("name", "")).lower()
            if len(base_name) > 2:  # Ignorar nomes muito curtos
                name_counts[base_name] += 1
        
        # Retornar a maior contagem de repetição
        return max(name_counts.values()) if name_counts else 0
    
    def _estimate_columns(self, nodes: List[Dict[str, Any]]) -> int:
        """Estima número de colunas baseado na estrutura."""
        # Procurar por nós no mesmo nível horizontal
        x_positions = []
        
        for node in nodes:
            bbox = node.get("absolute_bounding_box", {})
            if bbox and bbox.get("x") is not None:
                x_positions.append(bbox["x"])
        
        if x_positions:
            # Agrupar posições X similares (tolerância de 10px)
            unique_x = []
            for x in sorted(set(x_positions)):
                if not unique_x or abs(x - unique_x[-1]) > 10:
                    unique_x.append(x)
            
            return min(len(unique_x), 10)  # Máximo de 10 colunas
        
        return 1
    
    def _has_pagination_elements(self, nodes: List[Dict[str, Any]]) -> bool:
        """Verifica se há elementos de paginação."""
        for node in nodes:
            name = node.get("name", "").lower()
            if any(keyword in name for keyword in self.navigation_keywords):
                return True
        return False
    
    def _analyze_content(self, nodes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analisa o conteúdo textual dos nós."""
        content_analysis = {
            "text_elements": [],
            "common_words": [],
            "content_types": []
        }
        
        all_text = []
        
        for node in nodes:
            if node.get("characters"):
                text = node.get("characters", "").lower()
                content_analysis["text_elements"].append({
                    "text": text,
                    "node_name": node.get("name", ""),
                    "length": len(text)
                })
                all_text.extend(text.split())
        
        # Análise de palavras comuns
        if all_text:
            word_counts = Counter(all_text)
            content_analysis["common_words"] = word_counts.most_common(10)
        
        return content_analysis
    
    def _detect_interactive_elements(self, nodes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detecta elementos interativos (botões, links, etc)."""
        interactive_elements = []
        
        for node in nodes:
            name = node.get("name", "").lower()
            node_type = node.get("type", "")
            
            # Detectar botões
            if (any(keyword in name for keyword in self.button_keywords) or 
                node_type == "INSTANCE" and "button" in name):
                
                interactive_elements.append({
                    "type": "button",
                    "name": node.get("name", ""),
                    "figma_type": node_type,
                    "action_type": self._determine_action_type(name)
                })
            
            # Detectar links
            elif "link" in name or "href" in name:
                interactive_elements.append({
                    "type": "link",
                    "name": node.get("name", ""),
                    "figma_type": node_type
                })
        
        return interactive_elements
    
    def _determine_action_type(self, name: str) -> str:
        """Determina o tipo de ação baseado no nome."""
        name = name.lower()
        
        if any(keyword in name for keyword in ["edit", "editar"]):
            return "edit"
        elif any(keyword in name for keyword in ["delete", "excluir", "remover"]):
            return "delete"
        elif any(keyword in name for keyword in ["add", "cadastrar", "novo", "criar"]):
            return "create"
        elif any(keyword in name for keyword in ["save", "salvar"]):
            return "save"
        elif any(keyword in name for keyword in ["cancel", "cancelar"]):
            return "cancel"
        else:
            return "action"
    
    def _determine_component_type(self, analysis: Dict[str, Any]) -> str:
        """Determina o tipo principal do componente baseado na análise."""
        patterns = analysis.get("detected_patterns", [])
        
        # Priorizar baseado na confiança dos padrões
        pattern_priorities = {
            "table": 3,
            "form": 2,
            "navigation": 1,
            "list": 1
        }
        
        best_pattern = None
        best_score = 0
        
        for pattern in patterns:
            pattern_type = pattern.get("type", "")
            confidence = pattern.get("confidence", 0)
            priority = pattern_priorities.get(pattern_type, 0)
            
            score = confidence * priority
            
            if score > best_score:
                best_score = score
                best_pattern = pattern_type
        
        return best_pattern or "component"
    
    def _generate_structure_insights(self, nodes: List[Dict[str, Any]], 
                                   patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Gera insights estruturais específicos para o tipo de componente."""
        insights = {
            "total_elements": len(nodes),
            "complexity_score": self._calculate_complexity_score(nodes),
            "layout_hints": [],
            "recommended_structure": {}
        }
        
        # Gerar insights específicos para cada padrão detectado
        for pattern in patterns:
            pattern_type = pattern.get("type", "")
            
            if pattern_type == "table":
                insights["recommended_structure"]["table"] = {
                    "component_type": "data-table",
                    "should_use_ngFor": True,
                    "needs_pagination": pattern.get("has_pagination", False),
                    "action_buttons": pattern.get("has_actions", False),
                    "estimated_data_structure": self._suggest_data_structure(pattern)
                }
            
            elif pattern_type == "navigation":
                insights["recommended_structure"]["navigation"] = {
                    "component_type": "pagination",
                    "nav_type": pattern.get("nav_type", "navigation")
                }
            
            elif pattern_type == "form":
                insights["recommended_structure"]["form"] = {
                    "component_type": "reactive-form",
                    "form_elements": len(pattern.get("elements", {}).get("inputs", []))
                }
        
        return insights
    
    def _calculate_complexity_score(self, nodes: List[Dict[str, Any]]) -> float:
        """Calcula pontuação de complexidade do componente."""
        # Fatores de complexidade
        total_nodes = len(nodes)
        interactive_elements = len([n for n in nodes if any(keyword in n.get("name", "").lower() 
                                   for keyword in self.button_keywords)])
        text_elements = len([n for n in nodes if n.get("characters")])
        
        # Fórmula de complexidade
        complexity = (
            (total_nodes * 0.1) +
            (interactive_elements * 0.5) +
            (text_elements * 0.2)
        )
        
        return min(complexity, 10.0)  # Máximo 10
    
    def _suggest_data_structure(self, table_pattern: Dict[str, Any]) -> Dict[str, Any]:
        """Sugere estrutura de dados para tabelas."""
        columns = table_pattern.get("estimated_columns", 1)
        has_actions = table_pattern.get("has_actions", False)
        
        # Estrutura básica sugerida
        structure = {
            "interface_name": "TableData",
            "properties": []
        }
        
        # Gerar propriedades baseadas no número de colunas
        base_properties = ["id", "name", "status", "date", "value", "description"]
        
        for i in range(min(columns, len(base_properties))):
            structure["properties"].append({
                "name": base_properties[i],
                "type": "string",
                "required": i < 2  # Primeiras 2 propriedades são obrigatórias
            })
        
        if has_actions:
            structure["needs_actions"] = True
            structure["action_methods"] = ["onEdit", "onDelete", "onView"]
        
        return structure
