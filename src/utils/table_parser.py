"""
Parser inteligente de tabelas que separa corretamente cabeçalhos de dados
baseado na análise estrutural do Figma.
"""

import re
from typing import List, Dict, Any, Tuple
from collections import Counter

class IntelligentTableParser:
    """
    Parser que usa análise estrutural e estatística para identificar
    cabeçalhos e dados em componentes de tabela do Figma.
    """
    
    def __init__(self):
        # Padrões que indicam dados (não cabeçalhos)
        self.data_patterns = {
            'date': r'\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}',
            'placeholder': r'^(0{2,}\/0{2,}\/0{4,}|x{3,}|\.{3,}|#{3,})$',
            'number': r'^\d+([.,]\d+)?$',
            'currency': r'^[R$€£¥$]\s*\d+([.,]\d{2})?$',
            'repeated_name': r'^Nome\s+\w+$',
        }
        
        # Palavras que indicam ações (típicas de cabeçalhos de ação)
        self.action_keywords = ['edit', 'delete', 'remove', 'view', 'action', 'ação', 'editar', 'excluir', 'ver']
    
    def parse_table_structure(self, texts: List[Dict[str, Any]], figma_nodes: List[Dict]) -> Tuple[List[str], List[List[str]]]:
        """
        Analisa a estrutura da tabela e separa cabeçalhos de dados.
        
        Returns:
            Tuple[headers, data_rows]
        """
        # 1. Analisar estrutura hierárquica
        structure_analysis = self._analyze_table_structure(figma_nodes)
        
        # 2. Classificar textos por posição e conteúdo
        classified_texts = self._classify_texts(texts, structure_analysis)
        
        # 3. Identificar cabeçalhos vs dados
        headers = self._extract_headers(classified_texts)
        
        # 4. Organizar dados em linhas
        data_rows = self._extract_data_rows(classified_texts, len(headers))
        
        return headers, data_rows
    
    def _analyze_table_structure(self, figma_nodes: List[Dict]) -> Dict[str, Any]:
        """Analisa a estrutura hierárquica do componente de tabela."""
        structure = {
            'header_section': [],
            'data_sections': [],
            'action_sections': [],
            'total_rows': 0,
            'estimated_columns': 0
        }
        
        def analyze_node(node, level=0):
            node_name = node.get('name', '').lower()
            node_type = node.get('type', '')
            
            # Identificar seções de cabeçalho
            if any(keyword in node_name for keyword in ['header', 'thead', 'th', 'cabeçalho']):
                structure['header_section'].append(node)
            
            # Identificar seções de dados repetidas (frames numerados)
            elif re.match(r'frame\s*\d+', node_name) or re.match(r'row\s*\d+', node_name):
                structure['data_sections'].append(node)
                structure['total_rows'] += 1
            
            # Identificar elementos de ação
            elif any(keyword in node_name for keyword in ['button', 'action', 'btn']):
                structure['action_sections'].append(node)
            
            # Recursivo
            for child in node.get('children', []):
                analyze_node(child, level + 1)
        
        for node in figma_nodes:
            analyze_node(node)
        
        # Estimar número de colunas baseado na estrutura
        if structure['data_sections']:
            # Contar elementos filhos de uma linha típica
            sample_row = structure['data_sections'][0]
            structure['estimated_columns'] = self._count_cell_children(sample_row)
        
        return structure
    
    def _count_cell_children(self, row_node: Dict) -> int:
        """Conta o número de células em uma linha."""
        cells = 0
        
        def count_cells(node):
            nonlocal cells
            node_name = node.get('name', '').lower()
            
            # Se é uma célula ou contém texto
            if ('cell' in node_name or 
                node.get('type') == 'TEXT' or 
                any(child.get('type') == 'TEXT' for child in node.get('children', []))):
                cells += 1
            else:
                # Recursivo se não é célula direta
                for child in node.get('children', []):
                    count_cells(child)
        
        count_cells(row_node)
        return max(cells, 1)  # Mínimo 1 célula
    
    def _classify_texts(self, texts: List[Dict], structure: Dict) -> Dict[str, List[Dict]]:
        """Classifica textos em categorias baseado no conteúdo e posição."""
        classified = {
            'likely_headers': [],
            'likely_data': [],
            'action_labels': [],
            'metadata': []
        }
        
        for text_info in texts:
            text = text_info['content'].strip()
            name = text_info.get('name', '').lower()
            level = text_info.get('level', 0)
            
            # Classificar por conteúdo
            if self._is_data_pattern(text):
                classified['likely_data'].append(text_info)
            elif self._is_action_label(text):
                classified['action_labels'].append(text_info)
            elif self._is_metadata(text):
                classified['metadata'].append(text_info)
            else:
                # Possível cabeçalho
                classified['likely_headers'].append(text_info)
        
        return classified
    
    def _is_data_pattern(self, text: str) -> bool:
        """Verifica se o texto parece ser dado (não cabeçalho)."""
        for pattern_regex in self.data_patterns.values():
            if re.match(pattern_regex, text, re.IGNORECASE):
                return True
        
        # Textos muito repetitivos são dados
        if text.lower() in ['nome diretor', 'label', 'valor', 'item']:
            return True
        
        return False
    
    def _is_action_label(self, text: str) -> bool:
        """Verifica se é um label de ação."""
        return any(keyword in text.lower() for keyword in self.action_keywords)
    
    def _is_metadata(self, text: str) -> bool:
        """Verifica se é metadado (não faz parte da tabela principal)."""
        metadata_patterns = [
            r'\d+\s*iten[s]?\s*selecionado[s]?',
            r'filtro',
            r'title\s*$',
            r'^empresas\s+(originadoras|ocultas)',
        ]
        
        for pattern in metadata_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        
        return False
    
    def _extract_headers(self, classified_texts: Dict[str, List[Dict]]) -> List[str]:
        """Extrai cabeçalhos da classificação de textos."""
        headers = []
        
        # Priorizar cabeçalhos identificados
        potential_headers = classified_texts['likely_headers']
        
        # Filtrar cabeçalhos que fazem sentido para tabela
        valid_headers = []
        for text_info in potential_headers:
            text = text_info['content']
            
            # Pular metadados
            if self._is_metadata(text):
                continue
            
            # Pular textos muito genéricos
            if text.lower() in ['title', 'label', '']:
                continue
            
            # Cabeçalhos típicos de tabela
            if (len(text.split()) <= 3 and  # Máximo 3 palavras
                len(text) >= 2 and  # Mínimo 2 caracteres
                not self._is_data_pattern(text)):
                valid_headers.append(text)
        
        # Adicionar labels de ação
        for text_info in classified_texts['action_labels']:
            valid_headers.append(text_info['content'])
        
        # Se não encontrou cabeçalhos suficientes, usar heurística
        if len(valid_headers) < 3:
            # Procurar por textos únicos que não são dados
            all_texts = [t['content'] for t in classified_texts['likely_headers'] + classified_texts['action_labels']]
            text_counts = Counter(all_texts)
            
            # Textos que aparecem poucas vezes podem ser cabeçalhos
            for text, count in text_counts.items():
                if (count <= 2 and 
                    text not in valid_headers and 
                    not self._is_metadata(text) and
                    not self._is_data_pattern(text)):
                    valid_headers.append(text)
        
        # Limitar a número razoável de colunas
        return valid_headers[:8]  # Máximo 8 colunas
    
    def _extract_data_rows(self, classified_texts: Dict[str, List[Dict]], num_columns: int) -> List[List[str]]:
        """Extrai linhas de dados baseado no número de colunas."""
        data_rows = []
        
        # Coletar todos os textos de dados
        data_texts = [t['content'] for t in classified_texts['likely_data']]
        
        if not data_texts:
            # Se não encontrou dados específicos, gerar dados de exemplo
            sample_data = self._generate_sample_data(num_columns)
            return sample_data
        
        # Agrupar dados em linhas
        current_row = []
        for text in data_texts:
            current_row.append(text)
            
            if len(current_row) >= num_columns:
                data_rows.append(current_row[:num_columns])
                current_row = []
        
        # Adicionar linha incompleta se houver
        if current_row:
            # Completar linha com dados padrão
            while len(current_row) < num_columns:
                current_row.append("--")
            data_rows.append(current_row)
        
        # Garantir pelo menos algumas linhas de exemplo
        while len(data_rows) < 3:
            sample_row = self._generate_sample_row(num_columns, len(data_rows))
            data_rows.append(sample_row)
        
        return data_rows[:10]  # Máximo 10 linhas de exemplo
    
    def _generate_sample_data(self, num_columns: int) -> List[List[str]]:
        """Gera dados de exemplo quando não consegue extrair dados reais."""
        sample_data = []
        
        for i in range(3):  # 3 linhas de exemplo
            row = []
            for j in range(num_columns):
                if j == 0:
                    row.append(f"Item {i+1}")
                elif j == num_columns - 1:  # Última coluna pode ser ação
                    row.append("Ação")
                else:
                    row.append(f"Valor {i+1}.{j+1}")
            sample_data.append(row)
        
        return sample_data
    
    def _generate_sample_row(self, num_columns: int, row_index: int) -> List[str]:
        """Gera uma linha de exemplo."""
        row = []
        for j in range(num_columns):
            if j == 0:
                row.append(f"Item {row_index + 1}")
            elif j == num_columns - 1:
                row.append("--")
            else:
                row.append(f"Valor {row_index + 1}.{j + 1}")
        return row

def main():
    """Teste do parser inteligente de tabelas."""
    import json
    
    # Testar com dados reais
    table_file = "data/figma_discovery/sat-contabilidade/base/components/table.json"
    
    try:
        with open(table_file, 'r', encoding='utf-8') as f:
            figma_data = json.load(f)
        
        # Extrair textos
        texts = []
        def extract_texts(node, level=0):
            if node.get('type') == 'TEXT':
                text_content = node.get('characters', '').strip()
                if text_content:
                    texts.append({
                        'content': text_content,
                        'name': node.get('name', ''),
                        'level': level
                    })
            
            for child in node.get('children', []):
                extract_texts(child, level + 1)
        
        for figma_node in figma_data.get('figma_nodes', []):
            extract_texts(figma_node)
        
        # Usar parser inteligente
        parser = IntelligentTableParser()
        headers, data_rows = parser.parse_table_structure(texts, figma_data.get('figma_nodes', []))
        
        print("🔍 PARSER INTELIGENTE DE TABELAS")
        print("=" * 50)
        print(f"📊 Cabeçalhos identificados ({len(headers)}):")
        for i, header in enumerate(headers):
            print(f"  {i+1}. {header}")
        
        print(f"\n📋 Linhas de dados ({len(data_rows)}):")
        for i, row in enumerate(data_rows[:3]):  # Mostrar apenas 3 primeiras
            print(f"  Linha {i+1}: {row}")
        
        print("\n💻 HTML GERADO:")
        print(generate_refined_table_html(headers, data_rows))
    
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

def generate_refined_table_html(headers: List[str], data_rows: List[List[str]]) -> str:
    """Gera HTML refinado da tabela."""
    html = '''<div class="table-container">
  <table class="data-table">
    <thead>
      <tr>'''
    
    for header in headers:
        html += f'\n        <th>{header}</th>'
    
    html += '''
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let row of tableData; let i = index">'''
    
    for i, header in enumerate(headers):
        if any(keyword in header.lower() for keyword in ['edit', 'delete', 'action', 'editar', 'excluir']):
            html += f'\n        <td><button (click)="{header.lower()}Action(i)">{header}</button></td>'
        else:
            html += f'\n        <td>{{{{ row.col{i} }}</td>'
    
    html += '''
      </tr>
    </tbody>
  </table>
</div>'''
    
    return html

if __name__ == "__main__":
    main()
